import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { LabelFormatter } from "../../../Utils/functions";
import React, { useRef } from "react";
import StockPurchase from "../CustomViews/StockPurchase";

const stock_purchasing = {
  CustomView: (data) => <StockPurchase {...data} />,
  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        menus={[
          {
            key: "Make Payment",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Close",
                    submitText: "Save",
                  },
                }}
                modalProps={{ centered: true }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Make Payment
                  </a>
                }
                title={"Make Payment"}
                destroyOnClose={true}
                layoutType="ModalForm"
                initialValues={{
                  invoice: {
                    label: `${record.supplier && record.supplier.label} - ${record._id
                      }`,
                    ...record,
                  },
                }}
                onFinish={async (values) => {
                  await pouchDatabase(
                    modules.stock_payments.collection,
                    databasePrefix
                  ).saveDocument(values, CRUD_USER);
                  return true;
                }}
                columns={[...modules.stock_payments.columns]}
              />
            ),
          },
        ]}
      ></TableDropdown>
    );
  },
};

export default stock_purchasing;
