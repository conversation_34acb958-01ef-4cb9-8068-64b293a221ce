exports.customers = {
  name: "Customers",
  icon: "UsergroupAddOutlined",
  path: "/partners/customers",
  parent: "partners",
  collection: "customers",
  singular: "Customer",
  multi_Branch: true,
  columns: [
    {
      title: "Name",
      dataIndex: "name",
      sorter: true,
      valueType: "text",
      isRequired: true,
    },
    {
      title: "Email",
      dataIndex: "email",
      copyable: true,
      type: "email",
    },
    {
      title: "Phone",
      dataIndex: "phone",
      sorter: true,
      type: "phone",
      isRequired: true,
    },
    {
      title: "Mobile",
      dataIndex: "mobile",
      sorter: true,
      type: "phone",
    },
    {
      title: "Address",
      dataIndex: "address",
      sorter: true,
      valueType: "textArea",
      isRequired: true,
    },
    {
      title: "Tin Number",
      dataIndex: "tin_number",
      sorter: true,
      valueType: "text",
    },
  ],
};
