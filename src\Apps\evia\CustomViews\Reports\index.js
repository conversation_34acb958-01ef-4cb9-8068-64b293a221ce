import React, { useEffect, useState } from "react";

import { Tabs } from "antd";
import Arrival from "./Arrival";
import Departure from "./Departure";
import Collection from "./Collection";
import Occupancy from "./Occupancy";
import DailyDeparture from "./DailyDeparture";
import HighBalance from "./HighBalance";
import Expense from "./Expense";
import { buffCheckIns } from "../../modulesProperties/checkins";

const Reports = (props) => {
  const { pouchDatabase, databasePrefix } = props;

  const onChange = (key) => {

  };

  const [checkIns, setCheckIns] = useState(null);
  const [guests, setGuests] = useState(null);
  const [rooms, setApartments] = useState(null);
  const [receipts, setReceipts] = useState(null);
  const [eventsReceipts, setEventsReceipts] = useState(null);
  const [ordersReceipts, setOrdersReceipts] = useState(null);
  const [extensions, setExtensions] = useState(null);
  const [expenses, setExpenses] = useState(null);

  const [company, setCompany] = useState(
    localStorage.getItem("LOCAL_STORAGE_ORGANIZATION")
  );

  useEffect(() => {
    const db = pouchDatabase("organizations", databasePrefix);
    db.getAllData({ include_docs: true }).then((res) => {
      // setCompany({ ...res[0] });
      db.getAttachment(res[0]._id, "logo").then((res2) => {
        setCompany({ ...res[0], orgLogo: res2 });
      });
    });
  }, [company]);

  useEffect(() => {
    pouchDatabase("checkins", databasePrefix)
      .getAllData()
      .then((data) => buffCheckIns(data))
      .then((data) => setCheckIns(data));
  }, []);

  useEffect(() => {
    pouchDatabase("guests", databasePrefix)
      .getAllData()
      .then((data) => setGuests(data));
  }, []);

  useEffect(() => {
    pouchDatabase("rooms", databasePrefix)
      .getAllData()
      .then((data) => setApartments(data));
  }, []);

  useEffect(() => {
    pouchDatabase("receipts", databasePrefix)
      .getAllData()
      .then((data) => setReceipts(data));
  }, []);

  useEffect(() => {
    pouchDatabase("events_receipts", databasePrefix)
      .getAllData()
      .then((data) => setEventsReceipts(data));
  }, []);

  useEffect(() => {
    pouchDatabase("order_receipts", databasePrefix)
      .getAllData()
      .then((data) => setOrdersReceipts(data));
  }, []);

  useEffect(() => {
    pouchDatabase("extensions", databasePrefix)
      .getAllData()
      .then((data) => setExtensions(data));
  }, []);

  useEffect(() => {
    pouchDatabase("expenses", databasePrefix)
      .getAllData()
      .then((data) => setExpenses(data));
  }, []);



  const items = [
    {
      key: "3",
      label: `Collection Report`,
      children: receipts && (
        <Collection
          checkIns={checkIns}
          guests={guests}
          rooms={rooms}
          receipts={[
            // ...(receipts
            //   ? receipts.map((r) => ({
            //       ...r,
            //       department: "Rooms",
            //       invoice: r.checkin,
            //     }))
            //   : []),

            ...(ordersReceipts
              ? ordersReceipts.map((r) => ({
                ...r,
                department: "Bar & Restaurant",
                invoice: r.order,
              }))
              : []),
          ]}
          company={company}
        />
      ),
    },

    {
      key: "6",
      label: `Expenses Report`,
      children: expenses && <Expense expense={expenses} company={company} />,
    },

  ];

  return <Tabs defaultActiveKey="1" items={items} onChange={onChange} />;
};

export default Reports;
