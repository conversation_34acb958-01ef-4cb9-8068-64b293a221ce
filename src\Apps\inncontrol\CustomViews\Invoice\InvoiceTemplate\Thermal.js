import React from "react";

import "./thermal.css";
import moment from "moment";
import { numberFormat } from "../../../../../Utils/functions";
import RenderBlob from "../../../../../Components/RenderBlob";
const Thermal = ({ company, data, documentTitle }) => {
  const taxable = data.taxable;
  const Subtoal = data.items.reduce((acc, item) => {
    return acc + item.price * item.quantity;
  }, 0);

  const Tax = taxable ? Subtoal * 0.18 : 0;



  const GrandTotal = Subtoal + Tax;

  const customer = data.client;


  return (
    <>
      <div className="tm_pos_invoice_wrap">
        <div className="tm_pos_invoice_top">
          {company.logo && <RenderBlob blob={company.logo} size={100} />}
          <div className="tm_pos_company_name">{company.name}</div>
          <div className="tm_pos_company_address">{company.address}</div>
          <div className="tm_pos_company_mobile">{company.email}</div>
        </div>
        <div className="tm_pos_invoice_body">
          <div className="tm_pos_invoice_heading">
            <span>
              <strong>{documentTitle}</strong>
            </span>
          </div>
          <table>
            <tr>
              <td>
                {customer ? (
                  <p>
                    <strong style={{ fontSize: "12px" }}>
                      {`${customer.title}. ${customer.sur_name} ${customer.first_name}`}
                    </strong>
                    <br />
                    {customer.phone}{" "}
                    {customer.alternative_phone &&
                      `/ ${customer.alternative_phone}`}{" "}
                    <br />
                    {customer.email && (
                      <>
                        {customer.email} <br />
                      </>
                    )}
                    {customer.address && (
                      <>
                        {customer.address} <br />
                      </>
                    )}
                    {customer._id && (
                      <>
                        Customer ID :
                        <strong>
                          {customer._id} <br />
                        </strong>
                      </>
                    )}
                  </p>
                ) : (
                  <p>
                    {/* {customer&&customer.name&&customer.name} */}
                    Walkin Guest
                    <br />
                  </p>
                )}
              </td>
              <td style={{ textAlign: "right" }}>
                <p style={{ fontSize: "10px" }}>
                  {moment(data.date).format("Do MMM YY")} <br />#{" "}
                  <strong>{data.id}</strong>
                </p>
              </td>
            </tr>
          </table>

          <table className="tm_pos_invoice_table" style={{ fontSize: "10px" }}>
            <thead>
              <tr>
                <th>Item</th>
                <th>Price</th>
                <th>Qty</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              {data.items.map((item) => {
                return (
                  <tr>
                    <td>{item.name}</td>
                    <td>{numberFormat(item.price)}</td>
                    <td>{item.quantity}</td>
                    <td>{numberFormat(item.price * item.quantity)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="tm_bill_list" style={{ fontSize: "10px" }}>
            {/* {Tax > 0 && (
              <div className="tm_bill_list_in">
                <div className="tm_bill_title">Sub-Total:</div>
                <div className="tm_bill_value">{numberFormat(Subtoal)}</div>
              </div>
            )} */}
            {/* <div className="tm_bill_list_in">
              <div className="tm_bill_title">Discount: </div>
              <div className="tm_bill_value">-$150.00</div>
            </div>
            <div className="tm_invoice_seperator"></div>
            <div className="tm_bill_list_in">
              <div className="tm_bill_title">Service charge:</div>
              <div className="tm_bill_value">0.00tk</div>
            </div> */}
            {/* {Tax > 0 && (
              <>
                <div className="tm_bill_list_in">
                  <div className="tm_bill_title">Tax(18%):</div>
                  <div className="tm_bill_value">{numberFormat(Tax)}</div>
                </div>
                <div className="tm_invoice_seperator"></div>
              </>
            )} */}
            <div
              className="tm_bill_list_in"
              style={{ fontSize: "12px", fontWeight: "bold" }}
            >
              <div className="tm_bill_title tm_bill_focus">
                {Tax > 0 ? "Total:" : "Grand Total:"}
              </div>
              <div className="tm_bill_value tm_bill_focus">
                {numberFormat(Subtoal)}
              </div>
            </div>
          </div>
          <div className="tm_pos_sample_text">
            {data.operator.title} :<strong> {data.operator.name}</strong>
          </div>
          <div className="tm_pos_invoice_footer">Thank you, Come again!</div>
        </div>
      </div>
    </>
  );
};

export default Thermal;
