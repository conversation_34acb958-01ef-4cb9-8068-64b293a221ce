import React, { useEffect, useState } from "react";
import Thermal from "./Thermal";
import A4 from "./A4";
import PouchDb from "pouchdb-browser";
import PrintComponents from "react-print-components";
import { FloatButton, Radio } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import packageJson from "../../../../../../package.json";

const appName = packageJson.name;

const InvoiceTemplate = ({ data, documentTitle }) => {
  const [company, setCompany] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [value, setValue] = useState("Thermal Paper");


  
  

  useEffect(() => {
    const CompanyDB = new PouchDb("organizations");
    CompanyDB.allDocs({
      include_docs: true,
      attachments: true,
      binary: true,
    }).then((data) => {
      let comp =
        appName === "mission-control"
          ? data.rows.find((row) => row.doc._id === "LVZ4Q0FR").doc
          : data.rows[0].doc;

      
      if (comp._attachments && comp._attachments.logo) {
        CompanyDB.getAttachment(comp._id, "logo").then((res) => {
          
          comp.logo = res;
          setCompany(comp);
        });
      } else {
        setCompany(comp);
      }
    });
  }, []);

  useEffect(() => {
    const customersDB = new PouchDb("guests");
    
    data &&
    data.client &&
      customersDB
        .get(data.client.name.value || data.client.name._id, { binary: true })
        .then((res) => {
          
          setCustomer(res);
        });
  }, [data]);

  const options = [
    { label: "A4", value: "A4" },
    { label: "Thermal Paper", value: "Thermal Paper" },
  ];

  const onChange = ({ target: { value } }) => {
    
    setValue(value);
  };

  return (
    <>
      {company && customer && (
        <div>
          <PageHeader
            title={documentTitle}
            extra={
              <Radio.Group
                options={options}
                onChange={onChange}
                value={value}
                optionType="button"
                buttonStyle="solid"
              />
            }
            style={{ marginBottom: 24 }}
          ></PageHeader>

          <PrintComponents
            trigger={
              <FloatButton
                icon={<PrinterOutlined />}
                tooltip={<div>Print</div>}
              />
            }
          >
            {value === "Thermal Paper" ? (
              <Thermal
                company={company}
                data={{...data, client: customer}}
                documentTitle={documentTitle}
              />
            ) : (
              <A4
                company={company}
                data={{...data, client: customer}}
                documentTitle={documentTitle}
                taxable={data.taxable}
              />
            )}
          </PrintComponents>

          {value === "Thermal Paper" && (
            <Thermal
              company={company}
              data={{...data, client: customer}}
              documentTitle={documentTitle}
            />
          )}
          {value === "A4" && (
            <A4 company={company} data={{...data, client: customer}} documentTitle={documentTitle} />
          )}
        </div>
      )}
    </>
  );
};

export default InvoiceTemplate;
