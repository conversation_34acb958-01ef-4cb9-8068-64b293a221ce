import React, { useEffect, useState } from "react";
import {
  Button,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Row,
  Col,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";
import "./print.css";
import DocumentHead from "./DocumentHead";

const DailyDeparture = (props) => {
  const [dateRange, setDateRange] = useState([Date(), Date()]);

  const { checkIns, guests, rooms, receipts, extensions, company } = props;

  const buffedCheckIns = checkIns.map((r) => {
    const extension = extensions.find((e) => e.checkin.value === r._id);
    return {
      ...r,
      guest: r.guest && guests.find((g) => g._id === r.guest.value),
      room: rooms.find((a) => a._id === r.room_number && r.room_number.value),
      extensions: extension,
      departure_date: extension ? extension.departure_date : r.departure_date,
      paid: receipts.reduce((acc, c) => {
        if (c && c.checkin && c.checkin.key) {
          if (
            ((c.checkin && c.checkin.key) ||
              c.checkin.value ||
              c.checkin._id) === r._id
          ) {
            
            acc += parseInt(c.amount);
          }
        }
        
        return acc;
      }, 0),
    };
  });

  const columns = [
    {
      title: "Room no",
      key: "checkin",
      dataIndex: "room_number",
      render: (text) => text && text.label.split(" ").reverse().pop(),
    },
    {
      title: "Guest Name",
      dataIndex: "guest",
      key: "guest",
      render: (text) =>
        text && `${text.title} ${text.sur_name} ${text.first_name}`,
    },
    {
      title: "Check in",
      dataIndex: "arrival_date",
      render: (text) => moment(text).format("DD MMM YY - HH:mm"),
    },
    {
      title: "Check out",
      dataIndex: "departure_date",
      render: (text,r) => `${moment(text).format("DD MMM YY")}${
        (moment(r.departure_date).isSameOrBefore(
          moment(),
          "day"
        ) && !r.checked_out)
          ? ` (${moment().startOf("day").diff(moment(r.departure_date).startOf("day"), "days")} Past)`
          : ""
      }`,
    },
    {
      title: "Nights",
      dataIndex: "nights",
      key: "nights",
      render: (t, record) =>
        moment(
          record.extensions
            ? record.extensions.departure_date
            : record.departure_date
        ).startOf("day").diff(moment(record.arrival_date).startOf("day"), "days"),
    },
    {
      title: "Rate",
      dataIndex: "room_rate",
      key: "room_rate",
    },
    {
      title: "Total",
      dataIndex: "total",
      key: "total",
      render: (t, record) => {
        const rate = record.room_rate;
        const nights = moment(
          record.extensions
            ? record.extensions.departure_date
            : record.departure_date
        ).startOf("day").diff(moment(record.arrival_date).startOf("day"), "days");
        return numberFormat(rate * nights);
      },
    },
    
    {
      dataIndex: "paid",
      key: "paid",
      title: "Paid",
      render: (t, record) => numberFormat(record.paid),
    },
    {
      title: "Room Balance",
      dataIndex: "outstanding",
      key: "outstanding",
      render: (t, record) => {
        const rate = record.room_rate;
        const nights = moment(
          record.extensions
            ? record.extensions.departure_date
            : record.departure_date
        ).startOf("day").diff(moment(record.arrival_date).startOf("day"), "days");
        return numberFormat(rate * nights - record.paid);
      },
    },
    {
      title: "G Bill",
      dataIndex: "check_in_total",
      key: "check_in_total",
    },
    {
      title: "G Balance",
      dataIndex: "balance",
      key: "balance",
    },
  ];

  const [targetDate, setTargetDate] = useState(null);

  return (
    <div>
      <Table
        size="small"
        columns={columns}
        dataSource={buffedCheckIns.filter(
          (r) =>
                  r.checked_out &&
                  moment(r.departure_date).isSameOrAfter(
                    moment(dateRange[0].$d),
                    "day"
                  ) &&
                  moment(r.departure_date).isSameOrBefore(
                    moment(dateRange[1].$d),
                    "day"
                  )
        )}
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
            <DatePicker.RangePicker
                initialValue={[moment().startOf("month"), moment()]}
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>
                    Daily Collection Report
                  </Typography.Title>
                  <Table
                    size="small"
                    columns={columns}
                    dataSource={buffedCheckIns.filter(
                      (r) =>
                  r.checked_out &&
                  moment(r.departure_date).isSameOrAfter(
                    moment(dateRange[0].$d),
                    "day"
                  ) &&
                  moment(r.departure_date).isSameOrBefore(
                    moment(dateRange[1].$d),
                    "day"
                  )
                    )}
                    pagination={false}
                    className="custom-table"
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default DailyDeparture;
