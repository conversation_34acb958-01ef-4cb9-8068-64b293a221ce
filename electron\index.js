const { app, BrowserWindow, Menu, ipc<PERSON>ain, shell, nativeImage } = require("electron");
const path = require("path");
const AppTray = require("./AppTray");
const AppUpdater = require("./AppUpdater");
const IPCHandlers = require("./ipcHandlers");
const contextMenu = require("electron-context-menu");
const packageJson = require("../package.json");
// const AutoUpdater = require("./AutoUpdater");
Menu.setApplicationMenu(false);

let tray;

contextMenu({
  showCopyImageAddress: true, // Customize other options as needed
  append: (defaultActions, params, browserWindow) => [
    {
      label: "Reload",
      click: () => {
        // Reload the web page
        browserWindow.reload();
      },
    },
  ],
});

// const ports = {
//   abacus: 1,
//   default: 2,
//   lenkit: 3,
//   kanify: 4,
//   homz: 5,
//   kyeyo: 6,
//   "mission-control": 7,
// };

class ElectronClass {
  constructor(APP_NAME, APP_PATH_NAME, filePath) {
    this.APP_NAME = APP_NAME;
    this.APP_PATH_NAME = APP_PATH_NAME;
    this.filePath = filePath;
    this.port = 0;
    this.isPro = process.env.NODE_ENV === "development" ? false : true;
    this.win = null;
    this.ipcHandlers = null;
    this.appIconPath = null;

    console.log('🔧 ElectronClass initialized with:');
    console.log('  APP_NAME:', APP_NAME);
    console.log('  APP_PATH_NAME:', APP_PATH_NAME);
    console.log('  filePath:', filePath);
    console.log('  isPro:', this.isPro);
    console.log('  platform:', process.platform);

    // PERFORMANCE: Initialize IPC handlers
    this.ipcHandlers = new IPCHandlers();

    if (this.APP_NAME) {
      this.port = 0;
    }

    console.log(
      this.APP_NAME,
      this.port,
      !this.isPro || !this.filePath
        ? "http://localhost:" + (3000 + this.port)
        : this.filePath
    );

    const additionalData = { myKey: "myValue" };
    const gotTheLock = app.requestSingleInstanceLock(additionalData);

    if (!gotTheLock) {
      app.quit();
    } else {
      app.on(
        "second-instance",
        (event, commandLine, workingDirectory, additionalData) => {
          // Print out data received from the second instance.
          console.log(additionalData);
          // Someone tried to run a second instance, we should focus our window.
          if (this.win) {
            if (this.win.isMinimized()) this.win.restore();
            if (this.win.isVisible()) this.win.show();
            this.win.focus();
          }
        }
      );
      app.whenReady().then(() => {
        // Set application icon before creating window
        console.log('🚀 App ready, setting application icon...');
        const iconPath = this.setApplicationIcon();

        // For Windows, also try to set the app icon at the application level
        if (iconPath && process.platform === 'win32') {
          this.setWindowsAppIcon(iconPath);
        }

        this.CreateBrowserWindow();
        app.on("activate", () => {
          if (BrowserWindow.getAllWindows().length === 0) {
            this.CreateBrowserWindow();
          }
        });
      });
    }

    app.setName(this.APP_NAME);

    // Quit when all windows are closed, except on macOS. There, it's common
    // for applications and their menu bar to stay active until the user quits
    // explicitly with Cmd + Q.
    app.on("window-all-closed", () => {
      if (process.platform !== "darwin") {
        app.quit();
      }
    });

    ipcMain.on("app_version", (event) => {
      event.sender.send("app_version", { version: app.getVersion() });
    });
    ipcMain.on("restart_app", () => {
      autoUpdater.quitAndInstall();
    });
  }

  setApplicationIcon = () => {
    // Set application icon for better OS integration (taskbar, dock, etc.)
    console.log(`🔍 Setting application-level icon for app: "${this.APP_PATH_NAME}"`);

    // Platform-specific icon preferences - Windows needs specific sizes and formats
    const platformIconPrefs = {
      'win32': ['win/icon.ico', 'png/256x256.png', 'png/128x128.png', 'png/64x64.png', 'png/32x32.png', 'png/16x16.png'],
      'darwin': ['mac/icon.icns', 'png/128x128.png', 'png/64x64.png'],
      'linux': ['png/64x64.png', 'png/128x128.png', 'png/32x32.png']
    };

    const currentPlatformPrefs = platformIconPrefs[process.platform] || platformIconPrefs['linux'];

    // Build comprehensive icon paths for any app
    const iconPaths = [];

    // For each platform preference, try all possible locations
    currentPlatformPrefs.forEach(iconFile => {
      // Try electron/icons directory first (where initApp.js copies icons)
      iconPaths.push(path.join(__dirname, `./icons/${this.APP_PATH_NAME}/${iconFile}`));

      // Try extraResources directory (for packaged app) - only if resourcesPath exists
      if (process.resourcesPath) {
        iconPaths.push(path.join(process.resourcesPath, `icons/${this.APP_PATH_NAME}/${iconFile}`));
      }

      // Try main icons directory
      iconPaths.push(path.join(__dirname, `../icons/${this.APP_PATH_NAME}/${iconFile}`));

      // Try from project root
      iconPaths.push(path.join(process.cwd(), `icons/${this.APP_PATH_NAME}/${iconFile}`));
    });

    console.log(`🔍 Searching ${iconPaths.length} possible icon locations...`);

    for (const testPath of iconPaths) {
      console.log('Checking app icon path:', testPath);
      if (require('fs').existsSync(testPath)) {
        try {
          // Set app icon for dock/taskbar on macOS
          if (process.platform === 'darwin') {
            app.dock?.setIcon(testPath);
            console.log('✅ macOS dock icon set');
          }

          // For Windows, we need to handle icons differently
          // Note: app.setIcon() doesn't exist in Electron - this was incorrect
          // Windows taskbar icon is controlled by the BrowserWindow icon property
          if (process.platform === 'win32') {
            console.log('✅ Windows icon found, will be set on BrowserWindow creation');
          }

          // Store the found icon path for use in window creation
          this.appIconPath = testPath;
          console.log(`✅ Application icon found and set: ${testPath}`);
          return testPath;
        } catch (error) {
          console.warn('Failed to set application icon:', error.message);
        }
      }
    }

    // If no icon found, list available directories for debugging
    console.warn(`❌ Could not find application icon for "${this.APP_PATH_NAME}"`);
    this.debugIconDirectories();
    return null;
  }

  debugIconDirectories = () => {
    console.log('🔍 Debugging icon directories...');
    const fs = require('fs');

    const dirsToCheck = [
      path.join(__dirname, './icons'),
      path.join(__dirname, '../icons'),
      path.join(process.cwd(), 'icons')
    ];

    dirsToCheck.forEach(dir => {
      try {
        if (fs.existsSync(dir)) {
          const contents = fs.readdirSync(dir);
          console.log(`📁 ${dir}: [${contents.join(', ')}]`);

          // Check if our app directory exists
          const appDir = path.join(dir, this.APP_PATH_NAME);
          if (fs.existsSync(appDir)) {
            const appContents = fs.readdirSync(appDir);
            console.log(`📁 ${appDir}: [${appContents.join(', ')}]`);
          }
        } else {
          console.log(`📁 ${dir}: [DOES NOT EXIST]`);
        }
      } catch (error) {
        console.log(`📁 ${dir}: [ERROR: ${error.message}]`);
      }
    });
  }

  setWindowsIcon = (win, iconPath) => {
    console.log(`🔧 Setting Windows icon: ${iconPath}`);

    try {
      const fs = require('fs');

      // Validate icon file exists and has content
      if (!fs.existsSync(iconPath)) {
        console.warn('❌ Icon file does not exist:', iconPath);
        return;
      }

      const stats = fs.statSync(iconPath);
      if (stats.size === 0) {
        console.warn('❌ Icon file is empty:', iconPath);
        return;
      }

      console.log(`📊 Icon file size: ${stats.size} bytes`);

      // Method 1: Try with nativeImage
      try {
        const icon = nativeImage.createFromPath(iconPath);
        if (!icon.isEmpty()) {
          const size = icon.getSize();
          console.log(`📐 Icon dimensions: ${size.width}x${size.height}`);

          win.setIcon(icon);
          console.log('✅ Windows icon set using nativeImage');

          // Also try to set it on the window after a delay
          setTimeout(() => {
            try {
              win.setIcon(icon);
              console.log('✅ Windows icon re-applied with delay');
            } catch (delayError) {
              console.warn('Failed to re-apply icon with delay:', delayError.message);
            }
          }, 500);

          return;
        } else {
          console.warn('❌ nativeImage created but is empty');
        }
      } catch (nativeImageError) {
        console.warn('❌ nativeImage creation failed:', nativeImageError.message);
      }

      // Method 2: Try with direct path
      try {
        win.setIcon(iconPath);
        console.log('✅ Windows icon set using direct path');
      } catch (directPathError) {
        console.warn('❌ Direct path icon setting failed:', directPathError.message);
      }

    } catch (error) {
      console.warn('❌ Windows icon setting failed completely:', error.message);
    }
  }

  setWindowsAppIcon = (iconPath) => {
    console.log(`🔧 Setting Windows app-level icon: ${iconPath}`);

    try {
      // For Windows, we can try to set the default window icon
      // This affects new windows created by the app
      const icon = nativeImage.createFromPath(iconPath);
      if (!icon.isEmpty()) {
        // Store the icon for use with new windows
        app.setDefaultWindowIcon = icon;
        console.log('✅ Windows app-level default icon set');

        // Also try to set it as the app icon (if this method exists)
        if (typeof app.setIcon === 'function') {
          try {
            app.setIcon(icon);
            console.log('✅ Windows app.setIcon() called successfully');
          } catch (appIconError) {
            console.log('ℹ️ app.setIcon() not available or failed:', appIconError.message);
          }
        } else {
          console.log('ℹ️ app.setIcon() method not available');
        }
      }
    } catch (error) {
      console.warn('❌ Windows app-level icon setting failed:', error.message);
    }
  }

  CreateBrowserWindow = () => {
    // Use the icon path found during application icon setting, or find it again
    let iconPath = this.appIconPath;

    if (!iconPath) {
      console.log(`🔍 Searching for window icon for app: "${this.APP_PATH_NAME}"`);

      // Use the same robust icon resolution as setApplicationIcon
      const platformIconPrefs = {
        'win32': ['win/icon.ico', 'png/256x256.png', 'png/128x128.png', 'png/64x64.png', 'png/32x32.png', 'png/16x16.png'],
        'darwin': ['mac/icon.icns', 'png/128x128.png', 'png/64x64.png'],
        'linux': ['png/64x64.png', 'png/128x128.png', 'png/32x32.png']
      };

      const currentPlatformPrefs = platformIconPrefs[process.platform] || platformIconPrefs['linux'];
      const iconPaths = [];

      // For each platform preference, try all possible locations
      currentPlatformPrefs.forEach(iconFile => {
        iconPaths.push(path.join(__dirname, `./icons/${this.APP_PATH_NAME}/${iconFile}`));
        if (process.resourcesPath) {
          iconPaths.push(path.join(process.resourcesPath, `icons/${this.APP_PATH_NAME}/${iconFile}`));
        }
        iconPaths.push(path.join(__dirname, `../icons/${this.APP_PATH_NAME}/${iconFile}`));
        iconPaths.push(path.join(process.cwd(), `icons/${this.APP_PATH_NAME}/${iconFile}`));
      });

      for (const testPath of iconPaths) {
        if (require('fs').existsSync(testPath)) {
          iconPath = testPath;
          console.log('✅ Found window icon at:', iconPath);
          break;
        }
      }
    }

    // Fallback to default Electron icon if none found
    if (!iconPath) {
      console.warn(`❌ No custom window icon found for "${this.APP_PATH_NAME}", using default Electron icon`);
      this.debugIconDirectories();
    } else {
      console.log('✅ Using window icon path:', iconPath);
    }

    const win = new BrowserWindow({
      minWidth: 1024,
      minHeight: 700,
      width: 1400,
      height: 900,
      show: false,
      useContentSize: true,
      title: this.APP_NAME,
      icon: iconPath,
      "accept-first-mouse": true,
      // PERFORMANCE: Enhanced window configuration
      backgroundColor: '#ffffff', // Prevents white flash
      titleBarStyle: 'default', // Show native title bar
      vibrancy: 'under-window', // macOS only
      webPreferences: {
        preload: path.join(__dirname, "preload.js"),
        // BACKWARD COMPATIBILITY: Keep existing security settings
        nodeIntegration: true, // Keep enabled for existing functionality
        contextIsolation: false, // Keep disabled for existing functionality
        enableRemoteModule: true, // Keep enabled for existing functionality
        webSecurity: false, // Keep disabled for HTTP requests and external resources
        sandbox: false, // Keep false for Node.js APIs
        spellcheck: true,
        // PERFORMANCE: Memory and rendering optimizations (safe)
        experimentalFeatures: true,
        v8CacheOptions: 'code',
        backgroundThrottling: false,
        // PERFORMANCE: Hardware acceleration (safe)
        hardwareAcceleration: true,
        // PERFORMANCE: Safe optimizations only
        defaultEncoding: "UTF-8",
      },
    });

    this.win = win;

    // CRITICAL: Set window icon after creation for Windows taskbar icon
    if (iconPath && process.platform === 'win32') {
      this.setWindowsIcon(win, iconPath);
    }

    // and load the index.html of the app.
    // win.loadFile("index.html");

    win.loadURL(
      !this.isPro || !this.filePath
        ? "http://localhost:" + (3000 + this.port)
        : this.filePath
    );
    // Open the DevTools.
    if (!this.isPro) {
      win.webContents.openDevTools({ mode: "detach" });
    }
    win.maximize();
    win.show();

    // Additional Windows icon fix: Re-apply icon after window is shown
    if (iconPath && process.platform === 'win32') {
      setTimeout(() => {
        this.setWindowsIcon(win, iconPath);
      }, 200);
    }

    new AppUpdater(win);
    // console.log("the updater before");
    // const updater = new AutoUpdater();
    // console.log("the updater", updater);

    win.webContents.setWindowOpenHandler((edata) => {
      shell.openExternal(edata.url);
      return { action: "deny" };
    });

    // Use robust icon path resolution for tray (consistent with main window)
    console.log(`🔍 Searching for tray icon for app: "${this.APP_PATH_NAME}"`);

    // Tray icons prefer smaller sizes
    const trayIconPrefs = ['png/32x32.png', 'png/16x16.png', 'png/64x64.png'];
    const trayIconPaths = [];

    // For each tray icon preference, try all possible locations
    trayIconPrefs.forEach(iconFile => {
      trayIconPaths.push(path.join(__dirname, `./icons/${this.APP_PATH_NAME}/${iconFile}`));
      if (process.resourcesPath) {
        trayIconPaths.push(path.join(process.resourcesPath, `icons/${this.APP_PATH_NAME}/${iconFile}`));
      }
      trayIconPaths.push(path.join(__dirname, `../icons/${this.APP_PATH_NAME}/${iconFile}`));
      trayIconPaths.push(path.join(process.cwd(), `icons/${this.APP_PATH_NAME}/${iconFile}`));
    });

    let trayIconPath = null;
    for (const testPath of trayIconPaths) {
      if (require('fs').existsSync(testPath)) {
        trayIconPath = testPath;
        console.log('✅ Found tray icon at:', trayIconPath);
        break;
      }
    }

    if (!trayIconPath) {
      console.warn(`❌ No custom tray icon found for "${this.APP_PATH_NAME}", skipping tray creation`);
    } else {
      console.log('✅ Using tray icon path:', trayIconPath);
      tray = new AppTray(trayIconPath, win);
    }
  };
}

module.exports = ElectronClass;
