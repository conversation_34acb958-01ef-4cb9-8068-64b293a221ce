import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "antd";
import moment from "moment";
import InvoiceTemplate from "../../../Universal/CustomViews/Components/InvoiceTemplate";
import PouchDb from "pouchdb-browser";

const VisitInvoice = ({ record, ...props }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [guest, setGuest] = useState(null);

  const data = record;

  useEffect(() => {
    const guestDB = new PouchDb("guests");
    data.guest
      ? guestDB.get(data.guest.value).then((data) => {
          setGuest(data);
        })
      : setGuest(null);
  }, [data]);

  

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const newdata = {
    date: record.createdAt,
    id: record._id,
    client: { title: "Guest", name: guest }, //data.guest,
    operator: { title: "Entrant", name: record.entrant.label },
    items: [
      {
        name: `Accommodation in ${record.room_number.label}`,
        price: record.room_rate,
        quantity: moment(record.departure_date)
          .startOf("day")
          .diff(moment(record.arrival_date).startOf("day"), "days"),
      },
      ...((record.extensions && [
        {
          name: "Visit Extension",
          price: record.room_rate,
          quantity: record.extensions / record.room_rate,
        },
      ]) ||
        []),
      ...((record.items &&
        record.items.map((item) => {
          return {
            name: item.item,
            price: item.cost,
            quantity: item.quantity,
          };
        })) ||
        []),
    ],
  };

  

  return (
    <>
      <Typography.Text type="primary" onClick={showModal}>
        Print Checkin Invoice
      </Typography.Text>
      <Modal
        title="Basic Modal"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
      >
        <InvoiceTemplate documentTitle="Visit Invoice" data={newdata} />
      </Modal>
    </>
  );
};

export default VisitInvoice;
