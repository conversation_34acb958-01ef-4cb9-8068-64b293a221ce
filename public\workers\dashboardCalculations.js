// Dashboard calculations worker
// This runs in a separate thread to prevent UI blocking

self.onmessage = function(e) {
  const { type, data } = e.data;

  try {
    switch(type) {
      case 'CALCULATE_INVOICE_STATS':
        const invoiceStats = calculateInvoiceStats(data.invoices, data.receipts);
        self.postMessage({
          type: 'INVOICE_STATS_RESULT',
          data: invoiceStats,
          success: true
        });
        break;

      case 'CALCULATE_JOB_STATS':
        const jobStats = calculateJobStats(data.jobs);
        self.postMessage({
          type: 'JOB_STATS_RESULT',
          data: jobStats,
          success: true
        });
        break;

      case 'CALCULATE_CHART_DATA':
        const chartData = calculateChartData(data.invoices, data.expenses);
        self.postMessage({
          type: 'CHART_DATA_RESULT',
          data: chartData,
          success: true
        });
        break;

      default:
        self.postMessage({
          type: 'ERROR',
          error: 'Unknown calculation type',
          success: false
        });
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      error: error.message,
      success: false
    });
  }
};

// Calculate invoice statistics
function calculateInvoiceStats(invoices, receipts) {
  // Create a map of receipts by invoice ID for faster lookups
  const receiptsByInvoice = {};
  receipts.forEach(receipt => {
    if (receipt.invoice && receipt.invoice.value) {
      if (!receiptsByInvoice[receipt.invoice.value]) {
        receiptsByInvoice[receipt.invoice.value] = [];
      }
      receiptsByInvoice[receipt.invoice.value].push(receipt);
    }
  });

  // Process invoice data in a single pass
  let unPaid = 0;
  let overDue = 0;
  let thisMonth = 0;
  let lastMonth = 0;

  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();

  // Get last month and year
  const lastMonthDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const previousMonth = lastMonthDate.getMonth();
  const previousYear = lastMonthDate.getFullYear();

  invoices.forEach(invoice => {
    // Calculate invoice total once with safe numeric conversion
    const invoiceTotal = (invoice.taxable ? 1.18 : 1) * (
      (invoice.items || []).reduce((acc, item) => {
        if (!item) return acc;
        const price = parseFloat(item.price) || 0;
        const quantity = parseFloat(item.quantity) || 0;
        return acc + (price * quantity);
      }, 0) - (parseFloat(invoice.discount) || 0)
    );

    // Get receipts for this invoice
    const invoiceReceipts = receiptsByInvoice[invoice._id] || [];

    // Calculate total paid amount once
    const paidAmount = invoiceReceipts.reduce(
      (total, receipt) => total + (receipt.amount || 0), 0
    );

    // Calculate remaining amount
    const remainingAmount = invoiceTotal - paidAmount;

    // Add to unpaid total
    unPaid += remainingAmount;

    // Check if invoice is from current month
    const invoiceDate = new Date(invoice.date);
    const isCurrentMonth = (
      invoiceDate.getMonth() === currentMonth &&
      invoiceDate.getFullYear() === currentYear
    );

    if (isCurrentMonth) {
      thisMonth += remainingAmount;
    }

    // Check if invoice is from last month
    const isLastMonth = (
      invoiceDate.getMonth() === previousMonth &&
      invoiceDate.getFullYear() === previousYear
    );

    if (isLastMonth) {
      lastMonth += remainingAmount;
    }

    // Check if invoice is overdue (more than 7 days old)
    const daysSinceInvoice = Math.floor((today - invoiceDate) / (1000 * 60 * 60 * 24));
    if (daysSinceInvoice > 7) {
      overDue += remainingAmount;
    }
  });

  // Calculate percentage change for invoices
  let invoicePercentageChange = 0;
  if (lastMonth > 0) {
    invoicePercentageChange = ((thisMonth - lastMonth) / lastMonth) * 100;
  } else if (thisMonth > 0) {
    invoicePercentageChange = 100;
  }

  return {
    unPaid,
    overDue,
    thisMonth,
    lastMonth,
    invoicePercentageChange
  };
}

// Calculate job statistics
function calculateJobStats(jobs) {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Get last month and year
  const lastMonthDate = new Date(currentYear, currentMonth - 1, 1);
  const lastMonth = lastMonthDate.getMonth();
  const lastYear = lastMonthDate.getFullYear();

  // Count new jobs by month
  let newJobsThisMonth = 0;
  let newJobsLastMonth = 0;

  // Single pass through the data
  jobs.forEach(job => {
    // Use createdAt or date field to determine when the job was created
    const jobDate = new Date(job.createdAt || job.date);

    // Check if job was created this month
    if (jobDate.getMonth() === currentMonth && jobDate.getFullYear() === currentYear) {
      newJobsThisMonth++;
    }

    // Check if job was created last month
    if (jobDate.getMonth() === lastMonth && jobDate.getFullYear() === lastYear) {
      newJobsLastMonth++;
    }
  });

  // Calculate percentage change
  let percentageChange = 0;
  if (newJobsLastMonth > 0) {
    percentageChange = ((newJobsThisMonth - newJobsLastMonth) / newJobsLastMonth) * 100;
  } else if (newJobsThisMonth > 0) {
    percentageChange = 100;
  }

  return {
    jobs,
    newJobsThisMonth,
    newJobsLastMonth,
    percentageChange
  };
}

// Calculate chart data for income vs expenditure
function calculateChartData(invoices, expenses) {
  const incomeByMonth = {};
  const expenseByMonth = {};

  // Pre-generate the month keys for the last 12 months
  const monthKeys = [];
  for (let i = 11; i >= 0; i--) {
    const currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - i);
    const monthKey = currentDate.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
    monthKeys.push(monthKey);

    // Initialize with zero values
    incomeByMonth[monthKey] = 0;
    expenseByMonth[monthKey] = 0;
  }

  // Process invoices
  if (invoices && invoices.length > 0) {
    invoices.forEach(invoice => {
      if (!invoice.date) return;

      const invoiceDate = new Date(invoice.date);
      const invoiceMonth = invoiceDate.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });

      if (incomeByMonth.hasOwnProperty(invoiceMonth)) {
        const invoiceTotal = parseInt(
          (invoice.taxable ? 1.18 : 1) *
          (invoice.items || []).reduce((acc, item) => {
            if (!item) return acc;
            const price = parseFloat(item.price) || 0;
            const quantity = parseFloat(item.quantity) || 0;
            return acc + (price * quantity);
          }, 0)
        );
        incomeByMonth[invoiceMonth] += invoiceTotal;
      }
    });
  }

  // Process expenses
  if (expenses && expenses.length > 0) {
    expenses.forEach(expense => {
      if (!expense.date && !expense.expense_Date) return;

      const expenseDate = new Date(expense.date || expense.expense_Date);
      const expenseMonth = expenseDate.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });

      if (expenseByMonth.hasOwnProperty(expenseMonth)) {
        const expenseAmount = parseFloat(expense.amount) || 0;
        expenseByMonth[expenseMonth] += expenseAmount;
      }
    });
  }

  // Convert to chart format
  const chartData = [];
  monthKeys.forEach(month => {
    chartData.push({
      date: month,
      amount: incomeByMonth[month],
      category: "Income"
    });
    chartData.push({
      date: month,
      amount: expenseByMonth[month],
      category: "Expenditure"
    });
  });

  return chartData;
}
