import { PrinterOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, DatePicker, Row, Table, Typography } from "antd";
import moment from "moment";
import React, { useState } from "react";
import PrintComponents from "react-print-components";
import DocumentHead from "../../../../Components/Reports/DocumentHead";
import { formatNumber } from "../../../../Utils/functions";

const Expense = (props) => {
  const [dateRange, setDateRange] = useState([Date(), Date()]);

  const { expense, company } = props;

  const columns = [
    {
      dataIndex: "date",
      title: "Date",
      render: (text) => moment(text).format("DD MMM YY"),
    },
    {
      dataIndex: "supplier",
      title: "Supplier",
      render: (text) => text && text.label && text.label,
    },
    {
      dataIndex: "description",
      title: "Description",
    },
    {
      dataIndex: "amount",
      title: "Amount",
      render: (value) => formatNumber(value),
    },
  ];

  const tProps = {
    size: "small",
    columns: columns,
    pagination: false,
    dataSource: dateRange
      ? expense.filter(
          (r) =>
            moment(r.date).isSameOrAfter(moment(dateRange[0].$d), "day") &&
            moment(r.date).isSameOrBefore(moment(dateRange[1].$d), "day")
        )
      : expense.reverse().slice(0, 10).reverse(),
    summary: (pageData) => {
      

      return (
        <Table.Summary fixed style={{ fontSize: 20 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={columns.length - 1}>
              Total
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {formatNumber(pageData.reduce((a, b) => a + Number(b.amount?b.amount:0), 0))}
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  };

  return (
    <div>
      <Table
        {...tProps}
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Expense Report</Typography.Title>
                  <Table {...tProps} />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Expense;
