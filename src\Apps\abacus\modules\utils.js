const moment = require("moment");
export const items = [
  {
    valueType: "date",
    dataIndex: "date",
    title: "Date",
    fixed: "Left",
    initialValue: moment().startOf("day"),
  },
  {
    dataIndex: "customer",
    title: "Customer",
    type: "dbSelect",
    valueType: "select",
    collection: "customers",
    label: ["name"],
    isRequired: true,
  },
  {
    valueType: "formList",
    dataIndex: "items",
    title: "Items",
    fieldProps: {
      initialValue: [{}],
      creatorButtonProps: {
        block: true,
        style: {
          width: "100%",
        },
        copyIconProps: false,
        creatorButtonText: "Add Item",
      },
    },
    columns: [
      {
        valueType: "group",
        colProps: {
          md: 24,
        },
        columns: [
          {
            dataIndex: "product",
            title: "Product",
            type: "dbSelect",
            valueType: "select",
            collection: "products",
            label: ["name", " ", "sku", " - ", "measurements", "units"],
            colProps: {
              md: 12,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: `Please Select a product`,
                },
              ],
            },
          },
          {
            valueType: "digit",
            dataIndex: "quantity",
            title: "Quantity",
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: `Quantity is required`,
                },
              ],
            },
            colProps: {
              md: 4,
            },
            dependency: {
              value: "product",
              rules: [{ required: true, message: "Please select a product" }],
            },
          },
          {
            valueType: "money",
            dataIndex: "price",
            title: "Price",
            colProps: {
              md: 4,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: `Price is required`,
                },
              ],
            },
            dependency: {
              value: "product",
              rules: [{ required: true, message: "Please select a product" }],
            },
          },
          {
            valueType: "dependency",
            fieldProps: {
              name: ["price", "quantity"],
              colProps: {
                md: 4,
              },
            },
            columns: ({ price, quantity }) => {
              return [
                {
                  title: "Total",
                  dataIndex: "total",
                  colProps: {
                    md: 4,
                  },
                  fieldProps: {
                    disabled: true,
                    value: (price ? price : 0) * (quantity ? quantity : 0),
                  },
                  valueType: "money",
                },
              ];
            },
          },
        ],
      },
    ],
    render: (record, { items }) => {
      let allItems = "";

      


      items &&
        items.map(
          (item, index) =>
          (allItems = item && index < 3
            ? allItems +
            item.product.label.split(" - ")[0] +
            ((index + 1 === items.length || index + 1 === 3) ? " ...." : ", ")
            : allItems)
        );
      return allItems;
    },
  },
  {
    title: "Discounted",
    dataIndex: "discounted",
    valueType: "switch",
    fieldProps: {
      unCheckedChildren: "No",
      checkedChildren: "Yes",
    },
    colProps: {
      md: 2,
      offset: 14,
    },
    hideInTable: true,
  },
  {
    title: "Taxable",
    dataIndex: "taxable",
    valueType: "switch",
    fieldProps: {
      unCheckedChildren: "No",
      checkedChildren: "Yes",
    },
    colProps: {
      md: 2,
    },
    hideInTable: true,
  },
  {
    valueType: "dependency",
    fieldProps: {
      name: ["items"],
    },

    render: (record, { items }) => {
      let total = 0;
      items &&
        items.map(
          (item) =>
          (total = item
            ? total +
            (item.price ? item.price : 0) *
            (item.quantity ? item.quantity : 0)
            : total)
        );
      return total;
    },
    columns: ({ items }) => {
      return [
        {
          title: "Total",
          dataIndex: "total",
          colProps: {
            md: 8,
            offset: 16,
          },
          fieldProps: {
            disabled: true,
            value: items.reduce(
              (pv, cv) =>
                pv +
                (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
              0
            ),
          },
          valueType: "money",
        },
      ];
    },
  },

  {
    valueType: "dependency",
    hideInTable: true,
    name: ["taxable", "items", "discounted"],
    columns: ({ taxable, items, discounted }) => {
      let columnsToRender = [];

      if (discounted && items.length > 0) {
        columnsToRender.push({
          dataIndex: "discount_description",
          title: "Discount Description",
          colProps: {
            md: 6,
            offset: !taxable ? 12 : 6,
          },
        });
        columnsToRender.push({
          dataIndex: "discount",
          title: "Discount",
          valueType: "money",
          colProps: {
            md: 6,
            // offset: !taxable ? 18 : 12,
          },
        });
      }

      if (taxable && items.length > 0) {
        columnsToRender.push({
          dataIndex: "tax",
          title: "Tax",
          valueType: "money",
          colProps: {
            md: 6,
            offset: !discounted ? 18 : null,
          },
          fieldProps: {
            disabled: true,
            value:
              items.reduce(
                (pv, cv) =>
                  pv +
                  (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                0
              ) * 0.18,
          },
        });
      }

      return columnsToRender;
    },
  },

  {
    valueType: "textarea",
    dataIndex: "description",
    title: "Description, Remarks or Notes",
    hideInTable: true,
  },
];
