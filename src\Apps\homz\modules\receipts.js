import moment from "moment";
import checkins from "./checkins";
import invoices from "./invoices";
import { formatMoney } from "../../../Utils/functions";
export default {
  name: "Receipt",
  icon: "UsergroupAddOutlined",
  path: "/receipts",
  collection: "receipts",
  singular: "Receipt",
  removeCreate: true,
  columns: [
    {
      title: "Reference No",
      dataIndex: "ref_number",
      sorter: true,
      valueType: "number",
      width: "lg",
      colProps: {
        md: 6,
      },
    },
    {
      title: "Date",
      dataIndex: "createdAt",
      type: "date",
      hideInForm: true,
    },
    {
      type: "dbSelect",
      isRequired: true,
      dataIndex: "checkin",
      collection: checkins.collection,
      title: "Check In",
      label: ["guest.label", "-", "_id"],
      width: "lg",
    },
    {
      type: "dbSelect",
      dataIndex: "invoice",
      collection: invoices.collection,
      title: "Invoice",
      hideInTable: true,
      label: ["reservation.label", "-", "_id"],
      width: "lg",
      colProps: {
        md: 12,
      },
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      width: "lg",
      colProps: {
        md: 6,
      },
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Cheque: {
          text: "Cheque",
        },
        "Mobile Money": {
          text: "Mobile Money",
        },
      },
    },
    {
      title: "Currency",
      dataIndex: "currency",
      isRequired: true,
      sorter: true,
      hideInTable: true,
      valueType: "select",
      valueEnum: {
        USD: {
          text: "USD",
        },
        UGX: {
          text: "UGX",
        },
      },
    },
    {
      title: "Services",
      dataIndex: "services",
      sorter: true,
      isRequired: true,
      hideInTable: true,
      valueType: "select",
      fieldProps: {
        mode: "multiple",
      },
      valueEnum: {
        "Rental charges": {
          text: "Rental charges",
        },
        Breakfast: {
          text: "Breakfast",
        },
        Laundry: {
          text: "Laundry",
        },
        Printing: {
          text: "Printing",
        },
        "Visit Extension": {
          text: "Visit Extension",
        },
        Others: {
          text: "Others",
        },
      },
    },
    {
      title: "Amount",
      dataIndex: "amount",
      isRequired: true,
      type: "money",
    },
    {
      valueType: "dependency",
      hideInTable: true,
      title: "Total",
      width: "lg",
      fieldProps: {
        name: ["checkin", "currency", "amount"],
      },
      columns: ({ checkin = null, currency, amount = 0 }) => {
        const items = checkin && checkin.items,
          apartment_rate = checkin && checkin.apartment_rate,
          arrival_date = checkin && checkin.arrival_date,
          departure_date = checkin && checkin.departure_date;
        var total = 0,
          rental_charges = 0,
          extensions = 0,
          otherServices = 0,
          previousReceipts = 0;

        if (checkin && checkin.extensions) {
          total = total + checkin.extensions;
          extensions = checkin.extensions;
        }

        if (checkin && checkin.receipts) {
          total = total - checkin.receipts;
          previousReceipts = checkin.receipts;
        }

        if (apartment_rate) {
          total =
            total +
            (moment(departure_date)
              .startOf("day")
              .diff(moment(arrival_date).startOf("day"), "days") < 1
              ? 1
              : moment(departure_date)
                  .startOf("day")
                  .diff(moment(arrival_date).startOf("day"), "days")) *
              apartment_rate;
          rental_charges =
            (moment(departure_date)
              .startOf("day")
              .diff(moment(arrival_date).startOf("day"), "days") < 1
              ? 1
              : moment(departure_date)
                  .startOf("day")
                  .diff(moment(arrival_date).startOf("day"), "days")) *
            apartment_rate;
        }

        items &&
          items.map(function (item) {
            total = item
              ? total +
                ((item.tax ? item.tax : 0) / 100) *
                  ((item.cost ? item.cost : 0) *
                    (item.quantity ? item.quantity : 0)) +
                (item.cost ? item.cost : 0) *
                  (item.quantity ? item.quantity : 0)
              : total;
            otherServices = item
              ? (otherServices +=
                  ((item.tax ? item.tax : 0) / 100) *
                    ((item.cost ? item.cost : 0) *
                      (item.quantity ? item.quantity : 0)) +
                  (item.cost ? item.cost : 0) *
                    (item.quantity ? item.quantity : 0))
              : otherServices;
          });

        total = (currency === "UGX" ? 3900 : 1) * total - amount;
        return [
          {
            dataIndex: "balance",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "money",
            render: function render(v) {
              return formatMoney(v);
            },
            renderFormItem: function renderFormItem() {
              return (
                "Rental Charges : " +
                formatMoney((currency === "UGX" ? 3900 : 1) * rental_charges) +
                " | Other Services :  " +
                formatMoney((currency === "UGX" ? 3900 : 1) * otherServices) +
                " | Extensions :  " +
                formatMoney((currency === "UGX" ? 3900 : 1) * extensions) +
                " | Previous Receipts :  (" +
                formatMoney(
                  (currency === "UGX" ? 3900 : 1) * previousReceipts
                ) +
                ") | Balance :  " +
                formatMoney(total) +
                " "
              );
            },
          },
        ];
      },
    },
  ],
};
