import React, { useState, useEffect } from 'react';
import { Line, Area } from '@ant-design/plots';
import moment from 'moment';

const IncomeExpenditure = ({ invoices, expenses }) => {

    const [chartData, setChartData] = useState([]);

    useEffect(() => {
        let newChartData = [];
        for (let i = 11; i >= 0; i--) {
            let currentDate = moment(new Date())

            currentDate = currentDate.subtract(i, 'months');

            let incomes = invoices.filter((n) => {
                var d = moment(n.date);
                return (currentDate.format("MMM YY") === d.format("MMM YY"));
            });
            let spent = expenses.filter((n) => {
                var d = moment(n.expense_Date);
                return (currentDate.format("MMM YY") === d.format("MMM YY"));
            });

            var sumOfGroupedIncome = incomes.reduce(function (sumSoFar, row) { return sumSoFar += parseInt(row.items.reduce((p, c) => p += c.tax ? ((1 + c.tax) / 100) * ((c.quantity ? c.quantity : 0) * (c.cost ? c.cost : 0)) : ((c.quantity ? c.quantity : 0) * (c.cost ? c.cost : 0)), 0)) }, 0);

            var sumOfGroupedSpending = spent.reduce(function (sumSoFar, row) { return sumSoFar += parseInt(row.amount) }, 0);


            newChartData.push(
                {
                    date: currentDate.format("MMM YY"),
                    category: 'Income',
                    amount: sumOfGroupedIncome,
                },
                {
                    date: currentDate.format("MMM YY"),
                    category: 'Expense',
                    amount: sumOfGroupedSpending,
                }
            )

        }

        setChartData(newChartData)

        // return () => {
        //     cleanup
        // };
    }, [invoices, expenses]);


    // let data = invoices.sort((a, b) => new Date(a.date) - new Date(b.date)).map(i => ({ date: moment(i.date).format('D MMM YY'), category: 'Income', amount: i.items.reduce((p, c) => p += c.tax ? ((1 + c.tax) / 100) * ((c.quantity ? c.quantity : 0) * (c.cost ? c.cost : 0)) : ((c.quantity ? c.quantity : 0) * (c.cost ? c.cost : 0)), 0) }))
    // data = [...expenses.sort((a, b) => new Date(a.expense_Date) - new Date(b.expense_Date)).map(i => ({ date: moment(i.expense_Date).format('D MMM YY'), category: 'Expense', amount: i.amount })), ...data]
    const config = {
        data: chartData,
        xField: 'date',
        yField: 'amount',
        seriesField: 'category',
        smooth: true,
        yAxis: {
            label: {
                // Numbers formatted as thousandths
                formatter: (v) => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
            },
        },
    };

    return <Area {...config} />;
};

export default IncomeExpenditure