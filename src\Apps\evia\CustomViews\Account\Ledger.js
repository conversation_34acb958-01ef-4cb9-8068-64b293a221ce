import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Row,
  Col,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import "../Reports/print.css";
import DocumentHead from "../Reports/DocumentHead";
import { numberFormat } from "../../../../Utils/functions";

const Ledger = (props) => {
  const [dateRange, setDateRange] = useState(null);

  const { data, title } = props;

  const columns = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (text) => moment(text).format("DD MMM YY"),
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Amount",
      dataIndex: "amount",
      key: "amount",
      render: (text) => numberFormat(text),
    },
  ];

  return (
    <div>
      <Table
        size="small"
        columns={columns}
        pagination={false}
        summary={(pageData) => {
          

          return (
            <Table.Summary fixed style={{ fontSize: 20 }}>
              <Table.Summary.Row>
                <Table.Summary.Cell colSpan={2}>Total</Table.Summary.Cell>
                <Table.Summary.Cell>
                  {numberFormat(pageData.reduce((a, b) => a + b.amount, 0))}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          );
        }}
        dataSource={
          dateRange
            ? data
                .sort((a, b) => new Date(a.date) - new Date(b.date))
                .filter(
                  (r) =>
                    moment(r.date).isSameOrAfter(
                      moment(dateRange[0].$d),
                      "day"
                    ) &&
                    moment(r.date).isSameOrBefore(
                      moment(dateRange[1].$d),
                      "day"
                    )
                )
            : data
                .sort((a, b) => new Date(a.date) - new Date(b.date))
                .filter((r) => moment(r.date).isSame(new Date(), "year"))
        }
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                initialValue={[moment().startOf("month"), moment()]}
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {/* {company && <DocumentHead  />} */}
                  <Typography.Title level={3}>
                    {`${title} Ledger`}
                  </Typography.Title>
                  <Table
                    size="small"
                    columns={columns}
                    dataSource={
                      dateRange
                        ? data
                            .sort((a, b) => new Date(a.date) - new Date(b.date))
                            .filter(
                              (r) =>
                                moment(r.date).isSameOrAfter(
                                  moment(dateRange[0].$d),
                                  "day"
                                ) &&
                                moment(r.date).isSameOrBefore(
                                  moment(dateRange[1].$d),
                                  "day"
                                )
                            )
                        : data
                            .sort((a, b) => new Date(a.date) - new Date(b.date))
                            .filter((r) =>
                              moment(r.date).isSame(new Date(), "year")
                            )
                    }
                    pagination={false}
                    className="custom-table"
                    summary={(pageData) => {
                      

                      return (
                        <Table.Summary fixed style={{ fontSize: 20 }}>
                          <Table.Summary.Row>
                            <Table.Summary.Cell colSpan={2}>
                              Total
                            </Table.Summary.Cell>
                            <Table.Summary.Cell>
                              {numberFormat(
                                pageData.reduce((a, b) => a + b.amount, 0)
                              )}
                            </Table.Summary.Cell>
                          </Table.Summary.Row>
                        </Table.Summary>
                      );
                    }}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Ledger;
