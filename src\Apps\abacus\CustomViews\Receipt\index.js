import React, { useEffect, useState } from "react";
import "./css.css";
import PouchDb from "pouchdb-browser";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import { Flex, Divider, Table, FloatButton } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import PrintComponents from "react-print-components";
import { numberFormat, toWordsUGX } from "../../../../Utils/functions";
import DocumentFooter from "../DocumentFooter";

const Receipt = (props) => {
  const { data, title = "Receipt", bottomText = "" } = props;

  // Get settings from localStorage
  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS")) || {
    showInvoiceDetails: true,
    showPreviousReceipts: true
  };

  const [company, setCompany] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [invoice, setInvoice] = useState(null);
  const [previousReceipts, setPreviousReceipts] = useState([]);

  // Calculate totals
  const calculateTotals = () => {
    if (!invoice) return { subTotal: 0, tax: 0, grandTotal: 0 };

    const subTotal = invoice.items.reduce(
      (acc, item) => acc + item.price * item.quantity,
      0
    );
    const tax = invoice.taxable ? subTotal * 0.18 : 0;
    const grandTotal = subTotal + tax;
    return { subTotal, tax, grandTotal };
  };

  // Get total amount paid from previous receipts
  const getTotalPaid = () => {
    return previousReceipts.reduce((acc, receipt) => acc + receipt.amount, 0);
  };

  useEffect(() => {
    // Fetch company details
    const CompanyDB = new PouchDb("organizations");
    CompanyDB.allDocs({
      include_docs: true,
      attachments: true,
      binary: true,
    }).then((data) => {
      let comp = data.rows[0].doc;
      if (comp._attachments && comp._attachments.logo) {
        CompanyDB.getAttachment(comp._id, "logo").then((res) => {
          comp.logo = res;
          setCompany(comp);
        });
      } else {
        setCompany(comp);
      }
    });
  }, []);

  useEffect(() => {
    // Fetch customer details
    const customersDB = new PouchDb("customers");
    if (invoice && invoice.customer) {
      customersDB
        .get(invoice.customer.value || invoice.customer._id, { binary: true })
        .then((res) => {
          setCustomer(res);
        });
    }
  }, [invoice]);

  useEffect(() => {
    // Fetch invoice details
    if (data && data.invoice) {
      const invoicesDB = new PouchDb("invoices");
      invoicesDB.get(data.invoice.value).then((res) => {
        setInvoice(res);
      });
    }
  }, [data]);

  useEffect(() => {
    // Fetch previous receipts
    if (data && data.invoice) {
      const receiptsDB = new PouchDb("receipts");
      receiptsDB.allDocs({ include_docs: true }).then((res) => {
        const previousReceipts = res.rows
          .map((row) => row.doc)
          .filter(
            (receipt) =>
              receipt.invoice &&
              receipt.invoice.value === data.invoice.value &&
              receipt._id !== data._id &&
              moment(receipt.date).isSameOrBefore(moment(data.date))
          )
          .sort((a, b) => moment(a.date).diff(moment(b.date)));
        setPreviousReceipts(previousReceipts);
      });
    }
  }, [data]);

  const { subTotal, tax, grandTotal } = calculateTotals();
  const totalPaid = getTotalPaid() + data.amount;
  const balance = grandTotal - totalPaid;
  const withHoldingTax = data.withholding ? balance : 0;
  const finalBalance = balance - withHoldingTax;

  const previousReceiptsColumns = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (text) => moment(text).format("DD MMM YYYY"),
    },
    {
      title: "Receipt No",
      dataIndex: "_id",
      key: "_id",
    },
    {
      title: "Amount",
      dataIndex: "amount",
      key: "amount",
      align: "right",
      render: (text) => numberFormat(text),
    },
  ];

  

  return !company || !customer || !invoice ? null : (
    <div className="receipt-container">
      <div className="header-section">
        <div className="logo-section">
          <RenderBlob blob={company.logo} size={150} />
        </div>
        <div className="title-section">
          <strong style={{ fontSize: 25, fontWeight: "bold" }}>
            {title.toUpperCase()}
          </strong>
          <p className="receipt-number">
            Receipt No: <b>{data._id}</b>
            <br />
            Date: <b>{moment(data.date).format("DD MMM YYYY")}</b>
          </p>
        </div>
        <div className="company-details">
          {company.address}
          <br />
          Tel: {company.phone}
          {company.alternative_phone && <><br />Alt: {company.alternative_phone}</>}
          <br />
          Email: {company.email}
          {company.website && (
            <>
              <br />
              {company.website}
            </>
          )}
        </div>
      </div>

      <Divider />

      <div className="info-grid">
        <div className="customer-section">
          <div className="section-title">RECEIVED FROM</div>
          <div className="info-row">
            <strong>{customer.name}</strong>
          </div>
          {customer.address && (
            <div className="info-row">Address: {customer.address}</div>
          )}
          <div className="info-row">
            Tel: {customer.phone}
            {customer.alternative_phone && ` / ${customer.alternative_phone}`}
          </div>
          {customer.email && <div className="info-row">Email: {customer.email}</div>}
        </div>

        <div className="amount-section">
          <div className="section-title">PAYMENT DETAILS</div>
          <div className="info-row">
            <strong>Amount:</strong> {numberFormat(data.amount)} {appSettings.currency}
          </div>
          <div className="info-row amount-words">
            <strong>Amount in Words:</strong><br />
            {toWordsUGX.convert(data.amount)}
          </div>
          <div className="info-row">
            <strong>Invoice No:</strong> {invoice._id}
          </div>
          {data.paymentMethod && (
            <div className="info-row">
              <strong>Payment Method:</strong> {data.paymentMethod}
            </div>
          )}
        </div>
      </div>

      {/* Invoice Details Section - Conditional Render */}
      {appSettings.showInvoiceDetails && (
        <div className="invoice-items">
          <h3>Invoice Details</h3>
          <table className="items-table">
            <thead>
              <tr>
                <th className="sn-column">S/N</th>
                <th>Description</th>
                <th>Quantity</th>
                <th>Rate</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {invoice.items.map((item, index) => (
                <tr key={index}>
                  <td className="sn-column">{index + 1}</td>
                  <td>{item.product.label}</td>
                  <td>{item.quantity}</td>
                  <td className="amount">{numberFormat(item.price)}</td>
                  <td className="amount">
                    {numberFormat(item.price * item.quantity)}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr>
                <td colSpan="4">Sub Total</td>
                <td className="amount">{numberFormat(subTotal)}</td>
              </tr>
              {tax > 0 && (
                <tr>
                  <td colSpan="4">VAT (18%)</td>
                  <td className="amount">{numberFormat(tax)}</td>
                </tr>
              )}
              <tr>
                <td colSpan="4">
                  <strong>Grand Total</strong>
                </td>
                <td className="amount">
                  <strong>{numberFormat(grandTotal)}</strong>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      )}

      {/* Previous Receipts Section - Conditional Render */}
      {appSettings.showPreviousReceipts && data.previousReceipts && data.previousReceipts.length > 0 && (
        <div className="previous-receipts">
          <h3>Previous Receipts</h3>
          <table className="items-table">
            <thead>
              <tr>
                <th className="sn-column">S/N</th>
                <th>Date</th>
                <th>Receipt No</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {data.previousReceipts.map((receipt, index) => (
                <tr key={index}>
                  <td className="sn-column">{index + 1}</td>
                  <td>{moment(receipt.date).format("DD/MM/YYYY")}</td>
                  <td>{receipt._id}</td>
                  <td className="amount">{numberFormat(receipt.amount)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="payment-summary">
        <table className="summary-table">
          <tbody>
            <tr>
              <td>Invoice Amount:</td>
              <td className="amount">{numberFormat(grandTotal)}</td>
            </tr>
            <tr>
              <td>Total Paid:</td>
              <td className="amount">{numberFormat(totalPaid)}</td>
            </tr>
            {withHoldingTax > 0 && (
              <tr>
                <td>Withholding Tax:</td>
                <td className="amount">{numberFormat(withHoldingTax)}</td>
              </tr>
            )}
            <tr>
              <td>
                <strong>Balance:</strong>
              </td>
              <td className="amount">
                <strong>{numberFormat(finalBalance)}</strong>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="signature-section">
        <Flex justify="space-between">
          <div className="signature-box">
            <p>Received By:</p>
            <div className="signature-line">_____________________</div>
          </div>
          <div className="signature-box">
            <p>Authorized Signature:</p>
            <div className="signature-line">_____________________</div>
          </div>
        </Flex>
      </div>

      <DocumentFooter text={bottomText} />
    </div>
  );
};

const PrintableReceipt = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton
            icon={<PrinterOutlined />}
            tooltip={<div>Print</div>}
          />
        }
      >
        <Receipt {...props} />
      </PrintComponents>
      <Receipt {...props} />
    </>
  );
};

export default PrintableReceipt;








