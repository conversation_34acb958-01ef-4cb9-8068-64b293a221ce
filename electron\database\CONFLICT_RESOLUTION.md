# Conflict Resolution in DatabaseHandler

## Overview

The DatabaseHandler now includes a simple yet effective last-write-wins conflict resolution mechanism that automatically handles conflicts during database synchronization operations.

## How It Works

### 1. Conflict Detection
- Conflicts are detected during PouchDB sync operations when documents have `_conflicts` arrays
- The SyncService automatically calls `DatabaseHandler.handleConflicts()` when conflicts are found
- Conflicts are handled for both LAN and remote sync operations

### 2. Resolution Strategy: Deterministic Last-Write-Wins

The conflict resolution uses a deterministic last-write-wins strategy with smart merging:

1. **Primary Sort**: Documents are sorted by `updatedAt` timestamp (newest first)
2. **Deterministic Tiebreaker**: If timestamps are equal, documents are sorted by `_id` lexicographically
3. **Smart Merging**: Unique fields from older revisions are preserved using deep merge

### 3. Conflict Resolution Process

```javascript
// 1. Detect conflicts during sync
if (doc._conflicts) {
  await this.databaseHandler.handleConflicts({ doc }, 'lan');
}

// 2. Get all conflicting revisions
const conflictDocs = await this.getConflictingRevisions(db, docId, conflicts);

// 3. Resolve using last-write-wins with smart merge
const resolvedDoc = await this.resolveLastWriteWins(winningRev, conflictDocs);

// 4. Apply resolved document and clean up conflicts
await this.applyResolvedDocument(db, docId, resolvedDoc, conflicts);
```

### 4. Smart Merging Features

- **Preserves unique fields** from older document revisions
- **Handles arrays** by combining unique values
- **Recursively merges objects** while preserving structure
- **Skips PouchDB metadata** (`_id`, `_rev`, `_conflicts`) during merge
- **Maintains data integrity** by preferring newest values for existing fields

## Integration Points

### SyncService Integration
The SyncService automatically handles conflicts during:
- LAN sync operations (`performLanSync`)
- Remote sync operations (`performRemoteSync`) 
- Specific URL sync operations (`performLanSyncWithUrl`)

### Event System
Conflict resolution emits events for monitoring:
```javascript
this.emit('conflictsResolved', {
  dbKey,
  docId,
  conflictCount: conflicts.length,
  source,
  resolvedDoc
});
```

## Manual Conflict Resolution

### Resolve Single Document
```javascript
const result = await databaseHandler.resolveDocumentConflicts(dbKey, docId);
```

### Resolve All Conflicts in Database
```javascript
const result = await databaseHandler.resolveAllConflicts(dbKey);
```

### Get Conflict Statistics
```javascript
const stats = databaseHandler.getConflictStats();
// Returns: { totalConflicts, resolvedConflicts, failedResolutions }
```

## Performance Considerations

- **Non-blocking**: Conflict resolution doesn't block sync operations
- **Efficient**: Uses PouchDB's built-in conflict detection
- **Lightweight**: Minimal overhead during normal operations
- **Error-tolerant**: Failed conflict resolutions don't break sync

## Error Handling

- Graceful handling of missing conflict revisions
- Continues sync operations even if conflict resolution fails
- Comprehensive logging for debugging
- Statistics tracking for monitoring

## Consistency Guarantees

- **Deterministic**: Same conflicts resolve to same result across all clients
- **Eventual consistency**: All clients converge to the same document state
- **Data preservation**: No data loss during conflict resolution
- **Metadata integrity**: Proper PouchDB revision handling

## Monitoring and Debugging

### Logging
- Conflict detection and resolution events are logged
- Error conditions are logged with context
- Performance metrics are tracked

### Statistics
- Total conflicts encountered
- Successfully resolved conflicts  
- Failed resolution attempts
- Resolution strategy usage

This implementation ensures reliable, consistent conflict resolution while maintaining the real-time syncing performance that users expect.
