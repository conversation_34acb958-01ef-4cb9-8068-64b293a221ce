import React, { useEffect, useState } from "react";
import { Bar, Column, Histogram, Line } from "@ant-design/plots";
// import currencyFormatter from "currency-formatter";
import moment from "moment";
import { buffProducts } from "../../modulesProperties/utils";

const formatData = {
  date: (text) => moment(text).format("MMM YY"),
  amount: (text) => text.toLocaleString(),
  // currencyFormatter.format(text, { code: "" })
};

const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

const MonthlyCollection = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");
    Promise.all([
      pouchDatabase("receipts", databasePrefix).getAllData(),
      pouchDatabase("order_receipts", databasePrefix).getAllData(),
      pouchDatabase("events_receipts", databasePrefix).getAllData(),
    ]).then((values) => {
      const income =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[0].filter((i) => i.branch === SELECTED_BRANCH)
          : values[0];

      const bistroIncome =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[1].filter((i) => i.branch === SELECTED_BRANCH)
          : values[1];

      const eventsIncome =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[2].filter((i) => i.branch === SELECTED_BRANCH)
          : values[2];

      let chartData = [];

      for (let i = 11; i >= 0; i--) {
        let currentDate = moment(new Date());
        currentDate = currentDate.subtract(i, "months");

        let IncomeThisMonth = income.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var hotelIncomeThisMonthAmount = IncomeThisMonth.reduce(
          (p, c) => p + Number(c.amount),
          0
        );

        let bistroIncomeThisMonth = bistroIncome.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var bistroIncomeThisMonthAmount = bistroIncomeThisMonth.reduce(
          (p, c) => p + Number(c.amount),
          0
        );

        let eventsIncomeThisMonth = eventsIncome.filter((n) => {
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var eventsIncomeThisMonthAmount = eventsIncomeThisMonth.reduce(
          (p, c) => p + Number(c.amount),
          0
        );

        chartData.push(
          {
            year: currentDate.format("MMM YY"),
            value: hotelIncomeThisMonthAmount,
            category: "Rooms",
          },
          {
            year: currentDate.format("MMM YY"),
            value: bistroIncomeThisMonthAmount,
            category: "Bistro",
          },
          {
            year: currentDate.format("MMM YY"),
            value: eventsIncomeThisMonthAmount,
            category: "Events",
          }
        );
      }
      setData(chartData);
    });
  }, []);

  const config = {
    data,
    isGroup: true,
    xField: "year",
    yField: "value",
    seriesField: "category",
    smooth: true,
    xAxis: {
      //   type: 'time',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.category, value: datum.value.toLocaleString() };
      },
    },
    point: {
      size: 4,
    },
    color: ["#1677ff", "#ff4d4f", "#fadb14"],
    yAxis: {
      label: {
        formatter: (v) =>
          `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
  };

  return <Column {...config} />;
};

export default MonthlyCollection;
