import moment from "moment";
import checkins from "./checkins";
export default {
  name: 'Visit Extensions',
  icon: 'UsergroupAddOutlined',
  path: '/extensions',
  collection: 'extensions',
  singular: 'Visit Extension',
  removeCreate:true,
  columns: [{
    type: 'dbSelect',
    isRequired: true,
    dataIndex: 'checkin',
    collection: checkins.collection,
    title: 'Check In',
    label: ['guest.label', "-", '_id'],
    width: 'lg'
  }, {
    title: 'Departure Date',
    dataIndex: 'departure_date',
    sorter: true,
    valueType: 'date',
    width: 'lg',
    colProps: {
      md: 12
    }
  }, {
    valueType: 'dependency',
    hideInTable: true,
    colProps: {
      md: 4
    },
    fieldProps: {
      name: ['checkin', 'departure_date']
    },
    columns: function columns(_ref) {
      var checkin = _ref.checkin,
          departure_date = _ref.departure_date;
      return [{
        title: 'Extended nights',
        dataIndex: 'nights',
        type: 'digit',
        fieldProps: {
          disabled: true,
          value: moment(departure_date).startOf('day').diff(moment(checkin.departure_date).startOf('day'), 'days')
        }
      }, {
        title: 'Amount',
        dataIndex: 'amount',
        isRequired: true,
        type: 'money',
        fieldProps: {
          disabled: true,
          value: checkin.apartment_rate * moment(departure_date).startOf('day').diff(moment(checkin.departure_date).startOf('day'), 'days')
        }
      }];
    }
  }]
};