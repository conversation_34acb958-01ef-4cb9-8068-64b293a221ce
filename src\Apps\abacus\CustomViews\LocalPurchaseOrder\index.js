import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Descriptions,
  Table,
  Tag,
  Button,
  Space,
  Avatar,
  Skeleton,
  Badge,
  Tooltip,
  Statistic
} from "antd";
import {
  ShoppingCartOutlined,
  DollarOutlined,
  UserOutlined,
  CalendarOutlined,
  PrinterOutlined,
  ShopOutlined,
  InfoCircleOutlined,
  FileTextOutlined
} from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import moment from "moment";
import PouchDB from "pouchdb";
import { numberFormat } from "../../../../Utils/functions";
import PrintComponents from "react-print-components";
import "./styles.css";

const { Title, Text } = Typography;

const LocalPurchaseOrder = ({ data, documentTitle = "Local Purchase Order" }) => {
  const [loading, setLoading] = useState(true);
  const [supplier, setSupplier] = useState(null);

  useEffect(() => {
    const fetchSupplier = async () => {
      if (data && data.supplier) {
        try {
          const suppliersDB = new PouchDB("suppliers");
          const supplierDoc = await suppliersDB.get(data.supplier.value);
          setSupplier(supplierDoc);
        } catch (error) {
          console.error("Error fetching supplier:", error);
        }
      }
      setLoading(false);
    };

    fetchSupplier();
  }, [data]);

  if (!data || loading) {
    return <Skeleton active />;
  }

  const getStatusTag = (status) => {
    const statusMap = {
      draft: { color: "default", text: "Draft" },
      approved: { color: "processing", text: "Approved" },
      pending: { color: "warning", text: "Pending" },
      converted: { color: "success", text: "Converted to Stock" },
      cancelled: { color: "error", text: "Cancelled" }
    };

    const { color, text } = statusMap[status] || { color: "default", text: status };
    return <Tag color={color}>{text}</Tag>;
  };

  // Calculate totals
  const subtotal = data.items?.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0;

  const columns = [
    {
      title: "Product",
      dataIndex: "product",
      key: "product",
      render: (product) => product?.label || "-",
    },
    {
      title: "Quantity",
      dataIndex: "quantity",
      key: "quantity",
      align: "right",
    },
    {
      title: "Unit Price",
      dataIndex: "price",
      key: "price",
      align: "right",
      render: (price) => numberFormat(price),
    },
    {
      // Expected delivery date moved to general fields
    },
    {
      title: "Total",
      key: "total",
      align: "right",
      render: (_, record) => numberFormat((record.quantity || 0) * (record.price || 0)),
    },
  ];

  const PrintableContent = () => (
    <div className="print-content">
      <div className="print-header">
        <Title level={3}>{documentTitle}</Title>
        <div className="print-meta">
          <div>
            <strong>LPO Number:</strong> {data.lpo_number}
          </div>
          <div>
            <strong>Date:</strong> {moment(data.date).format("DD MMM YYYY")}
          </div>
          <div>
            <strong>Expected Delivery:</strong> {data.expected_delivery_date ? moment(data.expected_delivery_date).format("DD MMM YYYY") : "Not specified"}
          </div>
          <div>
            <strong>Status:</strong> {data.status}
          </div>
        </div>
      </div>

      <div className="print-supplier">
        <Title level={4}>Supplier</Title>
        <div>{supplier?.name}</div>
        <div>{supplier?.address}</div>
        <div>{supplier?.phone}</div>
        <div>{supplier?.email}</div>
      </div>

      <Table
        columns={columns}
        dataSource={data.items || []}
        pagination={false}
        rowKey={(record) => record.product?.value || Math.random().toString()}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={4} align="right">
                <strong>Subtotal:</strong>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <strong>{numberFormat(subtotal)}</strong>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />

      {data.notes && (
        <div className="print-notes">
          <Title level={4}>Notes</Title>
          <p>{data.notes}</p>
        </div>
      )}

      <div className="print-signatures">
        <div className="signature-block">
          <div className="signature-line">Prepared By: {data.preparedBy?.label || "_________________"}</div>
        </div>
        <div className="signature-block">
          <div className="signature-line">Approved By: {data.approvedBy?.label || "_________________"}</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="local-purchase-order-view">
      <PageHeader
        title={
          <Space>
            <FileTextOutlined /> {documentTitle}
          </Space>
        }
        subTitle={`#${data.lpo_number}`}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print
              </Button>
            }
          >
            <PrintableContent />
          </PrintComponents>,
        ]}
      />

      <Card>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12}>
            <Descriptions title="LPO Information" column={1} bordered>
              <Descriptions.Item label="LPO Number">{data.lpo_number}</Descriptions.Item>
              <Descriptions.Item label="Date">
                {moment(data.date).format("DD MMM YYYY")}
              </Descriptions.Item>
              <Descriptions.Item label="Expected Delivery">
                {data.expected_delivery_date ? moment(data.expected_delivery_date).format("DD MMM YYYY") : "Not specified"}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                {getStatusTag(data.status)}
              </Descriptions.Item>
              {data.branch && (
                <Descriptions.Item label="Branch">{data.branch}</Descriptions.Item>
              )}
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions title="Supplier Information" column={1} bordered>
              <Descriptions.Item label="Supplier">
                {supplier?.name || data.supplier?.label || "N/A"}
              </Descriptions.Item>
              {supplier?.phone && (
                <Descriptions.Item label="Phone">{supplier.phone}</Descriptions.Item>
              )}
              {supplier?.email && (
                <Descriptions.Item label="Email">{supplier.email}</Descriptions.Item>
              )}
              {supplier?.address && (
                <Descriptions.Item label="Address">{supplier.address}</Descriptions.Item>
              )}
            </Descriptions>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="Items" bordered={false}>
              <Table
                columns={columns}
                dataSource={data.items || []}
                pagination={false}
                rowKey={(record) => record.product?.value || Math.random().toString()}
                summary={() => (
                  <Table.Summary>
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={4} align="right">
                        <strong>Subtotal:</strong>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={1} align="right">
                        <strong>{numberFormat(subtotal)}</strong>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                )}
              />
            </Card>
          </Col>
        </Row>

        {data.notes && (
          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            <Col span={24}>
              <Card title="Notes" bordered={false}>
                <p>{data.notes}</p>
              </Card>
            </Col>
          </Row>
        )}

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="Approval Information" bordered={false}>
              <Row gutter={16}>
                <Col span={12}>
                  <Descriptions column={1} bordered>
                    <Descriptions.Item label="Prepared By">
                      {data.preparedBy?.label || "N/A"}
                    </Descriptions.Item>
                    {data.preparedAt && (
                      <Descriptions.Item label="Prepared At">
                        {moment(data.preparedAt).format("DD MMM YYYY HH:mm")}
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Col>
                <Col span={12}>
                  <Descriptions column={1} bordered>
                    <Descriptions.Item label="Approved By">
                      {data.approvedBy?.label || "N/A"}
                    </Descriptions.Item>
                    {data.approvedAt && (
                      <Descriptions.Item label="Approved At">
                        {moment(data.approvedAt).format("DD MMM YYYY HH:mm")}
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default LocalPurchaseOrder;
