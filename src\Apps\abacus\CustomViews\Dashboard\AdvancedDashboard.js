import { Col, Row, Card } from "antd";
import React, { useEffect, useState, useRef, useCallback, memo } from "react";
import StatsGrid from "../../../../Components/Stats/StatsGrid";


import BusinessAnalysisCard from "./BusinessAnalysisCard";
import TopProductsCard from "./TopProductsCard";
import TopProductsTable from "./TopProductsTable";
import TopCategoriesTable from "./TopCategoriesTable";
import { calculateDashboardStats, generateStatsGridData } from "./utils/dashboardCalculations";
// Removed cache and performance monitoring imports for simplified implementation

import OrganisationInformation from "../../../../Components/Dashboard/OrganisationInformation";
// import POS from "./POS";

// Memoized child components for better performance
const MemoizedStatsGrid = memo(StatsGrid);
const MemoizedBusinessAnalysisCard = memo(BusinessAnalysisCard);
const MemoizedTopProductsCard = memo(TopProductsCard);
const MemoizedOrganisationInformation = memo(OrganisationInformation);

const AdvancedDashboard = (props) => {
  const { pouchDatabase, databasePrefix, modulesProperties } = props;

  // State management
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [loading, setLoading] = useState(true);

  // Refs for cleanup
  const abortControllerRef = useRef(null);

  const [incomeData, setIncomeData] = useState([
    {
      title: "Sales this month",
      icon: "receipt",
      value: 0,
      diff: 0,
    },
    {
      title: "Sales this Week",
      icon: "coin",
      value: "0",
      diff: 0,
    },
    {
      title: "Expense this month",
      icon: "discount",
      value: "0",
      diff: 0,
    },
    {
      title: "Expense this week",
      icon: "user",
      value: "0",
      diff: 0,
    },
  ]);

  // Main data loading effect - simplified
  useEffect(() => {
    let isMounted = true;

    // Abort any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

    // Fetch dashboard data
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const [invoices, expenses] = await Promise.all([
          pouchDatabase("invoices", databasePrefix).getAllData(),
          pouchDatabase("expenses", databasePrefix).getAllData()
        ]);

        if (isMounted && !signal.aborted) {
          // Process the data
          const stats = calculateDashboardStats(invoices, expenses, SELECTED_BRANCH);
          const statsGridData = generateStatsGridData(stats);

          // Update state
          setIncomeData(statsGridData);
          setLoading(false);
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          if (isMounted) {
            setLoading(false);
          }
        }
      }
    };

    fetchDashboardData();

    return () => {
      isMounted = false;
      // Abort any ongoing requests when component unmounts or dependencies change
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [pouchDatabase, databasePrefix]);

  // Handle month selection from TopProductsCard
  const handleMonthChange = (month) => {
    setSelectedMonth(month);
  };

  return (
    <Row gutter={[16, 16]}>

      {/* <POS pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} /> */}
      <Col span={24}>
        <MemoizedStatsGrid data={incomeData} loading={loading} />
      </Col>

      {/* First row of charts */}
      <Col xs={24} md={24} lg={16}>
        <MemoizedBusinessAnalysisCard
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
        />
      </Col>
      <Col xs={24} md={24} lg={8}>
        <MemoizedTopProductsCard
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
          selectedMonth={selectedMonth}
          onMonthChange={handleMonthChange}
        />
      </Col>

      {/* Second row with tables */}
      <Col xs={24} md={12}>
        <Card
          title={<span style={{ fontWeight: 600 }}>Top Products Analysis</span>}
          style={{
            height: '100%',
            border: '1px solid #d9d9d9',
            borderRadius: '8px',
          }}
          styles={{
            header: {
              backgroundColor: '#f0f5ff',
              borderBottom: '1px solid #d6e4ff',
              borderTopLeftRadius: '8px',
              borderTopRightRadius: '8px',
            }
          }}
        >
          <TopProductsTable
            pouchDatabase={pouchDatabase}
            databasePrefix={databasePrefix}
            selectedMonth={selectedMonth}
          />
        </Card>
      </Col>
      <Col xs={24} md={12}>
        <Card
          title={<span style={{ fontWeight: 600 }}>Top Categories Analysis</span>}
          style={{
            height: '100%',
            border: '1px solid #d9d9d9',
            borderRadius: '8px',
          }}
          styles={{
            header: {
              backgroundColor: '#f6ffed',
              borderBottom: '1px solid #d9f7be',
              borderTopLeftRadius: '8px',
              borderTopRightRadius: '8px',
            }
          }}
        >
          <TopCategoriesTable
            pouchDatabase={pouchDatabase}
            databasePrefix={databasePrefix}
            selectedMonth={selectedMonth}
          />
        </Card>
      </Col>

      {/* Organization Information */}
      <Col span={24}>
        <MemoizedOrganisationInformation
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
        />
      </Col>
    </Row>
  );
};

export default AdvancedDashboard;
