import React, { useState, useEffect } from "react";
import ReactDOM from "react-dom";
import { Pie } from "@ant-design/plots";
import { buffProducts } from "../../modulesProperties/utils";
import moment from "moment";

const PieChart = ({ pouchDatabase, databasePrefix, by }) => {
  const [data, setData] = useState([]);
  const config = {
    appendPadding: 10,
    legend: false,
    data,
    angleField: "value",
    colorField: "product",
    radius: 0.75,
    label: {
      // type: "spider",
      labelHeight: 30,
      content: ({ product, percent }) => `${product}\n${(percent * 100).toFixed(1)}%`,
    },
  };

  useEffect(() => {
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");
    Promise.all([
      pouchDatabase("invoices", databasePrefix).getAllData(),
      pouchDatabase("products", databasePrefix)
        .getAllData()
        .then((r) => buffProducts(r, pouchDatabase, databasePrefix, "products", SELECTED_BRANCH)),
      // pouchDatabase("withdraws", databasePrefix).getAllData(),
    ]).then((values) => {
      const invoices =
        SELECTED_BRANCH && SELECTED_BRANCH !== "none"
          ? values[0].filter(
            (i) =>
              i.branch === SELECTED_BRANCH &&
              moment(i.date).isSame(new Date(), "month")
          )
          : values[0].filter((i) => moment(i.date).isSame(new Date(), "month"));
      const products = values[1];

      let chartData = [];

      products &&
        products.forEach((p) => {
          let v = {
            product: p.name,
            value: 0,
          };
          invoices.forEach((i) => {
            i.items.forEach((item) => {
              if (item.product.value === p._id) {
                v.product = item.product.label;
                v.value +=
                  by === "quantity"
                    ? Number(item.quantity)
                    : Number(item.quantity) * item.price -
                    Number(item.quantity) * p.cost;
              }
            });
          });
          v.value > 0 && chartData.push(v);
        });

      setData(chartData.sort((a, b) => b.value - a.value).splice(0, 15));
    });
  }, []);

  return <Pie {...config} />;
};

export default PieChart;
