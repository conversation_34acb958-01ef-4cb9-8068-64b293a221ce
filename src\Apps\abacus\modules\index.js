const { categories } = require("./categories");
const { products } = require("./products");
const { branches } = require("./branches");
const { customers } = require("./customers");
const { suppliers } = require("./suppliers");
const { damaged_products } = require("./damaged_products");
const { expenses } = require("./expenses");
const { invoices } = require("./invoices");
const { product_prices } = require("./product_prices");
const { transfers } = require("./transfer");
const { quotations } = require("./quotations");
const { receipts } = require("./receipts");
const { returned_products } = require("./returned_products");
const { returned_stock } = require("./returned_stock");
const { stock_adjustments } = require("./stock_adjustments");
const { non_purchased_stock } = require("./non_purchased_stock");
const { stock_payments } = require("./stock_payments");
const { stock_purchasing } = require("./stock_purchasing");
const { stock_orders } = require("./stock_orders");
const { local_purchase_orders } = require("./local_purchase_orders");
const { teams } = require("./teams");
const { expense_categories } = require("./expense_categories");
const { requisitions } = require("./requisitions");
const { brands } = require('./brands')
const { ai } = require("../../Universal/modules/ai");

exports.modules = {
  product_management: {
    name: "Product",
    description: "Product Management",
    icon: "ShoppingOutlined",
    path: "/product_management",
    columns: [],
  },
  sales: {
    name: "Sales",
    description: "Sales",
    icon: "ShoppingCartOutlined",
    path: "/sales",
    columns: [],
  },
  inventory: {
    name: "Inventory",
    description: "Inventory",
    icon: "AppstoreOutlined",
    path: "/inventory",
    columns: [],
  },
  expenditure: {
    name: "Expenditure",
    description: "Expenditure",
    icon: "DollarOutlined",
    path: "/expenditure",
    columns: [],
  },
  partners: {
    name: "Partners",
    description: "Partners",
    icon: "TeamOutlined",
    path: "/partners",
    columns: [],
  },
  categories: categories,
  products: products,
  branches: branches,
  customers: customers,
  product_prices: product_prices,
  stock_purchasing: stock_purchasing,
  stock_orders: stock_orders,
  local_purchase_orders: local_purchase_orders,
  stock_payments: stock_payments,
  transfers: transfers,
  returned_products: returned_products,
  returned_stock: returned_stock,
  stock_adjustments: stock_adjustments,
  non_purchased_stock: non_purchased_stock,
  damaged_products: damaged_products,
  suppliers: suppliers,
  expenses: expenses,
  expense_categories: expense_categories,
  requisitions: requisitions,
  invoices: invoices,
  receipts: receipts,
  quotations: quotations,
  teams: teams,
  brands: brands,
  ai: ai,
};
