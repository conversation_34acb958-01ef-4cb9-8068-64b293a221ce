import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { message, Popconfirm } from "antd";
import React, { useRef } from "react";
import {
  ShoppingCartOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from "@ant-design/icons";
import LocalPurchaseOrder from "../CustomViews/LocalPurchaseOrder";
import { refreshRecordData } from "../../../Utils/RecordRefreshUtility";

const local_purchase_orders = {
  CustomView: (data) => <LocalPurchaseOrder {...data} />,

  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
      updateOptimisticRecord,
    } = props;

    // Use the utility function for refreshing record data
    const refreshRecord = async (updatedRecord) => {
      return await refreshRecordData(
        updatedRecord,
        updateOptimisticRecord,
        pouchDatabase,
        collection,
        databasePrefix
      );
    };

    // Get current user
    const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));

    // Determine if the LPO can be approved, converted to pending, or converted to stock purchasing
    const isDraft = record.status === "draft";
    const isApproved = record.status === "approved";
    const isPending = record.status === "pending";
    const isConverted = record.status === "converted";

    const actionMenus = [];

    // Approve LPO action - only for draft orders
    if (isDraft) {
      actionMenus.push({
        key: "approve",
        name: (
          <Popconfirm
            title="Approve Local Purchase Order"
            description="Are you sure you want to approve this LPO?"
            onConfirm={async () => {
              try {
                const updatedRecord = {
                  ...record,
                  status: "approved",
                  approvedBy: {
                    value: currentUser._id,
                    label: `${currentUser.first_name} ${currentUser.last_name}`,
                    ...currentUser
                  },
                  approvedAt: new Date().toISOString()
                };

                await pouchDatabase(collection, databasePrefix).saveDocument(updatedRecord, CRUD_USER);

                // Refresh record for immediate UI updates
                await refreshRecord(updatedRecord);

                message.success("Local Purchase Order approved");
              } catch (error) {
                message.error("Failed to approve Local Purchase Order");
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <a key="button" type="primary">
              <CheckCircleOutlined /> Approve LPO
            </a>
          </Popconfirm>
        ),
      });
    }

    // Convert to Pending Order - only for approved LPOs
    if (isApproved) {
      actionMenus.push({
        key: "convertToPending",
        name: (
          <BetaSchemaForm
            formRef={action}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Convert to Stock Order",
              },
            }}
            modalProps={{ centered: true }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <ClockCircleOutlined /> Convert to Stock Order
              </a>
            }
            title={"Convert LPO to Stock Order"}
            destroyOnClose={true}
            layoutType="ModalForm"
            initialValues={{
              date: record.date,
              supplier: record.supplier,
              items: record.items,
              notes: `Generated from LPO #${record.lpo_number || record._id}`,
              status: "approved", // Set as approved so it can be converted to stock purchasing
              lpoId: record._id,
              branch: record.branch,
              expected_delivery_date: record.expected_delivery_date
            }}
            onFinish={async (values) => {
              try {
                // Create a stock order entry with pending status
                const stockOrderData = {
                  ...values,
                  status: "approved", // Set as approved so it can be converted to stock purchasing
                  lpoId: record._id,
                };

                // Save the stock order
                const stockOrderResult = await pouchDatabase(
                  "stock_orders",
                  databasePrefix
                ).saveDocument(stockOrderData, CRUD_USER);

                // Update the LPO status to pending
                const updatedRecord = {
                  ...record,
                  status: "pending",
                  stockOrderId: stockOrderResult._id,
                  pendingAt: new Date().toISOString()
                };

                await pouchDatabase(collection, databasePrefix).saveDocument(updatedRecord, CRUD_USER);

                // Refresh record for immediate UI updates
                await refreshRecord(updatedRecord);

                message.success("LPO converted to Stock Order successfully");
                return true;
              } catch (error) {
                console.error("Error converting to Stock Order:", error);
                message.error("Failed to convert to Stock Order");
                return false;
              }
            }}
            columns={modules.stock_orders.columns.filter(
              (col) => !["status"].includes(col.dataIndex)
            )}
          />
        ),
      });
    }

    // Generate Stock Purchasing entry - only for pending orders
    if (isPending) {
      actionMenus.push({
        key: "generateStockPurchasing",
        name: (
          <BetaSchemaForm
            formRef={action}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Convert to Stock Purchasing",
              },
            }}
            modalProps={{ centered: true }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <ShoppingCartOutlined /> Convert to Stock Purchasing
              </a>
            }
            title={"Convert to Stock Purchasing"}
            destroyOnClose={true}
            layoutType="ModalForm"
            initialValues={{
              date: record.date,
              supplier: record.supplier,
              items: record.items,
              notes: `Generated from LPO #${record.lpo_number || record._id}`,
              lpoId: record._id,
              branch: record.branch,
              expected_delivery_date: record.expected_delivery_date
            }}
            onFinish={async (values) => {
              try {
                // Create the stock purchasing entry
                const stockPurchasingData = {
                  ...values,
                  lpoId: record._id,
                };

                // Save the stock purchasing entry
                const stockPurchasingResult = await pouchDatabase(
                  "stock_purchasing",
                  databasePrefix
                ).saveDocument(stockPurchasingData, CRUD_USER);

                // Update the LPO status to converted
                const updatedRecord = {
                  ...record,
                  status: "converted",
                  stockPurchasingId: stockPurchasingResult._id,
                  convertedAt: new Date().toISOString()
                };

                await pouchDatabase(collection, databasePrefix).saveDocument(updatedRecord, CRUD_USER);

                // Refresh record for immediate UI updates
                await refreshRecord(updatedRecord);

                message.success("LPO converted to Stock Purchasing successfully");
                return true;
              } catch (error) {
                console.error("Error converting to Stock Purchasing:", error);
                message.error("Failed to convert to Stock Purchasing");
                return false;
              }
            }}
            columns={modules.stock_purchasing.columns.filter(
              (col) => col.dataIndex !== "status"
            )}
          />
        ),
      });

      // Add option to generate requisition
      actionMenus.push({
        key: "generateRequisition",
        name: (
          <BetaSchemaForm
            formRef={action}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Generate Requisition",
              },
            }}
            modalProps={{ centered: true }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <CheckCircleOutlined /> Generate Requisition
              </a>
            }
            title={"Generate Requisition from LPO"}
            destroyOnClose={true}
            layoutType="ModalForm"
            initialValues={{
              date: new Date().toISOString().split('T')[0],
              supplier: record.supplier,
              purpose: `Payment for LPO #${record.lpo_number || record._id}`,
              amount: record.items?.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0,
              branch: record.branch
            }}
            onFinish={async (values) => {
              try {
                // Get current user
                const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));

                // Create the requisition
                const requisitionData = {
                  ...values,
                  lpoId: record._id,
                  receiver: {
                    value: currentUser._id,
                    label: `${currentUser.first_name} ${currentUser.last_name}`,
                    ...currentUser
                  },
                };

                // Save the requisition
                await pouchDatabase(
                  "requisitions",
                  databasePrefix
                ).saveDocument(requisitionData, CRUD_USER);

                // Refresh record for immediate UI updates
                await refreshRecord(record);

                message.success("Requisition generated successfully");
                return true;
              } catch (error) {
                console.error("Error generating requisition:", error);
                message.error("Failed to generate requisition");
                return false;
              }
            }}
            columns={modules.requisitions.columns.filter(
              (col) => !["authoriser", "approvedBy"].includes(col.dataIndex)
            )}
          />
        ),
      });
    }

    return actionMenus.length > 0 ? (
      <TableDropdown key="actionGroup" menus={actionMenus}></TableDropdown>
    ) : null;
  },

  // Set current user as preparedBy and attach branch when saving a document
  beforeSave: (data, CRUD_USER) => {
    const currentUser = JSON.parse(localStorage.getItem("LOCAL_STORAGE_USER"));
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

    // For new documents (no _id or _rev)
    if (!data._id || !data._rev) {
      return {
        ...data,
        // Automatically attach branch
        branch: SELECTED_BRANCH,
        // Set preparedBy to current user
        preparedBy: {
          value: currentUser._id,
          label: `${currentUser.first_name} ${currentUser.last_name}`,
          ...currentUser
        },
        preparedAt: new Date().toISOString()
      };
    }

    // For existing documents, ensure branch is still attached
    return {
      ...data,
      branch: data.branch || SELECTED_BRANCH
    };
  },

  // Buffer results to add calculated fields
  buffResults: (results) => {
    return results.map(doc => {
      const subtotal = doc.items?.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0;
      return {
        ...doc,
        subtotal,
        total: subtotal
      };
    });
  },
};

export default local_purchase_orders;
