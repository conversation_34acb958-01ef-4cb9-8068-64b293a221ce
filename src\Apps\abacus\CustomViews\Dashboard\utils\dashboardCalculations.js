import moment from "moment";
import { filterByBranch } from "../../../../../Utils/branchFiltering";

/**
 * Calculate dashboard statistics for sales and expenses
 * @param {Array} invoices - Array of invoice documents
 * @param {Array} expenses - Array of expense documents
 * @param {String} selectedBranch - Selected branch ID or null
 * @returns {Object} Object containing all calculated statistics and diff values
 */
export const calculateDashboardStats = (invoices, expenses, selectedBranch) => {
  const currentDate = new Date();
  const currentMonth = moment(currentDate);
  const previousMonth = moment(currentDate).subtract(1, 'month');
  const currentWeek = moment(currentDate);
  const previousWeek = moment(currentDate).subtract(1, 'week');

  // Filter data by branch
  const branchFilteredInvoices = filterByBranch(invoices || [], selectedBranch);
  const branchFilteredExpenses = filterByBranch(expenses || [], selectedBranch);

  // Helper function to filter by date
  const filterByDate = (data, date, dateUnit) => {
    return data.filter(i => i.date && moment(i.date).isSame(date, dateUnit));
  };

  // Helper function to calculate sales total from invoices
  const calculateSalesTotal = (invoiceList) => {
    return invoiceList.reduce((p, c) => {
      if (!c.items || !Array.isArray(c.items)) return p;
      return p + c.items.reduce((pv, cv) => {
        const price = parseFloat(cv.price) || 0;
        const quantity = parseFloat(cv.quantity) || 0;
        return pv + (price * quantity);
      }, 0);
    }, 0);
  };

  // Helper function to calculate expense total
  const calculateExpenseTotal = (expenseList) => {
    return expenseList.reduce((p, c) => p + Number(c.amount || 0), 0);
  };

  // Calculate percentage change
  const calculatePercentageChange = (current, previous) => {
    return previous !== 0 ? ((current - previous) / previous) * 100 : 0;
  };

  // Current month sales
  const filteredInvoicesByMonth = filterByDate(branchFilteredInvoices, currentMonth, "month");
  const sales_this_month = calculateSalesTotal(filteredInvoicesByMonth);

  // Previous month sales
  const filteredInvoicesByPrevMonth = filterByDate(branchFilteredInvoices, previousMonth, "month");
  const sales_prev_month = calculateSalesTotal(filteredInvoicesByPrevMonth);

  // Calculate month-over-month percentage change for sales
  const sales_month_diff = calculatePercentageChange(sales_this_month, sales_prev_month);

  // Current week sales
  const filteredInvoicesByWeek = filterByDate(branchFilteredInvoices, currentWeek, "week");
  const sales_this_week = calculateSalesTotal(filteredInvoicesByWeek);

  // Previous week sales
  const filteredInvoicesByPrevWeek = filterByDate(branchFilteredInvoices, previousWeek, "week");
  const sales_prev_week = calculateSalesTotal(filteredInvoicesByPrevWeek);

  // Calculate week-over-week percentage change for sales
  const sales_week_diff = calculatePercentageChange(sales_this_week, sales_prev_week);

  // Current month expenses
  const filteredExpensesByMonth = filterByDate(branchFilteredExpenses, currentMonth, "month");
  const expense_this_month = calculateExpenseTotal(filteredExpensesByMonth);

  // Previous month expenses
  const filteredExpensesByPrevMonth = filterByDate(branchFilteredExpenses, previousMonth, "month");
  const expense_prev_month = calculateExpenseTotal(filteredExpensesByPrevMonth);

  // Calculate month-over-month percentage change for expenses
  const expense_month_diff = calculatePercentageChange(expense_this_month, expense_prev_month);

  // Current week expenses
  const filteredExpensesByWeek = filterByDate(branchFilteredExpenses, currentWeek, "week");
  const expense_this_week = calculateExpenseTotal(filteredExpensesByWeek);

  // Previous week expenses
  const filteredExpensesByPrevWeek = filterByDate(branchFilteredExpenses, previousWeek, "week");
  const expense_prev_week = calculateExpenseTotal(filteredExpensesByPrevWeek);

  // Calculate week-over-week percentage change for expenses
  const expense_week_diff = calculatePercentageChange(expense_this_week, expense_prev_week);

  // Log calculations for debugging
  console.log('Dashboard Stats - Sales this month:', sales_this_month, 'Sales prev month:', sales_prev_month, 'Diff:', sales_month_diff);
  console.log('Dashboard Stats - Sales this week:', sales_this_week, 'Sales prev week:', sales_prev_week, 'Diff:', sales_week_diff);
  console.log('Dashboard Stats - Expense this month:', expense_this_month, 'Expense prev month:', expense_prev_month, 'Diff:', expense_month_diff);
  console.log('Dashboard Stats - Expense this week:', expense_this_week, 'Expense prev week:', expense_prev_week, 'Diff:', expense_week_diff);

  // Return all calculated values
  return {
    sales_this_month,
    sales_prev_month,
    sales_month_diff,
    sales_this_week,
    sales_prev_week,
    sales_week_diff,
    expense_this_month,
    expense_prev_month,
    expense_month_diff,
    expense_this_week,
    expense_prev_week,
    expense_week_diff
  };
};

/**
 * Generate StatsGrid data from calculated statistics
 * @param {Object} stats - Object containing calculated statistics
 * @returns {Array} Array of stat objects for StatsGrid component
 */
export const generateStatsGridData = (stats) => {
  return [
    {
      title: "Sales this month",
      icon: "money",
      value: stats.sales_this_month,
      diff: stats.sales_month_diff,
      diffLabel: "prev month",
      theme: "primary",
      prefix: "UGX"
    },
    {
      title: "Sales this Week",
      icon: "coin",
      value: stats.sales_this_week,
      diff: stats.sales_week_diff,
      diffLabel: "prev week",
      theme: "success",
      prefix: "UGX"
    },
    {
      title: "Expense this month",
      icon: "cash",
      value: stats.expense_this_month,
      diff: stats.expense_month_diff,
      diffLabel: "prev month",
      theme: "danger",
      prefix: "UGX"
    },
    {
      title: "Expense this week",
      icon: "discount",
      value: stats.expense_this_week,
      diff: stats.expense_week_diff,
      diffLabel: "prev week",
      theme: "warning",
      prefix: "UGX"
    }
  ];
};
