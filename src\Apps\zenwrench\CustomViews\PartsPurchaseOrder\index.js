import React, { useEffect, useState } from "react";
import ReferenceNumber from "../../../../Components/ReferenceNumber";
// import "./css.css";
import PouchDb from "pouchdb-browser";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import PrintComponents from "react-print-components";
import { FloatButton, Flex, Divider, Tag } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";
import DocumentFooter from "../DocumentFooter";

const PartsPurchaseOrder = (props) => {
  const { data, title = "Local Purchase Order", bottomText = "" } = props;

  // Debug: Log the data to see what's being passed
  console.log("Purchase Order Data:", data);

  // Extract user information for prepared by and approved by
  const preparedByUser = data.preparedBy || data.entrant || data.createdBy;
  const approvedByUser = data.approvedBy;


  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS")) || {};

  const subTotal = data.items?.reduce(
    (acc, item) => acc + item.price * item.quantity,
    0
  ) || 0;
  const tax = data.taxable ? subTotal * 0.18 : 0;
  const grandTotal = subTotal + tax;

  const [company, setCompany] = useState(null);
  const [job, setJob] = useState(null);
  const [supplier, setSupplier] = useState(null);
  const [partsAndServices, setPartsAndServices] = useState(null);
  const [units, setUnits] = useState([]);

  // Fetch parts and services information
  useEffect(() => {
    const loadPartsAndServices = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const partsAndServicesData = await pouchDatabase("parts_and_services", databasePrefix).getAllData();
          setPartsAndServices(partsAndServicesData.map(doc => ({ doc })));
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setPartsAndServices([]);
        }
      } catch (error) {
        console.error("Error loading parts and services:", error);
        setPartsAndServices([]);
      }
    };

    loadPartsAndServices();
  }, [pouchDatabase, databasePrefix]);

  // Fetch job information if available
  useEffect(() => {
    const loadJob = async () => {
      if (!data || !data.job) return;

      const jobId = data.job.value || data.job;
      if (!jobId) return;

      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const jobData = await pouchDatabase("jobs", databasePrefix).getDocument(jobId);
          setJob(jobData);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
        }
      } catch (error) {
        console.error("Error fetching job:", error);
      }
    };

    loadJob();
  }, [data, pouchDatabase, databasePrefix]);

  // Fetch company information
  useEffect(() => {
    const loadCompanyData = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            let comp = organizationsData[0];

            if (comp._attachments && comp._attachments.logo) {
              try {
                const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
                comp.logo = logoBlob;
                setCompany(comp);
              } catch (logoError) {
                console.warn("Error fetching logo:", logoError);
                setCompany(comp);
              }
            } else {
              setCompany(comp);
            }
          } else {
            console.warn("No organization data found");
            setCompany({
              name: "Your Company",
              phone: "",
              alternative_phone: "",
              email: "",
              website: "",
              address: ""
            });
          }
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setCompany({
            name: "Your Company",
            phone: "",
            alternative_phone: "",
            email: "",
            website: "",
            address: ""
          });
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        setCompany({
          name: "Your Company",
          phone: "",
          alternative_phone: "",
          email: "",
          website: "",
          address: ""
        });
      }
    };

    loadCompanyData();
  }, [pouchDatabase, databasePrefix]);

  // Fetch supplier information
  useEffect(() => {
    const loadSupplier = async () => {
      if (!data || !data.supplier) return;

      const supplierId = data.supplier.value || data.supplier;
      if (!supplierId) return;

      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const supplierData = await pouchDatabase("suppliers", databasePrefix).getDocument(supplierId);
          setSupplier(supplierData);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
        }
      } catch (error) {
        console.error("Error fetching supplier:", error);
      }
    };

    loadSupplier();
  }, [data, pouchDatabase, databasePrefix]);

  // Fetch units information
  useEffect(() => {
    const loadUnits = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const unitsData = await pouchDatabase("units", databasePrefix).getAllData();
          setUnits(unitsData);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setUnits([]);
        }
      } catch (error) {
        console.error("Error loading units:", error);
        setUnits([]);
      }
    };

    loadUnits();
  }, [pouchDatabase, databasePrefix]);

  // Create empty rows to fill the table
  const emptyRows = [];
  for (
    let index = 0;
    index < (appSettings?.documentHeader === "V2" ? 28 : 24) - (data.items?.length || 0);
    index++
  ) {
    emptyRows.push({});
  }

  // Get status color based on status value
  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "orange";
      case "approved":
        return "green";
      case "received":
        return "blue";
      case "cancelled":
        return "red";
      default:
        return "default";
    }
  };

  if (!company || !supplier) {
    return null; // Don't render until we have all the required data
  }

  return (
    <div className="tm_invoice_wrap">
      <div className="tm_invoice tm_style1" id="tm_download_section">
        <div className="tm_invoice_in">
          {appSettings &&
            appSettings.documentHeader &&
            appSettings.documentHeader === "V2" && (
              <>
                <Flex vertical={false} justify="space-between">
                  <RenderBlob blob={company.logo} size={150} />
                  <div>
                    <strong style={{ fontSize: 25, fontWeight: "bold" }}>
                      {title.toUpperCase()}
                    </strong>
                    <p className="tm_invoice_number tm_m0 tm_f11">
                      PO No: <b className="tm_primary_color">
                        {data.referenceNumber ? (
                          <ReferenceNumber value={data.referenceNumber} />
                        ) : (
                          data._id
                        )}
                      </b>
                      <br /> Date:{" "}
                      <b className="tm_primary_color">
                        {moment(data.date).format("DD MMM YYYY")}
                      </b>
                      <br />
                      Status: <Tag color={getStatusColor(data.status)}>{data.status?.toUpperCase() || "PENDING"}</Tag>
                    </p>
                  </div>
                  <p className="tm_mb2 tm_f12">
                    {company.address}
                    <br />
                    Tel: {company.phone} / {company.alternative_phone} <br />
                    Email: {company.email}
                  </p>
                </Flex>
                <Divider />
                <Flex vertical={false} justify="space-between">
                  <div className="tm_mb2 tm_f10">
                    <p className="tm_mb2">
                      <b className="tm_primary_color">Supplier:</b>
                    </p>
                    <p>
                      <b style={{ fontSize: 15 }}>{supplier.name}</b>
                      <br />
                      {supplier.phone}{" "}
                      {supplier.alternative_phone &&
                        `/ ${supplier.alternative_phone}`}{" "}
                      <br />
                      {supplier.email && (
                        <>
                          {supplier.email} <br />
                        </>
                      )}
                      {supplier.address && (
                        <>
                          {supplier.address} <br />
                        </>
                      )}
                    </p>
                  </div>
                  {job && (
                    <div className="tm_mb2 tm_f10">
                      <p className="tm_mb2">
                        <b className="tm_primary_color">Job Reference:</b>
                      </p>
                      <p>
                        Job ID: <b>
                          {job.referenceNumber ? (
                            <ReferenceNumber value={job.referenceNumber} />
                          ) : (
                            job._id
                          )}
                        </b>
                        <br />
                        {job.vehicle && (
                          <>Vehicle: {job.vehicle.label}<br /></>
                        )}
                        {job.customer && (
                          <>Customer: {job.customer.label}<br /></>
                        )}
                      </p>
                    </div>
                  )}
                </Flex>
              </>
            )}

          {(!appSettings?.documentHeader ||
            appSettings?.documentHeader === "V1") && (
              <Flex vertical={true}>
                <center>
                  <RenderBlob blob={company.logo} size={250} /><br /><br /><br />
                  <strong style={{ fontSize: 25, fontWeight: "bold" }}>
                    {title.toUpperCase()}
                  </strong>
                  <br /><br />
                </center>
                <Flex vertical={false} justify="space-between">
                  <div>
                    <p className="tm_mb2 tm_f12">
                      <b className="tm_primary_color">Supplier:</b>
                    </p>
                    <p>
                      <b style={{ fontSize: 15 }}>{supplier.name}</b>
                      <br />
                      {supplier.phone}{" "}
                      {supplier.alternative_phone &&
                        `/ ${supplier.alternative_phone}`}{" "}
                      <br />
                      {supplier.email && (
                        <>
                          {supplier.email} <br />
                        </>
                      )}
                      {supplier.address && (
                        <>
                          {supplier.address} <br />
                        </>
                      )}
                      {supplier._id && (
                        <>
                          Supplier ID : <strong>
                            {supplier.referenceNumber ? (
                              <ReferenceNumber value={supplier.referenceNumber} />
                            ) : (
                              supplier._id
                            )}
                            <br />
                          </strong>
                        </>
                      )}
                      <hr />
                      {job && (
                        <>
                          Job ID: <b>
                            {job.referenceNumber ? (
                              <ReferenceNumber value={job.referenceNumber} />
                            ) : (
                              job._id
                            )}
                          </b>
                          <br />
                          {job.vehicle && (
                            <>Vehicle: {job.vehicle.label}<br /></>
                          )}
                        </>
                      )}
                    </p>
                  </div>
                  <div>

                    <p className="tm_invoice_number tm_m0">
                      PO No: <b className="tm_primary_color">
                        {data.referenceNumber ? (
                          <ReferenceNumber value={data.referenceNumber} />
                        ) : (
                          data._id
                        )}
                      </b>
                    </p>
                    <p className="tm_invoice_date tm_m0">
                      Date:{" "}
                      <b className="tm_primary_color">
                        {moment(data.date).format("DD MMM YYYY")}
                      </b>
                    </p>
                    <p className="tm_invoice_number tm_m0">
                      Status: <Tag color={getStatusColor(data.status)}>{data.status?.toUpperCase() || "PENDING"}</Tag>
                    </p>
                  </div>
                </Flex>
              </Flex>
            )}

          <div className="tm_table tm_style">
            <center style={{ minHeight: 440 }}>
              <div className="tm_border">
                <div className="main">
                  <table>
                    <thead>
                      <tr>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_center">
                          S/N
                        </th>
                        <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                          Description
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_left">
                          Qty
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Rate
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.items && data.items.map((item, index) => {


                        const PartUnit = partsAndServices && partsAndServices.find(
                          (part) => part.id === (item.item?.value || item.item)
                        );

                        const unit =
                          PartUnit && PartUnit.doc && PartUnit.doc.unit
                            ? units.find(
                              (u) => u._id === PartUnit.doc.unit.value
                            )?.abbreviation
                            : "";

                        return (
                          <tr className="main" key={index}>
                            <td className="tm_width_1 tm_text_center">
                              {index + 1}
                            </td>
                            <td className="tm_width_3">{item.item?.label || item.item || "Part"}</td>
                            <td className="tm_width_1 tm_text_left">
                              {item.quantity}
                            </td>
                            <td className="tm_width_1 tm_text_right">
                              {numberFormat(item.price)}
                            </td>
                            <td className="tm_width_1 tm_text_right">
                              {numberFormat(item.price * item.quantity)}
                            </td>
                          </tr>
                        );
                      })}
                      {emptyRows.map((_, index) => (
                        <tr className="main" key={`empty-${index}`}>
                          <td className="tm_width_1 tm_text_center">
                            {" "}
                            &nbsp;
                          </td>
                          <td className="tm_width_3"></td>
                          <td className="tm_width_1 tm_text_left"></td>
                          <td className="tm_width_1 tm_text_right"></td>
                          <td className="tm_width_1 tm_text_right"></td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </center>
            <div className="tm_invoice_footer tm_mb20 tm_m0_md">
              <div className="tm_left_footer">
                <p className="tm_mb2">
                  <b>Notes:</b> <br />
                  {data.notes || "No additional notes"}
                </p>
              </div>
              <div className="tm_right_footer">
                <table>
                  <tbody>
                    <tr>
                      <td className="tm_width_3 tm_primary_color tm_border_none tm_bold tm_f15">
                        Subtotal
                      </td>
                      <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold tm_f15">
                        {appSettings?.currency || "UGX"} {numberFormat(subTotal)}
                      </td>
                    </tr>
                    {data.taxable && (
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                          Tax <span className="tm_ternary_color">(18%)</span>
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                          {appSettings?.currency || "UGX"} {numberFormat(tax)}
                        </td>
                      </tr>
                    )}
                    <tr className="tm_border_top tm_border_bottom">
                      <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color">
                        Grand Total{" "}
                      </td>
                      <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color tm_text_right">
                        {appSettings?.currency || "UGX"} {numberFormat(grandTotal)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="tm_invoice_footer">
              <div className="tm_left_footer">
                <p className="tm_mb2">
                  <b>Prepared By:</b> <br />
                  {preparedByUser ? (
                    preparedByUser.label ||
                    `${preparedByUser.first_name || ''} ${preparedByUser.last_name || ''}`.trim() ||
                    preparedByUser.name ||
                    "System"
                  ) : "System"}
                </p>
              </div>
              <div className="tm_right_footer">
                <p className="tm_mb2">
                  <b>Approved By:</b> <br />
                  {approvedByUser ? (
                    approvedByUser.label ||
                    `${approvedByUser.first_name || ''} ${approvedByUser.last_name || ''}`.trim()
                  ) : (data.status === 'approved' ? 'Approved' : '____________________')}
                </p>
              </div>
            </div>

            {/* Document Footer */}
            <DocumentFooter company={company} bottomText={bottomText} />
          </div>
        </div>
      </div>
    </div>
  );
};

const PrintablePartsPurchaseOrder = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <PartsPurchaseOrder {...props} />
      </PrintComponents>
      <PartsPurchaseOrder {...props} />
    </>
  );
};

export default PrintablePartsPurchaseOrder;