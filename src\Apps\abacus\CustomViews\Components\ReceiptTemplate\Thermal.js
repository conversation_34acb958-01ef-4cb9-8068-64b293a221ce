import React from "react";
import "./thermal.css";
import moment from "moment";
import { numberFormat } from "../../../../../Utils/functions";
import RenderBlob from "../../../../../Components/RenderBlob";

const Thermal = ({ company, data, documentTitle }) => {
  const Subtotal = data.invoice.items.reduce((acc, item) => acc + item.price * item.quantity, 0);
  const amountReceived = data.amountReceived || 0;
  const balance = Subtotal - amountReceived;

  return (
    <div className="tm_pos_invoice_wrap">
      <div className="tm_pos_invoice_top">
        {company.logo && <RenderBlob blob={company.logo} size={100} />}
        <div className="tm_pos_company_name">{company.name}</div>
        <div className="tm_pos_company_address">{company.address}</div>
        <div className="tm_pos_company_mobile">{company.email}</div>
      </div>
      <div className="tm_pos_invoice_body">
        <div className="tm_pos_invoice_heading">
          <span><strong>{documentTitle}</strong></span>
        </div>
        <table>
          <tr>
            <td>
              <p style={{ fontSize: "10px" }}>
                {data.client.name?.name || data.client.name}
                {data.client.name?.sur_name} {data.client.name?.first_name}
              </p>
            </td>
            <td style={{ textAlign: "right" }}>
              <p style={{ fontSize: "10px" }}>
                {moment(data.date).format("Do MMM YY")}<br />
                #<strong>{data.id}</strong>
              </p>
            </td>
          </tr>
        </table>

        <table className="tm_pos_invoice_table" style={{ fontSize: "10px" }}>
          <thead>
            <tr>
              <th>Item</th>
              <th>Price</th>
              <th>Qty</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            {data.invoice.items.map((item) => (
              <tr key={item.id}>
                <td>{item.product.label.split("-")[0]}</td>
                <td>{item.quantity}</td>
                <td>{numberFormat(item.price)}</td>
                <td>{numberFormat(item.price * item.quantity)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="tm_bill_list" style={{ fontSize: "10px" }}>
          <div className="tm_bill_list_in">
            <div className="tm_bill_title">Subtotal:</div>
            <div className="tm_bill_value">{numberFormat(Subtotal)}</div>
          </div>

          <div className="tm_bill_list_in">
            <div className="tm_bill_title">Amount Received:</div>
            <div className="tm_bill_value">{numberFormat(amountReceived)}</div>
          </div>

          <div className="tm_invoice_seperator"></div>

          <div className="tm_bill_list_in" style={{ fontWeight: "bold" }}>
            <div className="tm_bill_title">Balance Due:</div>
            <div className="tm_bill_value">{numberFormat(balance)}</div>
          </div>
        </div>

        <div className="tm_pos_sample_text">
          {data.operator?.title}: <strong>{data.operator?.name}</strong>
        </div>
        <div className="tm_pos_invoice_footer">Thank you for your business!</div>
      </div>
    </div>
  );
};

export default Thermal;