import React, { useEffect, useState } from "react";
import ReceiptTemplate from "../../../Universal/CustomViews/ReceiptTemplate";
import PouchDb from "pouchdb-browser";

const StockPayment = ({ data }) => {
  const [supplier, setSupplier] = useState(null);
  const [invoice, setInvoice] = useState(null);
  const [receipt, setReceipt] = useState(null);

  useEffect(() => {
    const guestDB = new PouchDb("suppliers");
    data.order && invoice && invoice.supplier
      ? guestDB.get(invoice.supplier.value).then((data) => {
          setSupplier(data);
        })
      : setSupplier(null);
  });

  useEffect(() => {
    const invoiceDB = new PouchDb("stock_purchasing");
    data.order
      ? invoiceDB.get(data.order._id).then((data) => {
          setInvoice(data);
        })
      : setInvoice(null);
  });

  useEffect(() => {
    const receiptDB = new PouchDb("stock_payments");
    data.order
      ? receiptDB.allDocs({ include_docs: true }).then((res) => {
          const fliteredData = res.rows.filter(
            (doc) => doc.doc.order && doc.doc.order._id === data.order._id
          );
          
          setReceipt(fliteredData);
        })
      : setReceipt(null);
  });

  

  const newdata = {
    ...data,
    date: data.date,
    client: supplier,
    paid:
      receipt && receipt.length > 0
        ? receipt.reduce((previous, current) => {
            return previous + parseInt(current.doc.amount);
          }, 0)
        : 0,
    operator: { title: "Cashier", name: data.entrant.label },
    invoice: {
      ...invoice,
      items: invoice
        ? [
            ...invoice.items.map((item) => {
              return {
                name: item.product.label.split(" - ")[0],
                price: item.price,
                quantity: item.quantity,
              };
            }),
          ]
        : [],
    },
  };
  return (
    <div>
      <ReceiptTemplate data={newdata} documentTitle={"Payment Voucher"} />
    </div>
  );
};

export default StockPayment;
