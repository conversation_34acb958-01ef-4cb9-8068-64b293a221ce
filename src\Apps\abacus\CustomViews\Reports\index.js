import React, { lazy, Suspense } from 'react';
import { Tabs, Spin } from 'antd';
import { ReportsProvider } from './ReportsContext';

// Lazy load report components
const Sales = lazy(() => import('./Sales'));
const Expense = lazy(() => import('./Expense'));
const Profit = lazy(() => import('./Profit'));
const ProductList = lazy(() => import('./ProductList'));
const Purchases = lazy(() => import('./Purchases'));
const Inventory = lazy(() => import('./Inventory'));
const CategorySales = lazy(() => import('./CategorySales'));

const Reports = ({ pouchDatabase, databasePrefix }) => {
  const items = [
    {
      key: "1",
      label: `Product List`,
      children: (
        <Suspense fallback={<Spin />}>
          <ProductList />
        </Suspense>
      ),
    },
    {
      key: "8",
      label: `Inventory Report`,
      children: (
        <Suspense fallback={<Spin />}>
          <Inventory />
        </Suspense>
      ),
    },
    {
      key: "2",
      label: `Profit/Loss Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Profit />
        </Suspense>
      ),
    },
    {
      key: "3",
      label: `Sales Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Sales pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />
        </Suspense>
      ),
    },
    {
      key: "6",
      label: `Purchase Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Purchases />
        </Suspense>
      ),
    },
    {
      key: "11",
      label: `Category Sales Report`,
      children: (
        <Suspense fallback={<Spin />}>
          <CategorySales />
        </Suspense>
      ),
    },
    {
      key: "4",
      label: `Expense Ledger`,
      children: (
        <Suspense fallback={<Spin />}>
          <Expense />
        </Suspense>
      ),
    },
  ];

  return (
    <ReportsProvider pouchDatabase={pouchDatabase} databasePrefix={databasePrefix}>
      <Tabs defaultActiveKey="1" items={items} />
    </ReportsProvider>
  );
};

export default Reports;
