import React, { useState, useEffect } from "react";
import ReactDOM from "react-dom";
import { Pie } from "@ant-design/plots";
import { buffProducts } from "../../modulesProperties/utils";
import moment from "moment";

const PieChart = ({ by, receipts, eventsReceipts, ordersReceipts }) => {
  let chartData = [
    {
      depertment: "Rooms",
      value: receipts
        .filter((r) => moment(r.createdAt).isSame(new Date(), "week"))
        .reduce((p, c) => p + Number(c.amount), 0),
    },
    {
      depertment: "Bistro",
      value: ordersReceipts
        .filter((r) => moment(r.createdAt).isSame(new Date(), "week"))
        .reduce((p, c) => p + Number(c.amount), 0),
    },
    {
      depertment: "Events",
      value: eventsReceipts
        .filter((r) => moment(r.createdAt).isSame(new Date(), "week"))
        .reduce((p, c) => p + Number(c.amount), 0),
    },
  ];
  const config = {
    appendPadding: 10,
    legend: false,
    data: chartData,
    angleField: "value",
    colorField: "depertment",
    radius: 0.75,
    color: ["#1677ff", "#ff4d4f", "#fadb14"],
    label: {
      // type: "spider",
      labelHeight: 30,
      content: ({ depertment, percent }) => `${depertment}\n${(percent * 100).toFixed(1)}%`,
    },
  };

  return <Pie {...config} />;
};

export default PieChart;
