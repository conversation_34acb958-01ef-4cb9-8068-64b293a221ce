import {
  Descriptions,
  Divider,
  Space,
  Typography,
  Button,
  Tabs,
  Row,
  Col,
  Image,
  Card,
  Avatar,
  Tag,
  Timeline,
  Badge,
  Tooltip,
  FloatButton,
  Empty,
  Skeleton,
  Flex,
} from "antd";
import { PageHeader } from "@ant-design/pro-layout";
import { StatisticCard } from "@ant-design/pro-components";
import React, { useEffect, useState, useMemo } from "react";
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  IdcardOutlined,
  PrinterOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  HistoryOutlined,
  RiseOutlined,
  FallOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import ViewTable from "../../../../Components/ViewTable";
import PrintComponents from "react-print-components";
import Statement from "./Statement";
import moment from "moment";
import PouchDB from "pouchdb";
import { numberFormat } from "../../../../Utils/functions";
import "./styles.css";

const { Text, Link, Title, Paragraph } = Typography;
const { Statistic } = StatisticCard;

const Customer = (props) => {
  const customer = props.data;
  const [dp, setDp] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [receipts, setReceipts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  const sharedProps = {
    modules: props.modules,
    modulesProperties: props.modulesProperties,
    databasePrefix: props.databasePrefix,
    pouchDatabase: props.pouchDatabase,
    userPermissions: props.userPermissions,
    filterID: { column: "customer", id: props.data._id },
  };

  // Fetch customer data, invoices, and receipts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch invoices
        const invoicesDB = new PouchDB("invoices");
        const invoicesResult = await invoicesDB.allDocs({ include_docs: true });
        const customerInvoices = invoicesResult.rows
          .filter(row => row.doc && !row.doc._id.startsWith('_') &&
            row.doc.customer && row.doc.customer.value === customer._id)
          .map(row => row.doc);
        setInvoices(customerInvoices);

        // Fetch receipts
        const receiptsDB = new PouchDB("receipts");
        const receiptsResult = await receiptsDB.allDocs({ include_docs: true });
        const customerReceipts = receiptsResult.rows
          .filter(row => row.doc && !row.doc._id.startsWith('_') &&
            row.doc.customer && row.doc.customer.value === customer._id)
          .map(row => row.doc);
        setReceipts(customerReceipts);

        // Fetch company info for printing
        const companyDB = new PouchDB("company");
        const companyResult = await companyDB.allDocs({ include_docs: true });
        if (companyResult.rows.length > 0) {
          setCompany(companyResult.rows[0].doc);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [customer._id]);

  // Calculate customer statistics
  const statistics = useMemo(() => {
    if (!invoices.length) return { totalInvoiced: 0, totalPaid: 0, balance: 0, invoiceCount: 0 };

    const totalInvoiced = invoices.reduce((sum, invoice) => {
      const invoiceTotal = invoice.items?.reduce((total, item) => total + (item.price * item.quantity), 0) || 0;
      return sum + invoiceTotal;
    }, 0);

    const totalPaid = receipts.reduce((sum, receipt) => sum + (receipt.amount || 0), 0);

    return {
      totalInvoiced,
      totalPaid,
      balance: totalInvoiced - totalPaid,
      invoiceCount: invoices.length
    };
  }, [invoices, receipts]);

  // Get recent transactions for timeline
  const recentTransactions = useMemo(() => {
    const transactions = [
      ...invoices.map(invoice => ({
        type: 'invoice',
        date: invoice.date,
        id: invoice._id,
        amount: invoice.items?.reduce((total, item) => total + (item.price * item.quantity), 0) || 0
      })),
      ...receipts.map(receipt => ({
        type: 'receipt',
        date: receipt.date,
        id: receipt._id,
        amount: receipt.amount || 0
      }))
    ];

    return transactions
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);
  }, [invoices, receipts]);

  const { TabPane } = Tabs;

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable customer component
  const PrintableCustomer = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>CUSTOMER PROFILE</Title>
            <Text>Customer: {customer.name}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Customer Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Name">{customer.name}</Descriptions.Item>
        <Descriptions.Item label="Customer ID">{customer._id}</Descriptions.Item>
        {customer.email && <Descriptions.Item label="Email">{customer.email}</Descriptions.Item>}
        {customer.phone && <Descriptions.Item label="Phone">{customer.phone}</Descriptions.Item>}
        {customer.mobile && <Descriptions.Item label="Mobile">{customer.mobile}</Descriptions.Item>}
        {customer.address && <Descriptions.Item label="Address">{customer.address}</Descriptions.Item>}
        {customer.tin_number && <Descriptions.Item label="TIN Number">{customer.tin_number}</Descriptions.Item>}
      </Descriptions>

      <Divider />

      <Title level={4}>Financial Summary</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Total Invoiced">{numberFormat(statistics.totalInvoiced)}</Descriptions.Item>
        <Descriptions.Item label="Total Paid">{numberFormat(statistics.totalPaid)}</Descriptions.Item>
        <Descriptions.Item label="Balance">
          <Text
            style={{ color: statistics.balance > 0 ? '#f5222d' : statistics.balance < 0 ? '#52c41a' : 'inherit' }}
          >
            {numberFormat(statistics.balance)}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="Invoice Count">{statistics.invoiceCount}</Descriptions.Item>
      </Descriptions>

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  // Main content render
  const renderContent = () => (
    <Row gutter={[24, 24]}>
      <Col xs={24} lg={8}>
        <Card className="customer-card">
          <div style={{ textAlign: 'center', marginBottom: 24 }}>
            {dp ? (
              <Avatar size={100} src={dp} />
            ) : (
              <Avatar size={100} icon={<UserOutlined />} className="customer-avatar" />
            )}
            <Title level={4} style={{ marginTop: 16, marginBottom: 0 }}>
              {customer.name}
            </Title>
            <Text type="secondary">
              Customer ID: {customer._id}
            </Text>
          </div>

          <Divider style={{ margin: '16px 0' }} />

          <div>
            {customer.phone && (
              <div className="customer-info-item">
                <PhoneOutlined className="customer-contact-icon" />
                <Text className="customer-info-label">Phone:</Text>
                <Text copyable className="customer-info-value">{customer.phone}</Text>
              </div>
            )}

            {customer.mobile && (
              <div className="customer-info-item">
                <PhoneOutlined className="customer-contact-icon" />
                <Text className="customer-info-label">Mobile:</Text>
                <Text copyable className="customer-info-value">{customer.mobile}</Text>
              </div>
            )}

            {customer.email && (
              <div className="customer-info-item">
                <MailOutlined className="customer-contact-icon" />
                <Text className="customer-info-label">Email:</Text>
                <Text copyable className="customer-info-value">{customer.email}</Text>
              </div>
            )}

            {customer.address && (
              <div className="customer-info-item">
                <HomeOutlined className="customer-contact-icon" />
                <Text className="customer-info-label">Address:</Text>
                <Text className="customer-info-value">{customer.address}</Text>
              </div>
            )}

            {customer.tin_number && (
              <div className="customer-info-item">
                <IdcardOutlined className="customer-contact-icon" />
                <Text className="customer-info-label">TIN Number:</Text>
                <Text copyable className="customer-info-value">{customer.tin_number}</Text>
              </div>
            )}
          </div>
        </Card>
      </Col>

      <Col xs={24} lg={16}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Card className="customer-statistic-card">
              <Statistic
                title="Total Invoiced"
                value={numberFormat(statistics.totalInvoiced)}
                prefix={<DollarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="customer-statistic-card">
              <Statistic
                title="Total Paid"
                value={numberFormat(statistics.totalPaid)}
                prefix={<DollarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="customer-statistic-card">
              <Statistic
                title="Balance"
                value={numberFormat(statistics.balance)}
                prefix={<DollarOutlined />}
                valueStyle={{
                  color: statistics.balance > 0 ? '#f5222d' : statistics.balance < 0 ? '#52c41a' : 'inherit'
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card className="customer-statistic-card">
              <Statistic
                title="Invoice Count"
                value={statistics.invoiceCount}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Card
          title={<><HistoryOutlined /> Recent Transactions</>}
          className="customer-card"
          style={{ marginTop: 16 }}
        >
          {loading ? (
            <Skeleton active paragraph={{ rows: 5 }} />
          ) : recentTransactions.length > 0 ? (
            <Timeline>
              {recentTransactions.map((transaction, index) => (
                <Timeline.Item
                  key={index}
                  color={transaction.type === 'invoice' ? 'blue' : 'green'}
                  dot={transaction.type === 'invoice' ? <FileTextOutlined /> : <DollarOutlined />}
                >
                  <div className="customer-timeline-item">
                    <div className="customer-timeline-date">{formatDate(transaction.date)}</div>
                    <div className="customer-timeline-title">
                      {transaction.type === 'invoice' ? 'Invoice' : 'Receipt'} #{transaction.id}
                    </div>
                    <div className="customer-timeline-description">
                      Amount: {numberFormat(transaction.amount)}
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          ) : (
            <Empty description="No recent transactions" />
          )}
        </Card>
      </Col>
    </Row>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`${customer.name}`}
        subTitle={customer.phone}
        tags={statistics.balance > 0 ? [
          <Tag color="red" key="balance">
            Outstanding Balance: {numberFormat(statistics.balance)}
          </Tag>
        ] : null}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Customer Profile
              </Button>
            }
          >
            <PrintableCustomer />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card>
                <Skeleton avatar active paragraph={{ rows: 6 }} />
              </Card>
            </Col>
            <Col xs={24} lg={16}>
              <Row gutter={[16, 16]}>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
                <Col span={6}><Skeleton.Button active block style={{ height: 100 }} /></Col>
              </Row>
              <Card style={{ marginTop: 16 }}>
                <Skeleton active paragraph={{ rows: 5 }} />
              </Card>
            </Col>
          </Row>
        ) : (
          renderContent()
        )}

        <Tabs
          defaultActiveKey="statement"
          type="card"
          className="customer-tabs"
          style={{ marginTop: 24 }}
          size="large"
        >
          <TabPane
            tab={<span><FileTextOutlined /> Statement</span>}
            key="statement"
          >
            <Statement {...sharedProps} />
          </TabPane>

          <TabPane
            tab={<span><ShoppingCartOutlined /> Invoices</span>}
            key="invoices"
          >
            <ViewTable
              {...sharedProps}
              removeColumns={["items", "createdAt"]}
              {...props.modules.invoices}
              {...props.modulesProperties.invoices}
              organization={props.organization}
            />
          </TabPane>

          <TabPane
            tab={<span><DollarOutlined /> Receipts</span>}
            key="receipts"
          >
            <ViewTable
              {...sharedProps}
              {...props.modules.receipts}
              {...props.modulesProperties.receipts}
              organization={props.organization}
            />
          </TabPane>
        </Tabs>
      </div>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Customer Profile"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableCustomer />
        </PrintComponents>
      </div>
    </>
  );
};

export default Customer;
