import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Select,
  Button,
  Divider,
  Row,
  Col,
  Typography,
  message,
  InputNumber,
  Space,
  Tabs
} from 'antd';
import {
  ShopOutlined,
  PrinterOutlined,
  PercentageOutlined,
  CreditCardOutlined,
  SettingOutlined,
  BankOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const POSSettings = ({ pouchDatabase, databasePrefix, CRUD_USER }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    taxRate: 18,
    defaultPaymentMethod: 'Cash',
    defaultAccount: '',
    enableBarcodeScan: true,
    enableReceiptPrinting: true,
    receiptHeader: '',
    receiptFooter: 'Thank you for your business!',
    paymentMethods: [
      { value: 'Cash', label: 'Cash' },
      { value: 'Card', label: 'Card/Debit/Credit' },
      { value: 'Mobile Money', label: 'Mobile Money' },
      { value: 'Bank Transfer', label: 'Bank Transfer' },
      { value: 'Cheque', label: 'Cheque' },
    ],
    printerName: '',
    paperSize: 'A4',
    showLogo: true,
    logoUrl: '',
  });

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        // Try to get existing settings
        const settingsDoc = await pouchDatabase('settings', databasePrefix)
          .find({
            selector: {
              type: 'pos_settings'
            }
          })
          .then(result => result.docs[0]);

        // Load accounts for default account selection
        const accounts = await pouchDatabase('accounts', databasePrefix)
          .getAllData()
          .then(accounts => accounts.filter(account => !account._id.startsWith('_')));

        if (settingsDoc) {
          setSettings(prev => ({ ...prev, ...settingsDoc.settings }));
          form.setFieldsValue(settingsDoc.settings);
        }

        // Add accounts to form
        form.setFieldsValue({
          accounts: accounts.map(account => ({
            value: account._id,
            label: account.name
          }))
        });
      } catch (error) {
        console.error('Error loading POS settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  const handleSaveSettings = async (values) => {
    try {
      setLoading(true);

      // Find existing settings document
      const existingSettings = await pouchDatabase('settings', databasePrefix)
        .find({
          selector: {
            type: 'pos_settings'
          }
        })
        .then(result => result.docs[0]);

      if (existingSettings) {
        // Update existing document
        await pouchDatabase('settings', databasePrefix).saveDocument({
          ...existingSettings,
          settings: values,
        }, CRUD_USER);
      } else {
        // Create new settings document
        await pouchDatabase('settings', databasePrefix).saveDocument({
          type: 'pos_settings',
          settings: values,
          createdAt: new Date().toISOString(),
        }, CRUD_USER);
      }

      setSettings(values);
      message.success('POS settings saved successfully');
    } catch (error) {
      console.error('Error saving POS settings:', error);
      message.error('Failed to save settings: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddPaymentMethod = () => {
    const { paymentMethods = [] } = form.getFieldsValue();
    const newMethod = { value: '', label: '' };
    form.setFieldsValue({ paymentMethods: [...paymentMethods, newMethod] });
  };

  const handleRemovePaymentMethod = (index) => {
    const { paymentMethods = [] } = form.getFieldsValue();
    const updatedMethods = [...paymentMethods];
    updatedMethods.splice(index, 1);
    form.setFieldsValue({ paymentMethods: updatedMethods });
  };

  return (
    <Card title={<><SettingOutlined /> POS Settings</>} loading={loading}>
      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={handleSaveSettings}
      >
        <Tabs defaultActiveKey="general">
          <TabPane
            tab={<span><ShopOutlined /> General</span>}
            key="general"
          >
            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Default Tax Rate (%)"
                  name="taxRate"
                  rules={[{ required: true, message: 'Please enter tax rate' }]}
                >
                  <InputNumber
                    min={0}
                    max={100}
                    style={{ width: '100%' }}
                    addonAfter="%"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Default Payment Method"
                  name="defaultPaymentMethod"
                  rules={[{ required: true, message: 'Please select default payment method' }]}
                >
                  <Select>
                    {settings.paymentMethods.map((method, index) => (
                      <Select.Option key={index} value={method.value}>
                        {method.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Default Account"
                  name="defaultAccount"
                  rules={[{ required: true, message: 'Please select default account' }]}
                >
                  <Select>
                    <Form.List name="accounts">
                      {(fields) => (
                        <>
                          {fields.map(({ key, name }) => (
                            <Select.Option
                              key={key}
                              value={form.getFieldValue(['accounts', name, 'value'])}
                            >
                              {form.getFieldValue(['accounts', name, 'label'])}
                            </Select.Option>
                          ))}
                        </>
                      )}
                    </Form.List>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Enable Barcode Scanning"
                  name="enableBarcodeScan"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Enable Receipt Printing"
                  name="enableReceiptPrinting"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={<span><CreditCardOutlined /> Payment Methods</span>}
            key="payment"
          >
            <Divider orientation="left">Payment Methods</Divider>
            <Form.List name="paymentMethods">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Row key={key} gutter={16} style={{ marginBottom: 16 }}>
                      <Col xs={24} md={10}>
                        <Form.Item
                          {...restField}
                          name={[name, 'value']}
                          rules={[{ required: true, message: 'Missing value' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <Input placeholder="Value (e.g. 'cash')" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} md={10}>
                        <Form.Item
                          {...restField}
                          name={[name, 'label']}
                          rules={[{ required: true, message: 'Missing label' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <Input placeholder="Label (e.g. 'Cash')" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} md={4}>
                        <Button
                          danger
                          onClick={() => remove(name)}
                          disabled={fields.length <= 1}
                        >
                          Remove
                        </Button>
                      </Col>
                    </Row>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block>
                      Add Payment Method
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>

            <Divider orientation="left">Accounts</Divider>
            <p>Accounts are managed in the Accounts module. The accounts listed here are used for POS transactions.</p>
            <Form.List name="accounts">
              {(fields) => (
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={[16, 16]}>
                    {fields.map(({ key, name }) => (
                      <Col xs={24} sm={12} md={8} lg={6} key={key}>
                        <Card size="small">
                          <p><strong>Account:</strong> {form.getFieldValue(['accounts', name, 'label'])}</p>
                          <p><strong>ID:</strong> {form.getFieldValue(['accounts', name, 'value'])}</p>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                  {fields.length === 0 && (
                    <div style={{ textAlign: 'center', padding: '20px', background: '#f5f5f5', borderRadius: '4px' }}>
                      <p>No accounts found. Please create accounts in the Accounts module.</p>
                      <Button type="primary" icon={<BankOutlined />}>Go to Accounts</Button>
                    </div>
                  )}
                </div>
              )}
            </Form.List>
          </TabPane>

          <TabPane
            tab={<span><PrinterOutlined /> Receipt</span>}
            key="receipt"
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Receipt Header"
                  name="receiptHeader"
                >
                  <Input.TextArea rows={3} placeholder="Company name, address, etc." />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Receipt Footer"
                  name="receiptFooter"
                >
                  <Input.TextArea rows={3} placeholder="Thank you message, return policy, etc." />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Printer Name"
                  name="printerName"
                >
                  <Input placeholder="Default printer" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Paper Size"
                  name="paperSize"
                >
                  <Select>
                    <Select.Option value="A4">A4</Select.Option>
                    <Select.Option value="80mm">80mm Receipt</Select.Option>
                    <Select.Option value="58mm">58mm Receipt</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Show Logo"
                  name="showLogo"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="Logo URL"
                  name="logoUrl"
                >
                  <Input placeholder="URL to your logo image" />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
        </Tabs>

        <Divider />

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Save Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default POSSettings;
