import PouchDb from "pouchdb-browser";

var moment = require("moment");

export default {
  name: "Check In",
  icon: "HomeOutlined",
  path: "hotel/checkins",
  collection: "checkins",
  singular: "Check In",
  parent: "hotel",
  removeCreate: true,
  columns: [
    {
      dataIndex: "_id",
      title: "ID",
      hideInForm: true,

      isPrintable: true,
    },
    // {
    //   title: "Reference No",
    //   dataIndex: "ref_number",
    //   sorter: true,
    //   valueType: "number",
    //   width: "lg",
    //   colProps: {
    //     md: 6,
    //   },
    // },
    {
      type: "dbSelect",
      collection: "guests",
      label: ["title", "sur_name", "first_name"],
      dataIndex: "guest",
      title: "Guest",
      width: "lg",
      valueType: "text",
      // isRequired: true,
      filters: true,
      onFilter: true,

      isPrintable: true,
    },
    {
      title: "Check In Date",
      dataIndex: "arrival_date",
      sorter: true,
      valueType: "dateTime",
      // isRequired: true,
      noBackDate: true,
      isPrintable: true,
      width: "lg",
      colProps: {
        md: 12,
      },
    },

    {
      title: "Check Out Date",
      dataIndex: "departure_date",
      sorter: true,
      valueType: "date",
      isRequired: true,

      isPrintable: true,
      width: "lg",
      colProps: {
        md: 12,
      },
    },
    {
      valueType: "dependency",
      hideInTable: true,
      name: ["arrival_date", "departure_date"],
      columns: function columns(_ref) {
        var arrival_date = _ref.arrival_date,
          departure_date = _ref.departure_date;
        return [
          {
            dataIndex: "stay_duration",
            title: "Proposed Duration Of Stay",
            valueType: "digits",
            colProps: {
              md: 12,
            },
            fieldProps: {
              addonAfter: "Days",
              style: {
                width: "100%",
              },
              disabled: true,
              value:
                departure_date && arrival_date
                  ? moment(departure_date)
                      .startOf("day")
                      .diff(moment(arrival_date).startOf("day"), "days")
                  : 0,
            },
          },
        ];
      },
    },
    {
      valueType: "text",
      dataIndex: "car_number",
      title: "Car Number",
      hideInTable: true,
    },
    {
      valueType: "digit",
      dataIndex: "persons",
      title: "Persons",
      hideInTable: true,
    },
    {
      valueType: "text",
      dataIndex: "purpose_of_visit",
      title: "Purpose Of Visit",
      hideInTable: true,
    },
    {
      type: "dbSelect",
      collection: "rooms",
      label: ["number", "short_desc", " - ", "rate"],
      dataIndex: "room_number",
      isRequired: true,
      filterOptions: {
        dataIndex: "room_status",
        value: ["Occupied", "Booked", "House Use", "Out Of Order"],
        direction: "out",

        isPrintable: true,
      },
      title: "Room",
    },

    // {
    //   valueType: "dependency",
    //   hideInTable: true,
    //   name: ["room_number"],
    //   columns: function columns(_ref2) {
    //     var room_number = _ref2.room_number;

    //     if (room_number) {
    //       const room_rate = room_number.label.split(" - ")[1];
    //       return [

    //       ];
    //     } else {
    //       return [];
    //     }
    //   },
    // },
    {
      dataIndex: "room_rate",
      title: "Room Rate",
      valueType: "digit",
      isRequired: true,
      hideInTable: true,
      colProps: {
        md: 12,
      },
      fieldProps: {
        style: {
          width: "100%",
        },
        // defaultValue: room_number ? room_rate : 0,
      },
    },
    {
      dataIndex: "check_in_total",
      title: "Bill",
      valueType: "digit",
      hideInForm: true,
    },
    {
      valueType: "digit",
      dataIndex: "balance",
      title: "Balance",
      hideInForm: true,
    },
    {
      valueType: "dependency",
      hideInTable: true,
      name: ["room_number", "room_rate", "arrival_date", "departure_date"],
      columns: function columns({
        room_number,
        arrival_date,
        departure_date,
        room_rate,
      }) {
        let rateToUse = 0;

        // 

        if (!room_rate && room_number) {
          rateToUse = parseInt(room_number.label.split(" - ")[1]);
        } else {
          rateToUse = room_rate;
        }
        return [
          {
            dataIndex: "total",
            title: "Check In Total",
            valueType: "digit",
            colProps: {
              md: 12,
            },
            fieldProps: {
              style: {
                width: "100%",
              },
              disabled: true,

              isPrintable: true,
              value:
                rateToUse && departure_date && arrival_date
                  ? moment(departure_date)
                      .startOf("day")
                      .diff(moment(arrival_date).startOf("day"), "days") *
                    rateToUse
                  : 0,
            },
          },
        ];

        // 
      },
    },
    {
      type: "dbSelect",
      isRequired: true,
      collection: "companies",
      label: ["name"],
      dataIndex: "company",
      title: "Company",
      width: "lg",
    },
    {
      title: "Billing",
      dataIndex: "billing",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      valueEnum: {
        "Self Payment": {
          text: "Self Payment",
        },
        Company: {
          text: "Company",
        },
      },
    },
    {
      title:"Remarks",
      dataIndex:"remarks",
      hideInTable: true,
      valueType: "textarea",
    },
    {
      dataIndex: "checked_out",
      title: "Checked Out",
      valueType: "switch",
      filters: true,
      onFilter: true,
      valueEnum: {
        true: { text: "Checked Out" },
        [undefined]: { text: "Checked In" },
        [!true]: { text: "Checked In [Edited]" },
      },
      fieldProps: {
        checkedChildren: false,
        unCheckedChildren: true,
        defaultChecked: false,
      },
      render: (_, row) => (row.checked_out ? "Yes" : "No"),
    },
  ],
};
