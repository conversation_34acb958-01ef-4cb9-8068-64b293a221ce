export default {
  name: "Setting<PERSON>",
  icon: "KeyOutlined",
  path: "/settings",
  collection: "settings",
  singular: "Setting",
  multi_Branch: true,
  columns: [
    {
      type: "text",
      dataIndex: "usd_rate",
      isPrintable: true,
      title: "UGX to USD Conversion Rate",
    },
    {
      valueType: "switch",
      dataIndex: "allow_backdating",
      title: "Allow Backdating",
      isPrintable: true,
    },
    {
      valueType: "group",
      title: "Banking Information",
      isPrintable: true,
      colProps: {
        md: 24,
      },
      columns: [
        {
          type: "text",
          title: "Account Name",
          dataIndex: "acc_name",
          colProps: {
            md: 24,
          },
        },
        {
          type: "text",
          title: "Account Number",
          isPrintable: true,
          dataIndex: "acc_no",
          colProps: {
            md: 12,
          },
        },
        {
          type: "text",
          title: "Bank Name",
          dataIndex: "bank_name",
          isPrintable: true,
          colProps: {
            md: 12,
          },
        },
        {
          type: "text",
          title: "Bank Address",
          dataIndex: "bank_address",
          isPrintable: true,
          colProps: {
            md: 12,
          },
        },
        {
          type: "text",
          title: "Swift Code",
          dataIndex: "swift_code",
          isPrintable: true,
          colProps: {
            md: 12,
          },
        },
      ],
    },
  ],
};
