import AppDatabase from "../../../Utils/AppDatabase";

export const buffEvents = async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
  const guestDB = AppDatabase("guest", databasePrefix);
  const venuesDB = AppDatabase("venues", databasePrefix);

  const dbData = await Promise.all([
    await guestDB.getAllData(),
    await venuesDB.getAllData(),
    await venuesDB.getAllData(), // Keep this for backward compatibility
  ]);
  const [guests, venues] = dbData;

  return results.map((event) => {
    return {
      ...event,
      guest: guests.find((doc) => doc._id === event.guest.value),
      venue: venues.find((doc) => doc._id === event.venue.value),
    };
  });
};
