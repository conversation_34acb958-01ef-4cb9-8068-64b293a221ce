export default {
  name: "Assets",
  icon: "AppstoreAddOutlined",
  path: "assets",
  collection: "assets",
  singular: "Asset",
  columns: [
    {
      title: "Asset Name",
      dataIndex: "assetName",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Purchase Date",
      dataIndex: "purchaseDate",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Purchase Price",
      dataIndex: "purchasePrice",
      valueType: "digit",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Location",
      dataIndex: "location",
      valueType: "text",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Value",
      dataIndex: "value",
      valueType: "digit",
      isRequired: true,

      isPrintable: true,
    },
  ],
};
