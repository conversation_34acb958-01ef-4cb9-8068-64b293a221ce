import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Layout,
  Typography,
  Button,
  Flex,
  FloatButton,
  Card,
  Row,
  Col,
  Table,
  Tag,
  Tree,
  Empty,
} from "antd";
import {
  DollarOutlined,
  PrinterOutlined,
  BranchesOutlined,
  FileTextOutlined,
  AppstoreOutlined,
  PartitionOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PrintComponents from "react-print-components";
import RenderBlob from "../../../../Components/RenderBlob";
import { numberFormat } from "../../../../Utils/functions";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const ExpenseCategory = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [company, setCompany] = useState(null);
  const [parentCategory, setParentCategory] = useState(null);
  const [childCategories, setChildCategories] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [allCategories, setAllCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!pouchDatabase || databasePrefix === undefined) {
          console.error("pouchDatabase or databasePrefix not available");
          setLoading(false);
          return;
        }

        // Fetch company data (organizations)
        try {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            setCompany(organizationsData[0]);
          }
        } catch (error) {
          console.warn("Error fetching company data:", error);
        }

        // Fetch parent category if exists
        if (data.parent && data.parent.value) {
          try {
            const parentData = await pouchDatabase("expense_categories", databasePrefix).getDocument(data.parent.value);
            setParentCategory(parentData);
          } catch (error) {
            console.warn("Error fetching parent category:", error);
          }
        }

        // Fetch all categories to build hierarchy
        const categoriesData = await pouchDatabase("expense_categories", databasePrefix).getAllData();
        setAllCategories(categoriesData);

        // Find child categories
        const children = categoriesData.filter(
          category => category.parent && category.parent.value === data._id
        );
        setChildCategories(children);

        // Fetch expenses in this category
        const expensesData = await pouchDatabase("expenses", databasePrefix).getAllData();
        const filteredExpenses = expensesData.filter(
          expense => expense["expense-category"] && expense["expense-category"].value === data._id
        );

        // Sort by date descending and limit to 50
        const sortedExpenses = filteredExpenses
          .sort((a, b) => new Date(b.date) - new Date(a.date))
          .slice(0, 50);

        setExpenses(sortedExpenses);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Build category tree data
  const buildCategoryTree = () => {
    // Find all root categories (no parent)
    const rootCategories = allCategories.filter(cat => !cat.parent);

    // Build tree recursively
    const buildTree = (categories) => {
      return categories.map(category => {
        const children = allCategories.filter(
          child => child.parent && child.parent.value === category._id
        );

        const node = {
          title: category.name,
          key: category._id,
          isLeaf: children.length === 0,
        };

        // Highlight the current category
        if (category._id === data._id) {
          node.className = 'current-category';
        }

        if (children.length > 0) {
          node.children = buildTree(children);
        }

        return node;
      });
    };

    return buildTree(rootCategories);
  };

  // Calculate total expenses
  const calculateTotalExpenses = () => {
    return expenses.reduce((total, expense) => total + (expense.amount || 0), 0);
  };

  // Printable expense category component
  const PrintableExpenseCategory = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>EXPENSE CATEGORY</Title>
            <Text>Category: {data.name}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Category Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
        <Descriptions.Item label="Parent Category">
          {parentCategory ? parentCategory.name : "None"}
        </Descriptions.Item>
        <Descriptions.Item label="Child Categories" span={2}>
          {childCategories.length > 0
            ? childCategories.map(child => child.name).join(", ")
            : "None"}
        </Descriptions.Item>
      </Descriptions>

      {expenses.length > 0 && (
        <>
          <Divider />
          <Title level={4}>Recent Expenses</Title>
          <table className="category-expenses-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Supplier</th>
                <th>Description</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {expenses.slice(0, 10).map((expense, index) => (
                <tr key={index}>
                  <td>{moment(expense.date).format("DD MMM YYYY")}</td>
                  <td>{expense.supplier?.label || "N/A"}</td>
                  <td>{expense.description || "N/A"}</td>
                  <td>{numberFormat(expense.amount)}</td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr>
                <td colSpan="3" style={{ textAlign: "right" }}><strong>Total:</strong></td>
                <td><strong>{numberFormat(calculateTotalExpenses())}</strong></td>
              </tr>
            </tfoot>
          </table>
          {expenses.length > 10 && (
            <div style={{ textAlign: "center", marginTop: 10 }}>
              <Text type="secondary">
                Showing 10 of {expenses.length} expenses
              </Text>
            </div>
          )}
        </>
      )}

      <Divider />

      <div style={{ marginTop: "50px", textAlign: "center" }}>
        <p>Generated on: {moment().format("DD MMM YYYY, HH:mm")}</p>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading expense category information...</div>;
  }

  // Columns for the expenses table
  const expenseColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => moment(date).format('DD MMM YYYY'),
      sorter: (a, b) => moment(a.date).unix() - moment(b.date).unix(),
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      key: 'supplier',
      render: (supplier) => supplier?.label || 'N/A',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description) => description || 'N/A',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => numberFormat(amount),
      sorter: (a, b) => (a.amount || 0) - (b.amount || 0),
    },
    {
      title: 'Payment Method',
      dataIndex: 'method_of_payment',
      key: 'method_of_payment',
      render: (method) => (
        <Tag color={
          method === 'Cash' ? 'green' :
            method === 'Credit Card' ? 'blue' :
              method === 'Cheque' ? 'purple' :
                'default'
        }>
          {method || 'N/A'}
        </Tag>
      ),
    },
  ];

  return (
    <>
      <PageHeader
        backIcon={<DollarOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={data.name}
        subTitle="Expense Category"
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Category Details
              </Button>
            }
          >
            <PrintableExpenseCategory />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={12}>
            <Card title={<><FileTextOutlined /> Category Details</>} className="expense-category-card">
              <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
                <Descriptions.Item label="Parent Category">
                  {parentCategory ? parentCategory.name : "None"}
                </Descriptions.Item>
                <Descriptions.Item label="Child Categories" span={2}>
                  {childCategories.length > 0
                    ? childCategories.map((child, index) => (
                      <Tag key={index} color="blue" style={{ margin: '2px' }}>
                        {child.name}
                      </Tag>
                    ))
                    : "None"}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title={<><BranchesOutlined /> Category Hierarchy</>} className="expense-category-card">
              {allCategories.length > 0 ? (
                <Tree
                  showLine={{ showLeafIcon: false }}
                  defaultExpandAll
                  treeData={buildCategoryTree()}
                />
              ) : (
                <Empty description="No categories found" />
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card
              title={
                <Flex align="center" gap="small">
                  <AppstoreOutlined />
                  <span>Expenses in this Category ({expenses.length})</span>
                </Flex>
              }
              className="expense-category-card"
              extra={
                <Text strong>
                  Total: {numberFormat(calculateTotalExpenses())}
                </Text>
              }
            >
              {expenses.length > 0 ? (
                <Table
                  dataSource={expenses}
                  columns={expenseColumns}
                  rowKey="_id"
                  pagination={{ pageSize: 10 }}
                  summary={() => (
                    <Table.Summary fixed>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={3} align="right">
                          <Text strong>Total</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <Text strong>{numberFormat(calculateTotalExpenses())}</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}></Table.Summary.Cell>
                      </Table.Summary.Row>
                    </Table.Summary>
                  )}
                />
              ) : (
                <Empty description="No expenses found in this category" />
              )}
            </Card>
          </Col>
        </Row>

        {childCategories.length > 0 && (
          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            <Col xs={24}>
              <Card
                title={<><PartitionOutlined /> Child Categories</>}
                className="expense-category-card"
              >
                <Row gutter={[16, 16]}>
                  {childCategories.map((child, index) => (
                    <Col xs={24} sm={12} md={8} lg={6} key={index}>
                      <Card
                        className="child-category-card"
                        hoverable
                        onClick={() => window.location.href = `#/expense_categories/${child._id}`}
                      >
                        <Flex vertical align="center" gap="small">
                          <DollarOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                          <Text strong>{child.name}</Text>
                        </Flex>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </Col>
          </Row>
        )}
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Category Details"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableExpenseCategory />
        </PrintComponents>
      </div>
    </>
  );
};

export default ExpenseCategory;
