import { Col, Divider, Image, Row } from "antd";
import React, { useEffect, useState } from "react";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";
import { ToWords } from "to-words";
import moment from "moment";
import {
  formatMoney,
  toWordsUGX,
  toWordsUSD,
} from "../../../../Utils/functions";
function _extends() {
  _extends = Object.assign
    ? Object.assign.bind()
    : function (target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i];
          for (var key in source) {
            if (Object.prototype.hasOwnProperty.call(source, key)) {
              target[key] = source[key];
            }
          }
        }
        return target;
      };
  return _extends.apply(this, arguments);
}

var converter = require("number-to-words");

var toWords = new ToWords({
  localeCode: "en-US",
  converterOptions: {
    currency: true,
    ignoreDecimal: true,
    ignoreZeroCurrency: true,
    doNotAddOnly: true,
    currencyOptions: {
      // can be used to override defaults for the selected locale
      name: "Shilling",
      plural: "<PERSON>lling<PERSON>",
      symbol: "UGX",
    },
  },
});

var Receipt = function Receipt(props) {
  var data = props.data,
    columns = props.columns,
    singular = props.singular,
    documentSize = props.documentSize,
    pouchDatabase = props.pouchDatabase,
    databasePrefix = props.databasePrefix,
    copy = props.copy;
  var invoiceTotal = Number(data.checkin.amount);
  var balance = invoiceTotal - data.amount;
  var organization = JSON.parse(
    localStorage.getItem(LOCAL_STORAGE_ORGANIZATION)
  );

  var _useState = useState(0),
    receiptTotal = _useState[0],
    setReceiptTotal = _useState[1];

  var _useState2 = useState(0),
    receiptBalance = _useState2[0],
    setTaxTotal = _useState2[1];

  var _useState3 = useState(""),
    companyLogo = _useState3[0],
    setCompanyLogo = _useState3[1];

  var _useState4 = useState(null),
    dbOrganization = _useState4[0],
    setDbOrganization = _useState4[1]; // organization.logo && blobToBase64(organization.logo.file).then(l => setCompanyLogo(l))

  let rBalance =
    data.checkin.room_rate *
      moment(data.checkin.departure_date)
        .startOf("day")
        .diff(moment(data.checkin.arrival_date).startOf("day"), "days") -
    (data.amount + data.checkin.extension);

  if (data.checkin.extension) {
    rBalance += data.checkin.extension;
  }
  if (data.checkin.receipts) {
    rBalance -= data.checkin.receipts;
  }

  useEffect(function () {
    pouchDatabase("organizations", databasePrefix)
      .getAttachment(organization._id, "logo")
      .then(function (res) {
        return setCompanyLogo(res);
      });
  }, []);
  useEffect(function () {
    pouchDatabase("organizations", databasePrefix)
      .getAllData()
      .then(function (res) {
        return setDbOrganization(res[0]);
      });
  }, []);
  var reservation = data.checkin;
  return /*#__PURE__*/ React.createElement(
    "div",
    {
      style: {
        padding: 0,
      },
    },
    dbOrganization &&
      /*#__PURE__*/ React.createElement(
        Row,
        {
          gutter: 24,
          style: {
            marginTop: 32,
          },
        },
        /*#__PURE__*/ React.createElement(
          Col,
          {
            span: 12,
            style: {
              textAlign: "left",
              fontSize: "13px",
            },
          },
          /*#__PURE__*/ React.createElement("h3", null, dbOrganization.name),
          dbOrganization.alternative_name &&
            /*#__PURE__*/ React.createElement(
              "h3",
              null,
              dbOrganization.alternative_name
            ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            dbOrganization.address
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Phone no: "),
            dbOrganization.phone
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Email: "),
            dbOrganization.email
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "website: "),
            dbOrganization.website
          ),
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement(
            "div",
            {
              style: {
                fontSize: "15px",
              },
            },
            /*#__PURE__*/ React.createElement(
              "strong",
              null,
              "Check In No: ",
              data.checkin.ref_number
                ? `${data.checkin._id} - ${data.checkin.ref_number}`
                : data.checkin._id
            )
          )
        ),
        /*#__PURE__*/ React.createElement(
          Col,
          {
            span: 12,
            style: {
              textAlign: "right",
            },
          },
          /*#__PURE__*/ React.createElement(Image, {
            width: 80,
            src: companyLogo,
          }),
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement(
            "div",
            {
              style: {
                fontSize: "15px",
              },
            },
            " ",
            /*#__PURE__*/ React.createElement(
              "strong",
              null,
              "Receipt No: ",
              data.ref_number ? `${data._id} - ${data.ref_number}` : data._id
            )
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Receipt date: "),
            moment(data.createdAt).format("DD MMM YYYY")
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Print date: "),
            moment(new Date()).format("DD MMM YYYY HH:mm")
          )
        )
      ),
    /*#__PURE__*/ React.createElement(
      "center",
      {
        align: "center",
        style: {
          fontSize: "25px",
          marginTop: "15px",
          marginBottom: "15px",
        },
      },
      /*#__PURE__*/ React.createElement("u", null, "RECEIPT")
    ),
    /*#__PURE__*/ React.createElement(
      "div",
      {
        style: {
          textAlign: "left",
          fontSize: "15px",
        },
      },
      /*#__PURE__*/ React.createElement(
        "span",
        {
          style: {
            float: "right",
          },
        },
        /*#__PURE__*/ React.createElement("strong", null, copy)
      ),
      "Received With Thanks From :\xA0\xA0\xA0 ",
      /*#__PURE__*/ React.createElement(
        "strong",
        null,
        reservation.label.split("-")[0]
      ),
      " ",
      /*#__PURE__*/ React.createElement("br", null),
      "The sum of :\xA0\xA0\xA0 ",
      /*#__PURE__*/ React.createElement(
        "strong",
        null,
        data.currency === "USD"
          ? "USD - " + toWordsUSD.convert(data.amount)
          : toWordsUGX.convert(data.amount)
      ),
      /*#__PURE__*/ React.createElement("br", null),
      "Towards Payment of :\xA0\xA0\xA0 ",
      /*#__PURE__*/ React.createElement(
        "strong",
        null,
        "Rental charges, ",
        reservation.extensions && " visit extensions,",
        reservation.items &&
          reservation.items
            .map(function (i) {
              return i.item;
            })
            .join(", "),
        " "
      ),
      /*#__PURE__*/ React.createElement("br", null),
      /*#__PURE__*/ React.createElement("br", null)
    ),
    /*#__PURE__*/ React.createElement(
      Row,
      {
        justify: "space-between",
        style: {
          marginTop: 32,
        },
      },
      /*#__PURE__*/ React.createElement(
        Col,
        null,
        /*#__PURE__*/ React.createElement(
          Col,
          null,
          data.currency === "USD" &&
            /*#__PURE__*/ React.createElement(
              "div",
              {
                style: {
                  textAlign: "left",
                  fontSize: "15px",
                },
              },
              " Balance: ",
              rBalance === 0 ? "Nil" : formatMoney(rBalance)
            )
        )
      ),
      /*#__PURE__*/ React.createElement(
        Col,
        null,
        /*#__PURE__*/ React.createElement(
          "div",
          {
            style: {
              textAlign: "left",
              fontSize: "20px",
            },
          },
          data.currency,
          " ",
          formatMoney(data.amount)
        )
      )
    ),
    /*#__PURE__*/ React.createElement(Divider, {
      align: "center",
      style: {
        color: "#1890ff",
      },
    }),
    /*#__PURE__*/ React.createElement(
      Row,
      {
        justify: "space-between",
      },
      /*#__PURE__*/ React.createElement(
        Col,
        {
          style: {
            textAlign: "center",
          },
        },
        /*#__PURE__*/ React.createElement(
          "div",
          null,
          /*#__PURE__*/ React.createElement("br", null),
          "................................................",
          /*#__PURE__*/ React.createElement("br", null)
        ),
        /*#__PURE__*/ React.createElement("strong", null, "Guest's Signature")
      ),
      /*#__PURE__*/ React.createElement(
        Col,
        {
          style: {
            textAlign: "center",
          },
        },
        /*#__PURE__*/ React.createElement("div", null, "Payment Method"),
        /*#__PURE__*/ React.createElement(
          "strong",
          null,
          data.method_of_payment
        )
      ),
      /*#__PURE__*/ React.createElement(
        Col,
        {
          style: {
            textAlign: "center",
          },
        },
        /*#__PURE__*/ React.createElement(
          "div",
          null,
          /*#__PURE__*/ React.createElement("br", null),
          "................................................",
          /*#__PURE__*/ React.createElement("br", null)
        ),
        /*#__PURE__*/ React.createElement("strong", null, "Casher's Signature")
      )
    )
  );
};

export default (function (props) {
  return /*#__PURE__*/ React.createElement(
    React.Fragment,
    null,
    /*#__PURE__*/ React.createElement(
      Receipt,
      _extends({}, props, {
        copy: "Customer Copy",
      })
    ),
    /*#__PURE__*/ React.createElement(Divider, null),
    /*#__PURE__*/ React.createElement(
      Receipt,
      _extends({}, props, {
        copy: "Office Copy",
      })
    )
  );
});
