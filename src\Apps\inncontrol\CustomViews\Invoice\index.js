import React, { useEffect, useState } from "react";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";
import moment from "moment";
import InvoiceTemplate from "./InvoiceTemplate";
import PouchDb from "pouchdb-browser";

const Invoice = (props) => {
  const [guest, setGuest] = useState(null);

  const record = props.data;
  const data = props.data;

  

  useEffect(() => {
    const guestDB = new PouchDb("guests", guest);
    data.guest
      ? guestDB.get(data.guest.value).then((data) => {
          setGuest(data);
        })
      : setGuest(null);
  }, [props.data]);

  const newdata = {
    ...record,
    date: record.createdAt,
    id: record._id,
    client: { title: "Guest", name: guest }, //data.guest,
    operator: { title: "Entrant", name: record.entrant.label },
    items: [
      ...((record.items &&
        record.items.map((item) => {
          return {
            name: item.item,
            price: item.cost,
            quantity: item.quantity,
          };
        })) ||
        []),
    ],
  };

  return <InvoiceTemplate documentTitle="Visit Invoice" data={newdata} />;
};

export default Invoice;
