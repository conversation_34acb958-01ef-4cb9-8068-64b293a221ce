/**
 * Sync Reliability Testing Tools
 * Load this script in browser console to access sync reliability testing tools
 */

// Import the testing tools dynamically
async function loadSyncReliabilityTools() {
  try {
    console.log('🔧 Loading Sync Reliability Testing Tools...');
    
    // Import the tester module
    const { default: SyncReliabilityTester } = await import('/src/Utils/syncReliabilityTester.js');
    
    // Get database access (assuming global pouchDatabase function exists)
    if (typeof window.pouchDatabase !== 'function') {
      console.error('❌ pouchDatabase function not found. Make sure the app is loaded.');
      return;
    }

    // Get database prefix
    const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX') || '';
    if (!databasePrefix) {
      console.warn('⚠️ No database prefix found. Some tests may not work correctly.');
    }

    // Create tester instance
    window.syncReliabilityTester = new SyncReliabilityTester(window.pouchDatabase, databasePrefix);
    
    // Convenience methods for testing
    window.testSyncReliability = async (collections = ['test_sync'], duration = 60000) => {
      console.log(`🚀 Starting ${duration/1000}s sync reliability test...`);
      try {
        const report = await window.syncReliabilityTester.runComprehensiveTest(collections, duration);
        console.log('📊 Test Report:', report);
        return report;
      } catch (error) {
        console.error('❌ Test failed:', error);
        return null;
      }
    };

    window.quickSyncTest = () => window.testSyncReliability(['quick_test'], 30000);
    
    window.stopSyncTest = () => {
      if (window.syncReliabilityTester) {
        window.syncReliabilityTester.stopTest();
      }
    };

    window.getSyncTestStatus = () => {
      if (window.syncReliabilityTester) {
        return window.syncReliabilityTester.getTestStatus();
      }
      return null;
    };

    // Health monitoring tools
    window.checkSyncHealth = async () => {
      try {
        // Get a sample database to check orchestrator
        const testDb = window.pouchDatabase('organizations', databasePrefix);
        if (testDb && testDb.syncOrchestrator) {
          const healthCheck = await testDb.syncOrchestrator.performComprehensiveHealthCheck();
          console.log('🏥 Sync Health Check:', healthCheck);
          return healthCheck;
        } else {
          console.warn('⚠️ No sync orchestrator found. Health check not available.');
          return null;
        }
      } catch (error) {
        console.error('❌ Health check failed:', error);
        return null;
      }
    };

    window.getSyncReliabilityReport = () => {
      try {
        const testDb = window.pouchDatabase('organizations', databasePrefix);
        if (testDb && testDb.syncOrchestrator) {
          const report = testDb.syncOrchestrator.getSyncReliabilityReport();
          console.log('📈 Reliability Report:', report);
          return report;
        } else {
          console.warn('⚠️ No sync orchestrator found. Report not available.');
          return null;
        }
      } catch (error) {
        console.error('❌ Failed to get reliability report:', error);
        return null;
      }
    };

    // Monitoring utilities
    window.monitorSyncHealth = (intervalSeconds = 60) => {
      if (window.syncHealthMonitor) {
        clearInterval(window.syncHealthMonitor);
      }

      console.log(`📊 Starting sync health monitoring (every ${intervalSeconds}s)`);
      
      window.syncHealthMonitor = setInterval(() => {
        const report = window.getSyncReliabilityReport();
        if (report && report.reliabilityMetrics) {
          const metrics = report.reliabilityMetrics;
          const successRate = (metrics.successRate * 100).toFixed(1);
          const isHealthy = metrics.isHealthy ? '🟢' : '🔴';
          
          console.log(`${isHealthy} Sync Health: ${successRate}% success rate, ${metrics.metrics.averageSyncTime.toFixed(0)}ms avg time`);
          
          if (!metrics.isHealthy) {
            console.warn('⚠️ Sync health issues detected:', metrics.recommendations);
          }
        }
      }, intervalSeconds * 1000);

      return window.syncHealthMonitor;
    };

    window.stopSyncMonitoring = () => {
      if (window.syncHealthMonitor) {
        clearInterval(window.syncHealthMonitor);
        window.syncHealthMonitor = null;
        console.log('🛑 Sync health monitoring stopped');
      }
    };

    console.log('✅ Sync Reliability Testing Tools loaded successfully!');
    console.log('\n📋 Available Commands:');
    console.log('  🧪 TESTING:');
    console.log('    • testSyncReliability([collections], duration) - Run comprehensive test');
    console.log('    • quickSyncTest() - Run 30-second quick test');
    console.log('    • stopSyncTest() - Stop running test');
    console.log('    • getSyncTestStatus() - Get current test status');
    console.log('\n  🏥 HEALTH MONITORING:');
    console.log('    • checkSyncHealth() - Perform comprehensive health check');
    console.log('    • getSyncReliabilityReport() - Get detailed reliability report');
    console.log('    • monitorSyncHealth(intervalSeconds) - Start continuous monitoring');
    console.log('    • stopSyncMonitoring() - Stop continuous monitoring');
    console.log('\n  🎯 QUICK START:');
    console.log('    • quickSyncTest() - Test sync reliability for 30 seconds');
    console.log('    • checkSyncHealth() - Check current sync health');
    console.log('    • monitorSyncHealth(30) - Monitor health every 30 seconds');
    console.log('\n  📊 EXAMPLE USAGE:');
    console.log('    • testSyncReliability(["organizations", "users"], 120000) - 2-minute test');
    console.log('    • monitorSyncHealth(60) - Monitor every minute');
    
  } catch (error) {
    console.error('❌ Failed to load Sync Reliability Testing Tools:', error);
    console.log('💡 Make sure you are running this in the app context');
  }
}

// Auto-load if in browser environment
if (typeof window !== 'undefined') {
  loadSyncReliabilityTools();
} else {
  console.log('📋 To load Sync Reliability Testing Tools, run: loadSyncReliabilityTools()');
}
