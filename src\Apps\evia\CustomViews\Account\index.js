import React, { useEffect, useState } from "react";
import AppDatabase from "../../../../Utils/AppDatabase";
import Ledger from "./Ledger";
import { Radio } from "antd";
import moment from "moment";
const Account = (props) => {
  const [data, setData] = useState(null);

  useEffect(() => {
    const databasePrefix = localStorage.getItem("DB_PREFIX") || "";
    const checkinsDB = AppDatabase("checkins", databasePrefix);
    const receiptsDB = AppDatabase("receipts", databasePrefix);
    const invoicesDB = AppDatabase("invoices", databasePrefix);
    const ordersDB = AppDatabase("orders", databasePrefix);
    const order_receiptsDB = AppDatabase("order_receipts", databasePrefix);
    const eventsDB = AppDatabase("events", databasePrefix);
    const events_receiptsDB = AppDatabase("events_receipts", databasePrefix);

    const dbData = Promise.all([
      checkinsDB.getAllData(),
      receiptsDB.getAllData(),
      invoicesDB.getAllData(),
      ordersDB.getAllData(),
      order_receiptsDB.getAllData(),
      eventsDB.getAllData(),
      events_receiptsDB.getAllData(),
    ]);

    dbData.then(
      ([
        checkins,
        receipts,
        invoices,
        orders,
        order_receipts,
        events,
        events_receipts,
      ]) => {
        setData({
          checkins,
          receipts,
          invoices,
          orders,
          order_receipts,
          events,
          events_receipts,
        });
      }
    );

    // setData(props.data)
  }, []);

  // //cash ledger data
  // const cashLedger =
  //   data &&
  //   data.receipts.map((r) => {
  //     return {
  //       date: r.date,
  //       description: r.description,
  //       amount: r.amount,
  //     };
  //   });

  //

  const salesLedger = [];
  const creditLedger = [];

  data &&
    data.checkins.map((o) => {
      salesLedger.push({
        date: o.createdAt,
        description: `Accommodation in ${o.room_number.label}`,
        amount:
          o.room_rate * moment(o.departure_date).diff(o.arrival_date, "d"),
      });

      o.extensions &&
        salesLedger.push({
          date: o.departure_date,
          description: `Accommodation extension in ${o.room_number.label}`,
          amount: o.extensions,
        });
      o.items &&
        o.items.map((i) => {
          salesLedger.push({
            date: o.createdAt,
            description: i.quantity + " x " + i.item,
            amount: i.quantity * i.cost,
          });
        });

      let totalInvoice = 0;
      totalInvoice +=
        o.room_rate *
        moment(o.departure_date).startOf("day").diff(o.arrival_date, "d");

      totalInvoice +=
        o.items &&
        o.items.reduce((pv, cv) => (pv + cv ? cv.cost * cv.quantity : 0));

      totalInvoice += o.extensions;

      const invoiceReceiptTotal = data.receipts
        .filter((r) => r.checkin.value === o._id)
        .reduce((pv, cv) => pv + parseInt(cv.amount), 0);
      totalInvoice - invoiceReceiptTotal > 0 &&
        creditLedger.push({
          date: o.createdAt,
          description: "Accommodation",
          amount: totalInvoice - invoiceReceiptTotal,
        });
    });

  data &&
    data.orders.map((o) => {
      let totalInvoice = 0;
      o.items.map((i) => {
        salesLedger.push({
          date: o.createdAt,
          description: i.quantity + " x " + i.item.label.split(" -")[0],
          amount: i.quantity * i.cost,
        });
      });

      totalInvoice += o.items.reduce(
        (pv, cv) => (pv + cv ? cv.cost * cv.quantity : 0),
        0
      );
      const invoiceReceiptTotal = data.order_receipts
        .filter((r) => r.order._id === o._id)
        .reduce((pv, cv) => pv + parseInt(cv.amount), 0);



      totalInvoice - invoiceReceiptTotal > 0 &&
        creditLedger.push({
          date: o.createdAt,
          description: `Order ${o._id}`,
          amount: totalInvoice - invoiceReceiptTotal,
        });
    });

  data &&
    data.events.map((o) => {
      let totalInvoice = o.price;
      salesLedger.push({
        date: o.createdAt,
        description: o.eventName,
        amount: o.price,
      });

      o.items.map((i) => {
        salesLedger.push({
          date: o.createdAt,
          description: i.quantity + " x " + i.item.label.split(" -")[0],
          amount: i.quantity * i.cost,
        });
      });
      totalInvoice += o.items.reduce((pv, cv) =>
        pv + cv ? cv.cost * cv.quantity : 0
      );

      const invoiceReceiptTotal = data.events_receipts
        .filter((r) => r.order._id === o._id)
        .reduce((pv, cv) => pv + parseInt(cv.amount), 0);

      totalInvoice - invoiceReceiptTotal > 0 &&
        creditLedger.push({
          date: o.createdAt,
          description: `Event - ${o.eventName}`,
          amount: totalInvoice - invoiceReceiptTotal,
        });
    });

  //cash ledger data

  const cashLedger = [];

  data &&
    data.receipts.map((r) => {
      cashLedger.push({
        date: r.date,
        description: r.checkin.label,
        amount: parseInt(r.amount),
      });
    });

  data &&
    data.order_receipts.map((r) => {
      cashLedger.push({
        date: r.date,
        description: r.order.label,
        amount: parseInt(r.amount),
      });
    });

  data &&
    data.events_receipts.map((r) => {
      cashLedger.push({
        date: r.date,
        description: r.order.label,
        amount: parseInt(r.amount),
      });
    });

  //credit ledger data



  const options = [
    { label: "Sales", value: "Sales" },
    { label: "Cash", value: "Cash" },
    { label: "Credit", value: "Credit" },
  ];

  const [value3, setValue3] = useState("Sales");
  const onChange3 = ({ target: { value } }: RadioChangeEvent) => {

    setValue3(value);
  };

  return (
    <div>
      {/* {cashLedger && <Ledger data={cashLedger} />} */}

      <Radio.Group
        options={options}
        onChange={onChange3}
        value={value3}
        optionType="button"
        buttonStyle="solid"
      />

      {/* {salesLedger && <Ledger title="Sales" data={salesLedger} />} */}

      {value3 === "Cash" && <Ledger title="Cash Ledger" data={cashLedger} />}
      {value3 === "Credit" && (
        <Ledger title="Credit Ledger" data={creditLedger} />
      )}
      {value3 === "Sales" && <Ledger title="Sales Ledger" data={salesLedger} />}
    </div>
  );
};

export default Account;
