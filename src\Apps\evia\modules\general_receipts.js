import { numberFormat } from "../../../Utils/functions";

export default {
  name: "General Receipts",
  icon: "CreditCardOutlined",
  path: "general_receipts",
  collection: "general_receipts",
  singular: "General Receipt",
  columns: [
    {
      title: "Guest",
      dataIndex: "guest",
      type: "dbSelect",
      collection: "guests",
      label: ["title", "sur_name", "first_name"],
      isPrintable: true,
    },

    {
      title: "Payment Date",
      dataIndex: "date",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
      noBackDate: true,
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      isPrintable: true,
      width: "lg",
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Cheque: {
          text: "Cheque",
        },
        Bank: {
          text: "Bank",
        },
        "Mobile digit": {
          text: "Mobile digit",
        },
      },
    },
    {
      title: "Amount Paid",
      dataIndex: "amount",
      isRequired: true,
      valueType: "digit",
      isPrintable: true,
    },
    {
      title: "Description",
      dataIndex: "description",
      type: "textarea",
      isPrintable: true,
      width: "lg",
      colProps: {
        md: 24,
      },
    },
  ],
};
