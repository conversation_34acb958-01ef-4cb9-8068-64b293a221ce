// PERFORMANCE: Bundle and app performance analysis script
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Starting Performance Analysis...\n');

// Analyze build output
function analyzeBuildOutput() {
  console.log('📦 Analyzing Build Output:');
  console.log('=' .repeat(50));

  const buildDir = path.join(process.cwd(), 'build');
  
  if (!fs.existsSync(buildDir)) {
    console.log('❌ Build directory not found. Run "yarn build" first.');
    return;
  }

  // Analyze static files
  const staticDir = path.join(buildDir, 'static');
  if (fs.existsSync(staticDir)) {
    analyzeStaticFiles(staticDir);
  }

  // Analyze main HTML file
  const indexPath = path.join(buildDir, 'index.html');
  if (fs.existsSync(indexPath)) {
    analyzeIndexFile(indexPath);
  }

  console.log('');
}

function analyzeStaticFiles(staticDir) {
  const jsDir = path.join(staticDir, 'js');
  const cssDir = path.join(staticDir, 'css');
  const mediaDir = path.join(staticDir, 'media');

  // Analyze JavaScript files
  if (fs.existsSync(jsDir)) {
    console.log('\n📄 JavaScript Files:');
    const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));
    let totalJSSize = 0;

    jsFiles.forEach(file => {
      const filePath = path.join(jsDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      totalJSSize += stats.size;
      
      const type = file.includes('main') ? '🎯 Main' : 
                  file.includes('vendor') ? '📚 Vendor' : 
                  file.includes('runtime') ? '⚡ Runtime' : '🧩 Chunk';
      
      const sizeStatus = stats.size > 1024 * 1024 ? '🔴' : 
                        stats.size > 512 * 1024 ? '🟡' : '🟢';
      
      console.log(`   ${sizeStatus} ${type}: ${file} (${sizeKB} KB)`);
    });

    const totalJSMB = (totalJSSize / 1024 / 1024).toFixed(2);
    const jsStatus = totalJSSize > 5 * 1024 * 1024 ? '🔴' : 
                    totalJSSize > 2 * 1024 * 1024 ? '🟡' : '🟢';
    console.log(`   ${jsStatus} Total JS: ${totalJSMB} MB`);
  }

  // Analyze CSS files
  if (fs.existsSync(cssDir)) {
    console.log('\n🎨 CSS Files:');
    const cssFiles = fs.readdirSync(cssDir).filter(file => file.endsWith('.css'));
    let totalCSSSize = 0;

    cssFiles.forEach(file => {
      const filePath = path.join(cssDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      totalCSSSize += stats.size;
      
      const sizeStatus = stats.size > 500 * 1024 ? '🔴' : 
                        stats.size > 200 * 1024 ? '🟡' : '🟢';
      
      console.log(`   ${sizeStatus} ${file} (${sizeKB} KB)`);
    });

    const totalCSSKB = (totalCSSSize / 1024).toFixed(2);
    console.log(`   📊 Total CSS: ${totalCSSKB} KB`);
  }

  // Analyze media files
  if (fs.existsSync(mediaDir)) {
    console.log('\n🖼️  Media Files:');
    const mediaFiles = fs.readdirSync(mediaDir);
    let totalMediaSize = 0;

    mediaFiles.forEach(file => {
      const filePath = path.join(mediaDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      totalMediaSize += stats.size;
      
      const sizeStatus = stats.size > 1024 * 1024 ? '🔴' : 
                        stats.size > 500 * 1024 ? '🟡' : '🟢';
      
      console.log(`   ${sizeStatus} ${file} (${sizeKB} KB)`);
    });

    const totalMediaMB = (totalMediaSize / 1024 / 1024).toFixed(2);
    console.log(`   📊 Total Media: ${totalMediaMB} MB`);
  }
}

function analyzeIndexFile(indexPath) {
  console.log('\n📄 Index.html Analysis:');
  const content = fs.readFileSync(indexPath, 'utf8');
  const sizeKB = (Buffer.byteLength(content, 'utf8') / 1024).toFixed(2);
  
  // Count resources
  const scriptTags = (content.match(/<script/g) || []).length;
  const linkTags = (content.match(/<link.*rel="stylesheet"/g) || []).length;
  const inlineStyles = (content.match(/<style/g) || []).length;
  const inlineScripts = (content.match(/<script(?![^>]*src)/g) || []).length;
  
  console.log(`   📏 Size: ${sizeKB} KB`);
  console.log(`   📜 External Scripts: ${scriptTags}`);
  console.log(`   🎨 External Stylesheets: ${linkTags}`);
  console.log(`   📝 Inline Styles: ${inlineStyles}`);
  console.log(`   ⚡ Inline Scripts: ${inlineScripts}`);
}

// Analyze package.json dependencies
function analyzeDependencies() {
  console.log('📦 Dependency Analysis:');
  console.log('=' .repeat(50));

  const packagePath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json not found');
    return;
  }

  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const deps = packageJson.dependencies || {};
  const devDeps = packageJson.devDependencies || {};

  console.log(`\n📚 Production Dependencies: ${Object.keys(deps).length}`);
  console.log(`🛠️  Development Dependencies: ${Object.keys(devDeps).length}`);

  // Identify large dependencies
  const largeDeps = [
    '@ant-design/pro-components',
    '@ant-design/charts',
    '@ant-design/plots',
    'antd',
    'react',
    'react-dom'
  ];

  console.log('\n🔍 Key Dependencies:');
  largeDeps.forEach(dep => {
    if (deps[dep]) {
      console.log(`   📦 ${dep}: ${deps[dep]}`);
    }
  });
}

// Performance recommendations
function generateRecommendations() {
  console.log('\n💡 Performance Recommendations:');
  console.log('=' .repeat(50));

  const recommendations = [
    {
      category: '🚀 Build Optimizations',
      items: [
        'Enable tree shaking for unused code elimination',
        'Use dynamic imports for code splitting',
        'Optimize images with WebP format',
        'Enable gzip/brotli compression'
      ]
    },
    {
      category: '⚡ Runtime Optimizations',
      items: [
        'Implement React.memo for expensive components',
        'Use useMemo and useCallback for expensive calculations',
        'Implement virtual scrolling for large lists',
        'Lazy load non-critical components'
      ]
    },
    {
      category: '🖥️  Electron Optimizations',
      items: [
        'Enable context isolation for security',
        'Use preload scripts for secure IPC',
        'Implement proper memory management',
        'Enable hardware acceleration'
      ]
    },
    {
      category: '📱 UX Improvements',
      items: [
        'Add loading states for async operations',
        'Implement skeleton screens',
        'Use progressive loading for images',
        'Add offline support with service workers'
      ]
    }
  ];

  recommendations.forEach(section => {
    console.log(`\n${section.category}:`);
    section.items.forEach(item => {
      console.log(`   ✓ ${item}`);
    });
  });
}

// Check for common performance issues
function checkPerformanceIssues() {
  console.log('\n🔍 Performance Issue Detection:');
  console.log('=' .repeat(50));

  const issues = [];

  // Check build size
  const buildDir = path.join(process.cwd(), 'build');
  if (fs.existsSync(buildDir)) {
    const jsDir = path.join(buildDir, 'static', 'js');
    if (fs.existsSync(jsDir)) {
      const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));
      let totalSize = 0;
      
      jsFiles.forEach(file => {
        const stats = fs.statSync(path.join(jsDir, file));
        totalSize += stats.size;
        
        if (stats.size > 2 * 1024 * 1024) { // 2MB
          issues.push(`🔴 Large JS file detected: ${file} (${(stats.size / 1024 / 1024).toFixed(2)}MB)`);
        }
      });

      if (totalSize > 10 * 1024 * 1024) { // 10MB
        issues.push(`🔴 Total JS bundle size is large: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
      }
    }
  }

  // Check package.json for potential issues
  const packagePath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const deps = packageJson.dependencies || {};
    
    if (Object.keys(deps).length > 50) {
      issues.push(`🟡 High number of dependencies: ${Object.keys(deps).length}`);
    }
  }

  if (issues.length === 0) {
    console.log('✅ No major performance issues detected!');
  } else {
    console.log('⚠️  Issues found:');
    issues.forEach(issue => {
      console.log(`   ${issue}`);
    });
  }
}

// Main execution
function main() {
  try {
    analyzeBuildOutput();
    analyzeDependencies();
    checkPerformanceIssues();
    generateRecommendations();
    
    console.log('\n🎉 Performance analysis complete!');
    console.log('💡 Run "yarn build:analyze" to see detailed bundle analysis');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  }
}

main();
