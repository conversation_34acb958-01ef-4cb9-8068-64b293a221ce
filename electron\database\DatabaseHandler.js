/**
 * Simple Database Handler for Electron Main Process
 * Minimal, efficient PouchDB operations with conflict resolution
 */

const PouchDB = require('pouchdb');
const PouchDBFind = require('pouchdb-find');
const PouchDBUpsert = require('pouchdb-upsert');
const path = require('path');
const fs = require('fs');
const { EventEmitter } = require('events');
const { app } = require('electron');

// Initialize only essential plugins
PouchDB.plugin(PouchDBFind);
PouchDB.plugin(PouchDBUpsert);
PouchDB.plugin(require('pouchdb-adapter-leveldb'));

class DatabaseHandler extends EventEmitter {
  constructor() {
    super();
    // Fix EventEmitter memory leak by increasing max listeners
    this.setMaxListeners(50);

    this.databases = new Map();
    this.currentUser = { name: "sys" };

    // Use user data directory instead of process.cwd() to avoid permission issues
    // This ensures the database is stored in a user-writable location
    this.databasePath = path.join(app.getPath('userData'), '.database');

    // Create database directory
    if (!fs.existsSync(this.databasePath)) {
      fs.mkdirSync(this.databasePath, { recursive: true });
    }

    // Conflict resolution tracking
    this.conflictStats = {
      totalConflicts: 0,
      resolvedConflicts: 0,
      failedResolutions: 0,
      orphanedConflictsCleanedUp: 0
    };

    // DUPLICATION FIX: Track active conflict resolutions to prevent concurrent processing
    this.activeConflictResolutions = new Set();

    // Track documents that have been processed for orphaned conflicts to prevent repeated cleanup attempts
    this.processedOrphanedConflicts = new Set();

    // Cleanup active resolutions periodically to prevent memory leaks
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleResolutions();
    }, 60000); // Clean up every minute
  }

  /**
   * Clean up stale conflict resolutions that may have been left behind
   */
  cleanupStaleResolutions() {
    // Clear all active resolutions older than 5 minutes
    // This is a safety measure in case some resolutions get stuck
    if (this.activeConflictResolutions.size > 100) {
      console.log(`[DatabaseHandler] Clearing ${this.activeConflictResolutions.size} stale conflict resolutions`);
      this.activeConflictResolutions.clear();
    }

    // Also clear processed orphaned conflicts cache if it gets too large
    // This prevents memory leaks while still avoiding repeated cleanup attempts
    if (this.processedOrphanedConflicts.size > 500) {
      console.log(`[DatabaseHandler] Clearing ${this.processedOrphanedConflicts.size} processed orphaned conflict entries`);
      this.processedOrphanedConflicts.clear();
    }
  }

  /**
   * Cleanup method to be called when shutting down
   */
  cleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.activeConflictResolutions.clear();
    this.processedOrphanedConflicts.clear();
    this.removeAllListeners();
  }

  /**
   * Initialize a database instance
   */
  async initializeDatabase(name, databasePrefix, lan_details, branch) {
    const dbKey = `${databasePrefix || ''}${name}`;

    if (this.databases.has(dbKey)) {
      return { success: true };
    }

    const dbPath = path.join(this.databasePath, name);
    const db = new PouchDB(dbPath, {
      adapter: 'leveldb',
      revs_limit: 10,        // Keep more revisions for proper conflict resolution
      auto_compaction: true  // optional: reduces DB size
    });

    this.databases.set(dbKey, {
      db,
      name,
      databasePrefix: databasePrefix || '',
      lan_details,
      branch: branch || 'none',
      lanString: lan_details ? `http://${lan_details.username}:${lan_details.password}@${lan_details.host}:${lan_details.port}/` : null
    });

    return { success: true };
  }

  /**
   * Normalize branch value (deprecated - no longer used)
   */
  normalizeBranch(branch) {
    // Branch filtering removed - this method is kept for backward compatibility
    return branch;
  }

  /**
   * Generate a unique document ID using the same pattern as SimplifiedDB
   */
  generateDocumentId() {
    // Use the same ID generation pattern as the existing SimplifiedDB system
    return Date.now().toString(36).toUpperCase();
  }

  /**
   * Generate a reference number using the same pattern as SimplifiedDB
   */
  generateReferenceNumber(prefix = '', count = null) {
    const sequence = count !== null
      ? count.toString().padStart(6, '0')
      : Math.floor(Math.random() * 999999).toString().padStart(6, '0');
    const timestamp = Date.now().toString(36).slice(-4).toUpperCase();
    return `${prefix}${timestamp}-${sequence}`;
  }


  /**
   * Sync with a specific target URL
   * @param {string} dbKey - Database key
   * @param {string} targetUrl - Target database URL to sync with
   * @param {Object} options - Sync options
   * @returns {Promise<Object>} Sync result
   */
  async syncThis(dbKey, targetUrl, options = {}) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const { db, name, databasePrefix } = dbConfig;

      // Default sync options
      const syncOptions = {
        ...options
      };

      // CRITICAL: Apply organization-based filtering for data isolation
      this.applyOrganizationFilter(syncOptions, name, databasePrefix);
      
      console.log(`[DatabaseHandler] Starting sync for ${dbKey} to ${targetUrl}`, syncOptions);


      // Perform the sync
      const syncResult = await db.sync(targetUrl, syncOptions);

      console.log(`[DatabaseHandler] Sync completed for ${dbKey}:`, {
        docs_read: syncResult.docs_read || 0,
        docs_written: syncResult.docs_written || 0,
        doc_write_failures: syncResult.doc_write_failures || 0
      });

      // Emit sync completion event
      this.emit('syncCompleted', {
        dbKey,
        targetUrl,
        result: syncResult,
        manual: true
      });

      return {
        success: true,
        docs_read: syncResult.docs_read || 0,
        docs_written: syncResult.docs_written || 0,
        doc_write_failures: syncResult.doc_write_failures || 0
      };
    } catch (error) {
      console.error(`[DatabaseHandler] Sync failed for ${dbKey}:`, error);

      // Emit sync error event
      this.emit('syncError', {
        dbKey,
        targetUrl,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Apply organization-based filtering for data isolation
   * Ensures each organization only syncs its own data from shared databases
   * @param {Object} syncOptions - Sync options to modify
   * @param {string} name - Database name
   * @param {string} databasePrefix - Database prefix
   */
  applyOrganizationFilter(syncOptions, name, databasePrefix) {
    // Organizations database: Filter by organization ID
    if (name === "organizations" && !databasePrefix.includes('mission_control')) {
      const pArray = databasePrefix.split('_');
      // For prefix like 'prosy_m9xlo8hv_', org ID is at index -2 (second to last)
      const organizationID = (pArray.length >= 2 ? pArray[pArray.length - 2] : pArray[0]).toUpperCase();

      console.log(`[DatabaseHandler] Applying organization filter for ${name}: ${organizationID} (from prefix: ${databasePrefix})`);

      syncOptions.filter = (doc) => {
        const matches = doc._id === organizationID;
        if (matches) {
          console.log(`[DatabaseHandler] Organization filter: Including doc ${doc._id}`);
        }
        return matches;
      };
    }

    // Subscriptions database: Filter by organization.value
    else if (name === "subscriptions" && !databasePrefix.includes('mission_control')) {
      const pArray = databasePrefix.split('_');
      // For prefix like 'prosy_m9xlo8hv_', org ID is at index -2 (second to last)
      const organizationID = (pArray.length >= 2 ? pArray[pArray.length - 2] : pArray[0]).toUpperCase();

      console.log(`[DatabaseHandler] Applying subscription filter for ${name}: ${organizationID} (from prefix: ${databasePrefix})`);

      syncOptions.filter = (doc) => {
        const matches = doc.organization && doc.organization.value === organizationID;
        if (matches) {
          console.log(`[DatabaseHandler] Subscription filter: Including doc ${doc._id} for org ${doc.organization.value}`);
        }
        return matches;
      };
    }

    // Plans database: Usually no filtering needed as plans are shared
    // But we can add filtering if needed
    else if (name === "plans" && !databasePrefix.includes('mission_control')) {
      console.log(`[DatabaseHandler] Plans database: No filtering applied (plans are shared)`);
      // Plans are typically shared across organizations, so no filter needed
      // If you need organization-specific plans, add filtering here
    }

    // For mission_control databases, no filtering needed as they contain all data
    else if (databasePrefix.includes('mission_control')) {
      console.log(`[DatabaseHandler] Mission control database: No filtering applied`);
    }

    // For other organization-specific databases, no filtering needed
    else {
      console.log(`[DatabaseHandler] Organization-specific database ${name}: No filtering needed`);
    }
  }

  /**
   * Create or update a document - matches SimplifiedDB save() method exactly
   */
  async saveDocument(dbKey, data, user) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const { db, branch } = dbConfig;
      this.currentUser = user || this.currentUser;

      // Process attachments if present
      let processedData = { ...data };
      if (data._attachments) {
        processedData = await this.processAttachments(data, db);
      }

      // Match SimplifiedDB logic exactly: any document with _id is an update
      const isUpdate = !!processedData._id;
      const docId = processedData._id || Date.now().toString(36).toUpperCase();

      // Get existing document first if this is an update
      let existingDoc = null;
      if (isUpdate) {
        try {
          existingDoc = await db.get(docId);
        } catch (err) {
          if (err.status !== 404) {
            throw err;
          }
          // Document doesn't exist, treat as new
        }
      }

      // Prepare document exactly like SimplifiedDB (no branch assignment)
      const preparedDoc = {
        ...processedData,
        _id: docId,
        updatedAt: new Date().toISOString(),
        ...(isUpdate ? {} : {
          createdAt: new Date().toISOString(),
          createdBy: this.currentUser || user || 'system',
          referenceNumber: processedData.referenceNumber || this.generateReferenceNumber()
        })
      };

      // Remove any undefined values that might cause PouchDB issues (like SimplifiedDB)
      Object.keys(preparedDoc).forEach(key => {
        if (preparedDoc[key] === undefined) {
          delete preparedDoc[key];
        }
      });

      // Set revision if we have an existing document
      if (isUpdate && existingDoc) {
        preparedDoc._rev = existingDoc._rev;
      }

      // Save document using put
      await db.put(preparedDoc);

      // Emit events exactly like SimplifiedDB
      this.emit(isUpdate ? 'documentUpdated' : 'documentCreated', preparedDoc);
      this.emit('dbChange', {
        type: isUpdate ? 'update' : 'create',
        id: docId,
        doc: preparedDoc
      });

      // Trigger sync for all updates
      this.triggerSync(dbKey, 'create_update', {
        docId
      });

      // Return format matching SimplifiedDB: { id, doc }
      return { id: docId, doc: preparedDoc };

    } catch (error) {
      console.error(`[DatabaseHandler] Save failed for ${dbKey}:`, error);
      throw error; // SimplifiedDB throws errors, doesn't return error objects
    }
  }

  /**
   * Process attachments from ElectronDB format to PouchDB format
   */
  async processAttachments(data, db) {
    const processedData = { ...data };
    const attachments = {};

    if (data._attachments) {
      for (const [key, attachment] of Object.entries(data._attachments)) {
        try {
          // Convert array back to Buffer for PouchDB
          const buffer = Buffer.from(attachment.data);

          attachments[key] = {
            content_type: attachment.content_type,
            data: buffer
          };
        } catch (error) {
          console.error(`[DatabaseHandler] Error processing attachment ${key}:`, error);
        }
      }

      // Replace the _attachments with processed ones
      if (Object.keys(attachments).length > 0) {
        processedData._attachments = attachments;
      } else {
        delete processedData._attachments;
      }
    }

    return processedData;
  }

  /**
   * Get a document by ID - matches SimplifiedDB get() method exactly
   */
  async getDocument(dbKey, id) {
    console.log(`[DatabaseHandler] Getting document ${id} from ${dbKey}`);
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const doc = await dbConfig.db.get(id);
      return doc;
    } catch (err) {
      return null; // SimplifiedDB returns null for not found, not an error object
    }
  }

  /**
   * Get all documents - matches SimplifiedDB getAll() method exactly
   */
  async getAllDocuments(dbKey, options = {}) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const { db } = dbConfig;

      const result = await db.allDocs({
        include_docs: true,
        conflicts: true, // Include conflict info for lazy resolution
        ...options
      });

      // Filter exactly like SimplifiedDB: exclude design docs and deleted docs (no branch filtering)
      let docs = result.rows
        .filter(row => row.doc && !row.doc._id.startsWith('_'))
        .map(row => {
          const doc = row.doc;

          // Lazy conflict resolution: mark conflicted docs but don't resolve immediately
          if (doc && doc._conflicts && doc._conflicts.length > 0 && doc._id && doc._rev) {
            // Create a unique key to check if we've already processed these conflicts
            const conflictKey = `${doc._id}_${doc._conflicts.join('_')}`;

            // Only trigger conflict resolution if we haven't already processed these exact conflicts
            if (!this.processedOrphanedConflicts.has(conflictKey)) {
              console.log(`[DatabaseHandler] Document ${doc._id} has ${doc._conflicts.length} conflicts in ${dbKey} - will resolve in background`);

              // Resolve conflicts in background (lazy resolution)
              setImmediate(() => {
                this.handleConflicts({ doc }, 'lazy_resolution');
              });
            } else {
              // Only log occasionally to avoid spam
              if (Math.random() < 0.05) { // Log 5% of the time
                console.log(`[DatabaseHandler] Document ${doc._id} conflicts already processed, skipping lazy resolution`);
              }
            }
          } else if (doc && doc._conflicts && doc._conflicts.length > 0) {
            // Document has conflicts but is missing essential properties
            console.warn(`[DatabaseHandler] Document with conflicts is missing essential properties:`, {
              id: doc._id,
              hasRev: !!doc._rev,
              conflictCount: doc._conflicts ? doc._conflicts.length : 0
            });
          }

          return doc;
        });

      return docs; // SimplifiedDB returns array directly, not wrapped object

    } catch (err) {
      return []; // SimplifiedDB returns empty array on error
    }
  }

  /**
   * Delete a document - matches SimplifiedDB delete() method exactly
   */
  async deleteDocument(dbKey, id, user) {
    const dbConfig = this.databases.get(dbKey);
    if (!dbConfig) {
      throw new Error(`Database ${dbKey} not initialized`);
    }

    const { db } = dbConfig;
    this.currentUser = user || this.currentUser;

    // Get document first (like SimplifiedDB)
    const doc = await this.getDocument(dbKey, id);
    if (!doc) throw new Error(`Document ${id} not found`);

    // Delete from PouchDB using remove (like SimplifiedDB)
    await db.remove(doc);

    // Emit events exactly like SimplifiedDB
    this.emit('documentDeleted', doc);
    this.emit('dbChange', {
      type: 'delete',
      id: doc._id,
      doc: doc
    });

    // Trigger sync
    this.triggerSync(dbKey, 'delete');

    return doc; // SimplifiedDB returns the deleted document
  }

  /**
   * Bulk save documents
   */
  async bulkSave(dbKey, docs, user) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const { db } = dbConfig;
      const timestamp = new Date().toISOString();

      // Prepare documents using SimplifiedDB logic (no branch assignment)
      const baseTimestamp = Date.now();
      const preparedDocs = docs.map((doc, index) => {
        const isUpdate = !!doc._id; // Match SimplifiedDB logic
        const docId = doc._id || (baseTimestamp + index).toString(36).toUpperCase();

        const preparedDoc = {
          ...doc,
          _id: docId,
          updatedAt: timestamp, // Match SimplifiedDB field names
          ...(isUpdate ? {} : {
            createdAt: timestamp,
            createdBy: user?.name || this.currentUser.name,
            referenceNumber: doc.referenceNumber || this.generateReferenceNumber('', index + 1)
          })
        };

        return preparedDoc;
      });

      const result = await db.bulkDocs(preparedDocs);

      // Emit change event
      this.emit('documentChanged', {
        dbKey,
        type: 'bulk_create',
        docs: preparedDocs
      });

      // Trigger sync
      this.triggerSync(dbKey, 'bulk_create');

      return {
        success: true,
        results: result.map(res => ({ id: res.id, rev: res.rev }))
      };

    } catch (error) {
      console.error(`[DatabaseHandler] Bulk save failed for ${dbKey}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Find documents using Mango queries
   */
  async findDocuments(dbKey, selector, options = {}) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const { db } = dbConfig;

      const result = await db.find({
        selector,
        ...options
      });

      // Return all documents (no branch filtering)
      return result.docs;

    } catch (error) {
      console.error(`[DatabaseHandler] Find failed for ${dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Get all documents with options (PouchDB allDocs equivalent)
   * This is what useSimpleTableData hook expects
   */
  async allDocs(dbKey, options = {}) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const result = await dbConfig.db.allDocs(options);
      return result;
    } catch (error) {
      console.error(`[DatabaseHandler] AllDocs failed for ${dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Process large conflict sets in chunks to reduce memory usage
   */
  async processLargeConflictSet(winningRev, conflictDocs) {
    // Sort all documents by timestamp first
    const allDocs = [winningRev, ...conflictDocs].sort((a, b) => {
      const aTime = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
      const bTime = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;

      if (bTime !== aTime) {
        return bTime - aTime;
      }

      return a._id.localeCompare(b._id);
    });

    // Start with the newest document
    let mergedDoc = { ...allDocs[0] };
    const chunkSize = 3; // Process 3 documents at a time

    // Process remaining documents in chunks
    for (let i = 1; i < allDocs.length; i += chunkSize) {
      const chunk = allDocs.slice(i, i + chunkSize);

      // Merge this chunk
      for (const doc of chunk) {
        mergedDoc = this.deepMergeWithArrays(mergedDoc, doc);
      }

      // Allow event loop to process other tasks
      await new Promise(resolve => setImmediate(resolve));
    }

    return mergedDoc;
  }



  /**
   * Trigger sync for a database with optional metadata
   */
  triggerSync(dbKey, operation, metadata = {}) {
    // Emit sync trigger event to be handled by SyncService
    this.emit('syncTrigger', {
      dbKey,
      operation,
      ...metadata
    });
  }

  /**
   * Handle conflicts using simple and efficient last-write-wins strategy
   * OPTIMIZED: Handles missing revisions gracefully
   * OPTIMIZED: Prevents duplicate conflict resolution processing
   * @param {Object} change - The change object from PouchDB sync
   * @param {string} source - Source of the conflict (lan, remote, local)
   */
  async handleConflicts(change, source = 'unknown') {
    console.log(`[DatabaseHandler] ===== STARTING CONFLICT RESOLUTION =====`);
    console.log(`[DatabaseHandler] Source: ${source}`);
    // console.log(`[DatabaseHandler] Change object:`, JSON.stringify(change, null, 2));

    try {
      // Enhanced validation to prevent undefined errors
      if (!change || !change.doc || !change.doc._conflicts || !change.doc._id) {
        console.warn(`[DatabaseHandler] ❌ VALIDATION FAILED - Invalid change object passed to handleConflicts:`, change);
        return;
      }

      const conflicts = change.doc._conflicts;
      const docId = change.doc._id;
      const winningRev = change.doc;

      console.log(`[DatabaseHandler] ✅ VALIDATION PASSED - Document: ${docId}, Conflicts: ${conflicts.length}`);

      // Validate that we have essential document properties
      if (!winningRev._rev) {
        console.warn(`[DatabaseHandler] ❌ MISSING _REV - Document ${docId} missing _rev property, skipping conflict resolution`);
        return;
      }

      console.log(`[DatabaseHandler] ✅ HAS _REV - Document ${docId} has revision ${winningRev._rev}`);

      // Special handling for design documents - skip to avoid conflicts
      if (docId.startsWith('_design/')) {
        console.log(`[DatabaseHandler] ⏭️ SKIPPING DESIGN DOC - Skipping design document conflict resolution for ${docId}`);
        return;
      }

      // DUPLICATION FIX: Prevent concurrent conflict resolution for the same document
      const conflictKey = `${docId}_${winningRev._rev}`;
      if (this.activeConflictResolutions.has(conflictKey)) {
        console.log(`[DatabaseHandler] ⏭️ ALREADY IN PROGRESS - Conflict resolution already in progress for ${docId}, skipping`);
        return;
      }

      console.log(`[DatabaseHandler] ✅ PROCEEDING - Starting conflict resolution for ${docId}`);


      this.activeConflictResolutions.add(conflictKey);

      try {
        console.log(`[DatabaseHandler] Resolving ${conflicts.length} conflicts for ${docId} from ${source}`);
        this.conflictStats.totalConflicts++;

        // Log conflict details for debugging
        console.log(`[DatabaseHandler] Conflict details - Document: ${docId}, Conflicts: ${conflicts.join(', ')}, Source: ${source}`);

        // Find the database this document belongs to
        const dbKey = this.findDatabaseForDocument(docId, change);
        if (!dbKey) {
          console.warn(`[DatabaseHandler] Could not find database for document ${docId}`);
          return;
        }
        console.log(`[DatabaseHandler] Found database ${dbKey} for document ${docId}`);

        const dbConfig = this.databases.get(dbKey);
        if (!dbConfig) {
          console.warn(`[DatabaseHandler] Database config not found for ${dbKey}`);
          return;
        }
        console.log(`[DatabaseHandler] Retrieved database config for ${dbKey}`);

        // Early check: verify the document exists before attempting conflict resolution
        try {
          await dbConfig.db.get(docId);
          console.log(`[DatabaseHandler] Document ${docId} exists, proceeding with conflict resolution`);
        } catch (error) {
          if (error.status === 404) {
            console.log(`[DatabaseHandler] Document ${docId} no longer exists, cleaning up orphaned conflicts`);
            // Document was deleted, but we need to clean up the conflict revisions
            await this.cleanupOrphanedConflicts(dbConfig.db, docId, conflicts);
            return;
          }
          console.warn(`[DatabaseHandler] Error checking document existence for ${docId}:`, error.message);
          // For other errors, continue with conflict resolution as the document might still be recoverable
        }

        // Check if the document still exists before trying to resolve conflicts
        let currentDoc;
        try {
          currentDoc = await dbConfig.db.get(docId);
          console.log(`[DatabaseHandler] Retrieved current document ${docId} with revision ${currentDoc._rev}`);
        } catch (error) {
          if (error.status === 404) {
            console.log(`[DatabaseHandler] Document ${docId} no longer exists, skipping conflict resolution`);
            return; // Document was deleted, no need to resolve conflicts
          }
          console.error(`[DatabaseHandler] Error retrieving current document ${docId}:`, error.message);
          throw error; // Re-throw other errors
        }

        // Simple and efficient conflict resolution - just use the winning revision
        // This eliminates the "missing revision" errors and reduces complexity
        console.log(`[DatabaseHandler] Creating merged document for ${docId}`);
        const mergedDoc = { ...winningRev };
        mergedDoc.updatedAt = new Date().toISOString();

        // Ensure we have the current revision
        mergedDoc._rev = currentDoc._rev;
        console.log(`[DatabaseHandler] Set merged document revision to ${mergedDoc._rev} for ${docId}`);

        // Remove any reserved PouchDB fields that might cause validation errors
        delete mergedDoc._hasConflicts;
        delete mergedDoc._conflicts; // This will be removed automatically after resolution

        // Add simple conflict resolution metadata (non-reserved field)
        mergedDoc.conflictResolution = {
          strategy: 'simpleLastWriteWins',
          resolvedAt: new Date().toISOString(),
          conflictCount: conflicts.length,
          resolvedBy: 'DatabaseHandler',
          source: source
        };
        console.log(`[DatabaseHandler] Added conflict resolution metadata to ${docId}`);

        // Use put to update the document (we already have the current _rev)
        try {
          console.log(`[DatabaseHandler] Attempting to save merged document ${docId}`);
          await dbConfig.db.put(mergedDoc);
          console.log(`[DatabaseHandler] Successfully saved merged document ${docId}`);
        } catch (putError) {
          if (putError.status === 404) {
            console.log(`[DatabaseHandler] Document ${docId} was deleted during conflict resolution, skipping`);
            return; // Document was deleted while we were processing
          }
          console.error(`[DatabaseHandler] Put failed for ${docId}:`, putError.message);
          throw putError;
        }

        // Remove conflict revisions - handle missing revisions gracefully
        console.log(`[DatabaseHandler] Removing ${conflicts.length} conflict revisions for ${docId}`);
        let removedCount = 0;
        for (const conflictRev of conflicts) {
          try {
            console.log(`[DatabaseHandler] Attempting to remove conflict revision ${conflictRev} for ${docId}`);
            await dbConfig.db.remove(docId, conflictRev);
            removedCount++;
            console.log(`[DatabaseHandler] Successfully removed conflict revision ${conflictRev} for ${docId}`);
          } catch (error) {
            // Silently ignore missing revisions - they may have been cleaned up
            if (error.status !== 404) {
              console.warn(`[DatabaseHandler] Could not remove conflict revision ${conflictRev}:`, error.message);
            } else {
              console.log(`[DatabaseHandler] Conflict revision ${conflictRev} for ${docId} was already removed`);
            }
          }
        }
        console.log(`[DatabaseHandler] Removed ${removedCount}/${conflicts.length} conflict revisions for ${docId}`);


        // Emit conflict resolution event
        this.emit('conflictsResolved', {
          dbKey,
          docId,
          conflictCount: conflicts.length,
          source,
          mergedDoc,
          strategy: 'simpleLastWriteWins'
        });

        this.conflictStats.resolvedConflicts++;

        // Final verification: check if conflicts were actually resolved
        let conflictsActuallyResolved = false;
        try {
          const verificationDoc = await dbConfig.db.get(docId, { conflicts: true });
          if (verificationDoc._conflicts && verificationDoc._conflicts.length > 0) {
            console.warn(`[DatabaseHandler] Conflict resolution for ${docId} completed but document still has ${verificationDoc._conflicts.length} conflicts:`, verificationDoc._conflicts);
            conflictsActuallyResolved = false;
          } else {
            console.log(`[DatabaseHandler] Successfully resolved all conflicts for ${docId} - verification passed`);
            conflictsActuallyResolved = true;
          }
        } catch (verifyError) {
          console.warn(`[DatabaseHandler] Could not verify conflict resolution for ${docId}:`, verifyError.message);
          conflictsActuallyResolved = false;
        }

        // ONLY mark as processed if conflicts were actually resolved
        if (conflictsActuallyResolved) {
          const conflictKey = `${docId}_${conflicts.join('_')}`;
          this.processedOrphanedConflicts.add(conflictKey);
          console.log(`[DatabaseHandler] Marked conflict set as successfully processed for ${docId}`);
        } else {
          console.warn(`[DatabaseHandler] NOT marking ${docId} as processed - conflicts still exist`);
        }

        console.log(`[DatabaseHandler] Successfully resolved conflicts for ${docId}`);

      } finally {
        // Always remove from active resolutions, even if an error occurred
        this.activeConflictResolutions.delete(conflictKey);
      }

    } catch (error) {
      this.conflictStats.failedResolutions++;
      console.error(`[DatabaseHandler] ❌ CRITICAL ERROR - Conflict resolution failed completely:`, error);
      console.error(`[DatabaseHandler] ❌ ERROR STACK:`, error.stack);
      console.error(`[DatabaseHandler] ❌ ERROR DETAILS:`, {
        message: error.message,
        status: error.status,
        name: error.name,
        docId: change?.doc?._id,
        conflicts: change?.doc?._conflicts
      });
    }
  }

  /**
   * Find which database a document belongs to based on the change event
   */
  findDatabaseForDocument(docId, change) {
    // Strategy 1: If we only have one database, use it
    if (this.databases.size === 1) {
      return Array.from(this.databases.keys())[0];
    }

    // Strategy 2: Try to find the database by checking which one contains this document
    for (const [dbKey, dbConfig] of this.databases) {
      try {
        // Try a quick existence check (this is async but we'll handle it in the caller)
        // For now, return the first database as a fallback
        return dbKey;
      } catch (error) {
        // Continue to next database
        continue;
      }
    }

    // Strategy 3: Fallback to first available database
    return Array.from(this.databases.keys())[0] || null;
  }

  /**
   * Handle conflicts specifically for design documents
   * Design documents require special merging of views and other properties
   */
  async handleDesignDocumentConflicts(change, source = 'unknown') {
    try {
      // Enhanced validation to prevent undefined errors
      if (!change || !change.doc || !change.doc._conflicts || !change.doc._id) {
        console.warn(`[DatabaseHandler] Invalid change object passed to handleDesignDocumentConflicts:`, change);
        return;
      }

      const conflicts = change.doc._conflicts;
      const docId = change.doc._id;
      const winningRev = change.doc;

      // Validate that we have essential document properties
      if (!winningRev._rev) {
        console.warn(`[DatabaseHandler] Design document ${docId} missing _rev property, skipping conflict resolution`);
        return;
      }

      console.log(`[DatabaseHandler] Handling design document conflicts for ${docId} from ${source}`);

      // Prevent concurrent conflict resolution for the same document
      const conflictKey = `${docId}_${winningRev._rev}_design`;
      if (this.activeConflictResolutions.has(conflictKey)) {
        console.log(`[DatabaseHandler] Design document conflict resolution already in progress for ${docId}`);
        return;
      }

      this.activeConflictResolutions.add(conflictKey);

      try {
        // Find the database configuration
        const dbKey = this.findDatabaseKeyByDocument(docId);
        if (!dbKey) {
          console.warn(`[DatabaseHandler] Could not find database for design document ${docId}`);
          return;
        }

        const dbConfig = this.databases.get(dbKey);
        if (!dbConfig) {
          console.warn(`[DatabaseHandler] Database configuration not found for ${dbKey}`);
          return;
        }

        // Get all conflicting revisions
        const conflictDocs = await this.getConflictingRevisions(dbConfig.db, docId, conflicts);

        // Resolve design document conflicts using specialized merging
        const resolvedDoc = await this.resolveDesignDocumentConflicts(winningRev, conflictDocs);

        // Apply the resolved document
        await this.applyResolvedDocument(dbConfig.db, docId, resolvedDoc, conflicts);

        // Emit conflict resolution event
        this.emit('conflictsResolved', {
          dbKey,
          docId,
          conflictCount: conflicts.length,
          source,
          resolvedDoc,
          type: 'design_document'
        });

        this.conflictStats.resolvedConflicts++;
        console.log(`[DatabaseHandler] Successfully resolved design document conflicts for ${docId}`);

      } finally {
        // Always remove from active resolutions, even if an error occurred
        this.activeConflictResolutions.delete(conflictKey);
      }

    } catch (error) {
      console.error(`[DatabaseHandler] Error handling design document conflicts for ${change.doc._id}:`, error);
      this.conflictStats.failedConflicts++;
    }
  }

  /**
   * Quick conflict resolution for simple cases (performance optimization)
   * Handles single conflicts with close timestamps efficiently
   */
  async quickConflictResolution(db, docId, winningRev, conflictRev) {
    try {
      // First check if the document still exists
      let currentDoc;
      try {
        currentDoc = await db.get(docId);
      } catch (error) {
        if (error.status === 404) {
          console.log(`[DatabaseHandler] Document ${docId} no longer exists for quick conflict resolution`);
          return null; // Document was deleted, skip resolution
        }
        throw error;
      }

      // Get the conflicting revision
      let conflictDoc;
      try {
        conflictDoc = await db.get(docId, { rev: conflictRev });
      } catch (error) {
        if (error.status === 404) {
          console.log(`[DatabaseHandler] Conflict revision ${conflictRev} no longer exists for ${docId}`);
          return null; // Conflict revision was cleaned up, skip resolution
        }
        throw error;
      }

      // Check if timestamps are very close (within 1 second) - likely same user
      const winningTime = new Date(winningRev.updatedAt || winningRev.createdAt || 0).getTime();
      const conflictTime = new Date(conflictDoc.updatedAt || conflictDoc.createdAt || 0).getTime();
      const timeDiff = Math.abs(winningTime - conflictTime);

      // If timestamps are very close and documents are similar, use simple resolution
      if (timeDiff < 1000) {
        const newerDoc = winningTime >= conflictTime ? winningRev : conflictDoc;
        const olderDoc = winningTime >= conflictTime ? conflictDoc : winningRev;

        // Simple merge: newer doc + any unique fields from older doc
        const mergedDoc = { ...newerDoc };

        // Add any unique fields from the older document
        for (const key in olderDoc) {
          if (!key.startsWith('_') && !mergedDoc.hasOwnProperty(key)) {
            mergedDoc[key] = olderDoc[key];
          }
        }

        // Ensure proper metadata
        mergedDoc._id = docId;
        mergedDoc._rev = currentDoc._rev; // Use the current document's revision
        mergedDoc.updatedAt = new Date().toISOString();

        // Remove any reserved PouchDB fields that might cause validation errors
        delete mergedDoc._hasConflicts;
        delete mergedDoc._conflicts;

        // Add simple conflict resolution metadata
        mergedDoc.conflictResolution = {
          strategy: 'quickResolution',
          resolvedAt: new Date().toISOString(),
          conflictCount: 1,
          resolvedBy: 'DatabaseHandler',
          timeDifference: timeDiff
        };

        // Apply the resolved document using put (we already have the current _rev)
        try {
          await db.put(mergedDoc);
        } catch (putError) {
          if (putError.status === 404) {
            console.log(`[DatabaseHandler] Document ${docId} was deleted during quick conflict resolution, skipping`);
            return null; // Document was deleted while we were processing
          }
          console.error(`[DatabaseHandler] Quick resolution put failed for ${docId}:`, putError.message);
          throw putError;
        }

        // Remove conflict revision
        try {
          await db.remove(docId, conflictRev);
        } catch (error) {
          console.warn(`[DatabaseHandler] Could not remove conflict revision ${conflictRev}:`, error.message);
        }

        return mergedDoc;
      }

      // If not suitable for quick resolution, return null to fall back to full resolution
      return null;

    } catch (error) {
      console.warn(`[DatabaseHandler] Quick conflict resolution failed for ${docId}:`, error.message);
      return null; // Fall back to full resolution
    }
  }

  /**
   * Clean up orphaned conflict revisions when the main document is missing
   * This prevents conflicts from persisting forever in the database
   */
  async cleanupOrphanedConflicts(db, docId, conflicts) {
    try {
      // Create a unique key for this cleanup operation
      const cleanupKey = `${docId}_${conflicts.join('_')}`;

      // Check if we've already processed this exact set of conflicts
      if (this.processedOrphanedConflicts.has(cleanupKey)) {
        // Only log this occasionally to avoid spam
        if (Math.random() < 0.1) { // Log 10% of the time
          console.log(`[DatabaseHandler] Orphaned conflicts for ${docId} already processed, skipping`);
        }
        return;
      }

      // First, check if any conflicts actually exist to avoid unnecessary logging
      let existingConflicts = [];
      for (const conflictRev of conflicts) {
        try {
          await db.get(docId, { rev: conflictRev });
          existingConflicts.push(conflictRev);
        } catch (error) {
          // Conflict revision doesn't exist, skip it
        }
      }

      // If no conflicts actually exist, mark as processed and skip cleanup
      if (existingConflicts.length === 0) {
        this.processedOrphanedConflicts.add(cleanupKey);
        console.log(`[DatabaseHandler] All orphaned conflicts for ${docId} were already cleaned up, marking as processed`);
        return;
      }

      console.log(`[DatabaseHandler] Cleaning up ${existingConflicts.length}/${conflicts.length} orphaned conflicts for ${docId}`);

      let cleanedCount = 0;
      for (const conflictRev of existingConflicts) {
        try {
          // Get the conflict revision (we know it exists from the check above)
          const conflictDoc = await db.get(docId, { rev: conflictRev });

          // Remove the conflict revision
          await db.remove(conflictDoc._id, conflictDoc._rev);
          cleanedCount++;
          console.log(`[DatabaseHandler] Removed orphaned conflict revision ${conflictRev} for ${docId}`);

        } catch (error) {
          if (error.status === 404) {
            // Conflict revision was removed between our check and removal attempt
            cleanedCount++;
            console.log(`[DatabaseHandler] Orphaned conflict revision ${conflictRev} for ${docId} was removed during cleanup`);
          } else {
            console.warn(`[DatabaseHandler] Could not remove orphaned conflict revision ${conflictRev}:`, error.message);
          }
        }
      }

      // Mark this cleanup operation as processed
      this.processedOrphanedConflicts.add(cleanupKey);

      if (cleanedCount > 0) {
        console.log(`[DatabaseHandler] Successfully cleaned up ${cleanedCount}/${existingConflicts.length} orphaned conflicts for ${docId}`);

        // IMPORTANT: Update the main document to remove _conflicts metadata
        // This prevents repeated cleanup attempts for the same document
        try {
          // Note: Since the main document was deleted (that's why we're in orphaned cleanup),
          // we don't need to update it. The _conflicts metadata will be gone when the document is gone.
          console.log(`[DatabaseHandler] Orphaned conflicts cleanup completed for deleted document ${docId}`);
        } catch (error) {
          // This is expected for orphaned conflicts - the main document is already gone
          console.log(`[DatabaseHandler] Main document ${docId} was already deleted, conflicts cleanup complete`);
        }

        // Update stats
        this.conflictStats.orphanedConflictsCleanedUp += cleanedCount;

        // Emit cleanup event
        this.emit('orphanedConflictsCleanedUp', {
          docId,
          conflictCount: existingConflicts.length,
          cleanedCount,
          strategy: 'orphanedCleanup'
        });
      }

    } catch (error) {
      console.error(`[DatabaseHandler] Error cleaning up orphaned conflicts for ${docId}:`, error);
    }
  }

  /**
   * Get all conflicting document revisions
   */
  async getConflictingRevisions(db, docId, conflicts) {
    try {
      const conflictDocs = [];

      // Process conflicts one by one to avoid overwhelming the database
      for (const rev of conflicts) {
        try {
          const doc = await db.get(docId, { rev });
          conflictDocs.push(doc);
        } catch (error) {
          // Only log if it's not a simple "not found" error
          if (error.status !== 404) {
            console.warn(`[DatabaseHandler] Could not get conflict revision ${rev}:`, error.message);
          }
          // Skip missing revisions - they may have been cleaned up already
        }
      }

      return conflictDocs;
    } catch (error) {
      console.error(`[DatabaseHandler] Error getting conflict revisions:`, error);
      return [];
    }
  }

  /**
   * Resolve design document conflicts with specialized view merging
   * Design documents need careful handling of views, validate_doc_update, etc.
   */
  async resolveDesignDocumentConflicts(winningRev, conflictDocs) {
    const allDocs = [winningRev, ...conflictDocs];

    console.log(`[DatabaseHandler] Resolving design document conflicts for ${winningRev._id} with ${conflictDocs.length} conflicting revisions`);

    // Sort by timestamp with deterministic tiebreaker (same as regular docs)
    const sortedDocs = allDocs.sort((a, b) => {
      const aTime = new Date(a.updatedAt || a.createdAt || 0).getTime();
      const bTime = new Date(b.updatedAt || b.createdAt || 0).getTime();

      // Primary sort: by timestamp (newest first)
      if (bTime !== aTime) {
        return bTime - aTime;
      }

      // DETERMINISTIC TIEBREAKER: If timestamps are equal, use document ID
      return a._id.localeCompare(b._id);
    });

    // Start with the newest document as base
    const newestDoc = sortedDocs[0];
    const mergedDoc = { ...newestDoc };

    // Merge views from all documents
    const allViews = {};

    // Collect all unique views from all document versions
    sortedDocs.forEach(doc => {
      if (doc.views) {
        Object.keys(doc.views).forEach(viewName => {
          if (!allViews[viewName]) {
            allViews[viewName] = doc.views[viewName];
          } else {
            // If view exists, prefer the one from the newer document
            // but merge any unique properties
            allViews[viewName] = {
              ...allViews[viewName],
              ...doc.views[viewName]
            };
          }
        });
      }
    });

    // Apply merged views
    if (Object.keys(allViews).length > 0) {
      mergedDoc.views = allViews;
    }

    // Merge other design document properties (validate_doc_update, filters, etc.)
    const designProperties = ['validate_doc_update', 'filters', 'shows', 'lists', 'updates', 'rewrites'];

    designProperties.forEach(prop => {
      const propValues = sortedDocs
        .map(doc => doc[prop])
        .filter(val => val !== undefined);

      if (propValues.length > 0) {
        // Use the value from the newest document that has this property
        mergedDoc[prop] = propValues[0];
      }
    });

    // Ensure proper metadata
    mergedDoc._id = winningRev._id;
    mergedDoc._rev = winningRev._rev;
    mergedDoc.updatedAt = new Date().toISOString();

    // Add conflict resolution metadata
    mergedDoc.conflictResolution = {
      strategy: 'designDocumentMerge',
      resolvedAt: new Date().toISOString(),
      conflictCount: conflictDocs.length,
      resolvedBy: 'DatabaseHandler',
      mergedViews: Object.keys(allViews),
      mergedProperties: designProperties.filter(prop => mergedDoc[prop] !== undefined)
    };

    console.log(`[DatabaseHandler] Design document merge completed: ${Object.keys(allViews).length} views merged`);

    return mergedDoc;
  }

  /**
   * Resolve conflicts using deterministic last-write-wins strategy with smart merging
   * Enhanced to match Database/index.js implementation
   */
  async resolveLastWriteWins(winningRev, conflictDocs) {
    const allDocs = [winningRev, ...conflictDocs];

    console.log(`[DatabaseHandler] Resolving conflicts for ${winningRev._id} with ${conflictDocs.length} conflicting revisions`);

    // Sort by timestamp with deterministic tiebreaker (same as Database/index.js)
    const sortedDocs = allDocs.sort((a, b) => {
      const aTime = new Date(a.updatedAt || a.createdAt || 0).getTime();
      const bTime = new Date(b.updatedAt || b.createdAt || 0).getTime();

      // Primary sort: by timestamp (newest first)
      if (bTime !== aTime) {
        return bTime - aTime;
      }

      // DETERMINISTIC TIEBREAKER: If timestamps are equal, use document ID
      // This ensures consistent conflict resolution across all clients
      return a._id.localeCompare(b._id);
    });

    // Start with the newest document as base
    const newestDoc = sortedDocs[0];

    // Smart merge: preserve unique fields from older revisions using enhanced deep merge
    // This ensures we keep the most recent data while preserving unique fields from older revisions
    const mergedDoc = sortedDocs.slice(1).reduce((merged, doc) => {
      return this.deepMergeWithArrays(merged, doc);
    }, { ...newestDoc });

    // Ensure proper metadata
    mergedDoc._id = winningRev._id;
    mergedDoc._rev = winningRev._rev;
    mergedDoc.updatedAt = new Date().toISOString();

    // Add enhanced conflict resolution metadata
    mergedDoc.conflictResolution = {
      strategy: 'lastWriteWinsWithSmartMerge',
      resolvedAt: new Date().toISOString(),
      conflictCount: conflictDocs.length,
      resolvedBy: 'DatabaseHandler',
      mergedFromRevisions: sortedDocs.map(doc => doc._rev),
      newestRevision: newestDoc._rev,
      preservedFields: this.getPreservedFields(newestDoc, mergedDoc)
    };

    console.log(`[DatabaseHandler] Conflict resolution completed for ${winningRev._id}, merged ${conflictDocs.length} revisions`);

    return mergedDoc;
  }

  /**
   * Apply the resolved document to the database and clean up conflicts
   */
  async applyResolvedDocument(db, docId, resolvedDoc, conflicts) {
    try {
      // Check if the document still exists before trying to apply resolution
      let currentDoc;
      try {
        currentDoc = await db.get(docId);
        resolvedDoc._rev = currentDoc._rev; // Ensure we have the current revision
      } catch (error) {
        if (error.status === 404) {
          console.log(`[DatabaseHandler] Document ${docId} no longer exists, skipping apply resolved document`);
          return; // Document was deleted, no need to apply resolution
        }
        throw error; // Re-throw other errors
      }

      // Update the document using put (we already have the current _rev)
      try {
        await db.put(resolvedDoc);
      } catch (putError) {
        if (putError.status === 404) {
          console.log(`[DatabaseHandler] Document ${docId} was deleted during apply resolved document, skipping`);
          return; // Document was deleted while we were processing
        }
        console.error(`[DatabaseHandler] Apply resolved document put failed for ${docId}:`, putError.message);
        throw putError;
      }

      // Remove conflict revisions
      for (const conflictRev of conflicts) {
        try {
          await db.remove(docId, conflictRev);
        } catch (error) {
          // It's okay if we can't remove a conflict revision - it might already be gone
          console.warn(`[DatabaseHandler] Could not remove conflict revision ${conflictRev}:`, error.message);
        }
      }
    } catch (error) {
      console.error(`[DatabaseHandler] Error applying resolved document:`, error);
      throw error;
    }
  }

  /**
   * Deep merge objects with array handling - ENHANCED DEDUPLICATION
   * Fixes duplication issues by improving array merging and object comparison
   */
  deepMergeWithArrays(target, source) {
    if (!source) return target;
    if (!target) return source;

    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (key === '_id' || key === '_rev' || key === '_conflicts') {
          // Skip PouchDB metadata fields during merge
          continue;
        }

        if (result[key] === undefined) {
          // If target doesn't have this field, take it from source
          result[key] = source[key];
        } else if (Array.isArray(result[key]) && Array.isArray(source[key])) {
          // ENHANCED ARRAY MERGING: Better deduplication logic
          result[key] = this.mergeArraysWithDeduplication(result[key], source[key]);
        } else if (typeof result[key] === 'object' && typeof source[key] === 'object' &&
                   result[key] !== null && source[key] !== null) {
          // Recursively merge objects
          result[key] = this.deepMergeWithArrays(result[key], source[key]);
        }
        // For primitive values, keep the target value (newest document wins)
      }
    }

    return result;
  }

  /**
   * Find database key by document ID (helper for design document conflicts)
   */
  findDatabaseKeyByDocument(docId) {
    // For design documents, we need to find which database they belong to
    // This is a simple approach - in practice, you might need more sophisticated logic
    for (const [dbKey, dbConfig] of this.databases) {
      // Check if this database might contain this design document
      // Design documents are typically database-specific
      if (dbConfig && dbConfig.db) {
        return dbKey; // Return the first available database key
      }
    }

    // Fallback: return the first database key if available
    const firstDbKey = Array.from(this.databases.keys())[0];
    if (firstDbKey) {
      console.warn(`[DatabaseHandler] Using fallback database ${firstDbKey} for design document ${docId}`);
      return firstDbKey;
    }

    return null;
  }

  /**
   * Enhanced array merging with better deduplication
   * Handles objects with _id, id, or value properties for proper comparison
   */
  mergeArraysWithDeduplication(targetArray, sourceArray) {
    const combined = [...targetArray, ...sourceArray];
    const uniqueItems = [];
    const seenKeys = new Set();

    for (const item of combined) {
      let uniqueKey;

      if (typeof item === 'object' && item !== null) {
        // For objects, create a unique key based on _id, id, value, or full content
        if (item._id) {
          uniqueKey = `obj_id_${item._id}`;
        } else if (item.id) {
          uniqueKey = `obj_id_${item.id}`;
        } else if (item.value !== undefined) {
          uniqueKey = `obj_value_${JSON.stringify(item.value)}`;
        } else {
          // For objects without clear identifiers, use sorted JSON
          uniqueKey = `obj_full_${JSON.stringify(this.sortObjectKeys(item))}`;
        }
      } else {
        // For primitives, use the value directly
        uniqueKey = `primitive_${item}`;
      }

      if (!seenKeys.has(uniqueKey)) {
        seenKeys.add(uniqueKey);
        uniqueItems.push(item);
      }
    }

    return uniqueItems;
  }

  /**
   * Sort object keys for consistent comparison
   */
  sortObjectKeys(obj) {
    if (typeof obj !== 'object' || obj === null) return obj;
    if (Array.isArray(obj)) return obj.map(item => this.sortObjectKeys(item));

    const sorted = {};
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = this.sortObjectKeys(obj[key]);
    });
    return sorted;
  }

  /**
   * Get fields that were preserved during merge (for enhanced metadata)
   * Matches Database/index.js approach for consistency
   */
  getPreservedFields(newestDoc, mergedDoc) {
    const preserved = [];

    for (const key in mergedDoc) {
      if (mergedDoc.hasOwnProperty(key) && !key.startsWith('_') && key !== 'conflictResolution') {
        // Check if this field came from an older revision or was modified during merge
        if (!newestDoc.hasOwnProperty(key)) {
          preserved.push(`${key} (added from older revision)`);
        } else if (JSON.stringify(newestDoc[key]) !== JSON.stringify(mergedDoc[key])) {
          preserved.push(`${key} (merged from multiple revisions)`);
        }
      }
    }

    return preserved;
  }

  /**
   * Get conflict resolution statistics
   */
  getConflictStats() {
    return { ...this.conflictStats };
  }

  /**
   * Reset conflict resolution statistics
   */
  resetConflictStats() {
    this.conflictStats = {
      totalConflicts: 0,
      resolvedConflicts: 0,
      failedResolutions: 0
    };
  }

  /**
   * Get database sync status
   */
  getSyncStatus(dbKey) {
    return { isHealthy: true, lastSyncTimes: {}, pendingOperations: new Set() };
  }

  /**
   * Update LAN details for a database
   */
  updateLanDetails(dbKey, lan_details) {
    const dbConfig = this.databases.get(dbKey);
    if (!dbConfig) {
      return { success: false, error: 'Database not found' };
    }

    dbConfig.lan_details = lan_details;
    dbConfig.lanString = lan_details
      ? `http://${lan_details.username}:${lan_details.password}@${lan_details.host}:${lan_details.port}/`
      : null;

    return { success: true };
  }

  /**
   * Authenticate with a database (for LAN authentication)
   */
  async authenticate(dbKey, username, password) {
    const dbConfig = this.databases.get(dbKey);
    if (!dbConfig) {
      throw new Error('Database not found');
    }

    if (!dbConfig.lanString) {
      throw new Error('No LAN configuration available for authentication');
    }

    try {
      // For LAN authentication, we'll try to connect to the remote database
      // and verify credentials by attempting a simple operation
      const remoteUrl = `${dbConfig.lanString}${dbConfig.databasePrefix}${dbConfig.name}`;

      // Create a temporary PouchDB instance to test authentication
      const testDb = new PouchDB(remoteUrl);

      // Try to get database info to verify authentication
      const info = await testDb.info();

      // Close the test connection
      await testDb.close();

      return {
        authenticated: true,
        database: dbConfig.name,
        info
      };
    } catch (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Get all initialized databases
   */
  getAllDatabases() {
    return Array.from(this.databases.keys());
  }

  /**
   * Close a database
   */
  async closeDatabase(dbKey) {
    const dbConfig = this.databases.get(dbKey);
    if (dbConfig) {
      await dbConfig.db.close();
      this.databases.delete(dbKey);
    }
    return { success: true };
  }

  /**
   * Close all databases
   */
  async closeAllDatabases() {
    const promises = Array.from(this.databases.keys()).map(dbKey =>
      this.closeDatabase(dbKey)
    );
    await Promise.all(promises);
  }

  /**
   * Get an attachment from a document
   */
  async getAttachment(dbKey, id, name) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      // Validate input parameters
      if (!id || typeof id !== 'string' || !name || typeof name !== 'string') {
        console.log(`[DatabaseHandler] Invalid parameters for getAttachment: id='${id}', name='${name}' in ${dbKey}`);
        return null;
      }

      const blob = await dbConfig.db.getAttachment(id, name);

      // Convert blob to base64 in Node.js environment
      if (blob) {
        // In Node.js, PouchDB returns a Buffer for attachments
        if (Buffer.isBuffer(blob)) {
          return `data:application/octet-stream;base64,${blob.toString('base64')}`;
        }
        // If it's a Blob-like object, convert to buffer first
        if (blob.arrayBuffer) {
          const arrayBuffer = await blob.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          return `data:application/octet-stream;base64,${buffer.toString('base64')}`;
        }
        // Fallback: try to convert directly
        const buffer = Buffer.from(blob);
        return `data:application/octet-stream;base64,${buffer.toString('base64')}`;
      }

      return null;
    } catch (error) {
      // Handle 404 (not found) errors gracefully - this is expected when attachments don't exist
      if (error.status === 404 || error.error === true || error.name === 'not_found') {
        console.log(`[DatabaseHandler] Attachment '${name}' not found for document '${id}' in ${dbKey}`);
        return null;
      }

      // Log other errors but still return null to prevent crashes
      console.error(`[DatabaseHandler] Get attachment failed for ${dbKey}:`, error);
      return null;
    }
  }

  /**
   * Put an attachment to a document
   */
  async putAttachment(dbKey, id, name, rev, attachment, type) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const result = await dbConfig.db.putAttachment(id, name, rev, attachment, type);
      return result;
    } catch (error) {
      console.error(`[DatabaseHandler] Put attachment failed for ${dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Remove an attachment from a document
   */
  async removeAttachment(dbKey, id, name, rev) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const result = await dbConfig.db.removeAttachment(id, name, rev);
      return result;
    } catch (error) {
      console.error(`[DatabaseHandler] Remove attachment failed for ${dbKey}:`, error);
      throw error;
    }
  }

  /**
   * Create an index for faster queries
   */
  async createIndex(dbKey, index) {
    const dbConfig = this.databases.get(dbKey);
    if (!dbConfig) {
      throw new Error(`Database ${dbKey} not initialized`);
    }
    return await dbConfig.db.createIndex(index);
  }

  /**
   * Force sync for a specific database
   */
  async forceSync(dbKey) {
    // Trigger sync through the sync service
    this.triggerSync(dbKey, 'force_sync');
    return { success: true };
  }

  /**
   * Manually resolve conflicts for a specific document
   */
  async resolveDocumentConflicts(dbKey, docId) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      // Get the document with conflicts
      const doc = await dbConfig.db.get(docId, { conflicts: true });

      if (!doc._conflicts || doc._conflicts.length === 0) {
        return { success: true, message: 'No conflicts found' };
      }

      // Create a change event and handle conflicts
      await this.handleConflicts({ doc }, 'manual');

      return { success: true, message: `Resolved ${doc._conflicts.length} conflicts` };
    } catch (error) {
      console.error(`[DatabaseHandler] Manual conflict resolution failed for ${dbKey}/${docId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check for and resolve all conflicts in a database
   */
  async resolveAllConflicts(dbKey) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      // Get all documents with conflicts
      const result = await dbConfig.db.allDocs({
        include_docs: true,
        conflicts: true
      });

      const conflictedDocs = result.rows
        .filter(row => row.doc && row.doc._conflicts && row.doc._conflicts.length > 0)
        .map(row => row.doc);

      console.log(`[DatabaseHandler] Found ${conflictedDocs.length} documents with conflicts in ${dbKey}`);

      let resolvedCount = 0;
      for (const doc of conflictedDocs) {
        try {
          // Use enhanced built-in conflict handling
          await this.handleConflicts({ doc }, 'batch_resolve');
          resolvedCount++;

          // Log progress for large batches
          if (conflictedDocs.length > 10 && resolvedCount % 10 === 0) {
            console.log(`[DatabaseHandler] Progress: ${resolvedCount}/${conflictedDocs.length} conflicts resolved`);
          }
        } catch (error) {
          console.error(`[DatabaseHandler] Failed to resolve conflicts for ${doc._id}:`, error);
        }
      }

      console.log(`[DatabaseHandler] Batch conflict resolution completed: ${resolvedCount}/${conflictedDocs.length} resolved`);

      return {
        success: true,
        totalConflicts: conflictedDocs.length,
        resolvedCount,
        failedCount: conflictedDocs.length - resolvedCount
      };
    } catch (error) {
      console.error(`[DatabaseHandler] Batch conflict resolution failed for ${dbKey}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a log entry to the logs database
   */
  async addLog(databasePrefix, logData) {
    const logsDbName = databasePrefix ? `${databasePrefix}logs` : 'logs';

    // Get or create logs database
    if (!this.databases.has(logsDbName)) {
      // CONSISTENCY FIX: Use 'logs' as file path (like other databases), not the prefixed name
      const logsDbPath = path.join(this.databasePath, 'logs');
      const logsDb = new PouchDB(logsDbPath, {
        adapter: 'leveldb',
        revs_limit: 10,        // Keep more revisions for proper conflict resolution
        auto_compaction: true  // optional: reduces DB size
      });

      this.databases.set(logsDbName, {
        db: logsDb,
        name: 'logs',
        databasePrefix: databasePrefix || '',
        branch: 'none'
      });
    }

    const logsDb = this.databases.get(logsDbName).db;
    const result = await logsDb.put(logData);
    return { success: true, id: result.id, rev: result.rev };
  }
}

module.exports = DatabaseHandler;
