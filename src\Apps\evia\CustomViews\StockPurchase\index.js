import React, { useEffect, useState } from "react";
import NewInvoice from "../OrderInvoice";
import InvoiceTemplate from "../../../Universal/CustomViews/Components/InvoiceTemplate";
import { buffEvents } from "../../modulesProperties/utils";
import PouchDb from "pouchdb-browser";

function StockPurchase({ data }) {
  const [supplier, setSupplier] = useState(null);

  useEffect(() => {
    const supplierDB = new PouchDb("suppliers");
    data.supplier
      ? supplierDB.get(data.supplier.value).then((data) => {
          setSupplier(data);
        })
      : setSupplier(null);
  }, [data]);

  

  const newdata = {
    date: data.createdAt,
    id: data._id,
    client: { title: "supplier", name: supplier }, //data.supplier,
    operator: { title: "Entrant", name: data.entrant.label },
    items: [
      ...data.items.map((item) => {
        return {
          name: item.product.label,
          price: item.price,
          quantity: item.quantity,
        };
      }),
    ],
  };
  return (
    data && <InvoiceTemplate documentTitle="Purchase Invoice" data={newdata} />
  );
}

export default StockPurchase;
