import React, { useState, useEffect } from 'react';
import { Pie } from '@ant-design/plots';

const ProjectsDonut = ({ jobs, name }) => {

    //All
    const allJobs = jobs ? jobs.length : 0
    //Complete
    const completedJobs = { type: 'Completed', value: jobs ? ((jobs.filter(job => job.status === 'success').length) * 100) / allJobs : 0 }
    //Pending
    const inProcessJobs = { type: 'In Progress', value: jobs ? ((jobs.filter(job => job.status === 'processing').length) * 100) / allJobs : 0 }
    //Cancelled
    const cancelledJobs = { type: 'Cancelled', value: jobs ? ((jobs.filter(job => job.status === 'error').length) * 100) / allJobs : 0 }
    //Booked in
    const bookedInJobs = { type: 'Drafted', value: jobs ? ((jobs.filter(job => job.status === 'default').length) * 100) / allJobs : 0 }


    const data = (jobs && jobs.length === 0) ? [{ type: 'Completed', value: 0 }, { type: 'In Progress', value: 0 }, { type: 'Cancelled', value: 0 }, { type: 'Drafted', value: 0 }] : [
        completedJobs,
        inProcessJobs,
        cancelledJobs,
        bookedInJobs,
    ];
    const config = {
        appendPadding: 10,
        data,
        angleField: 'value',
        colorField: 'type',
        radius: 1,
        innerRadius: 0.6,
        label: {
            type: 'inner',
            // offset: '-50%',
            content: '{value}',
            autoRotate: false,
            style: {
                textAlign: 'center',
                fontSize: 12,
            },
        },
        interactions: [
            {
                type: 'element-selected',
            },
            {
                type: 'element-active',
            },
        ],
        statistic: {
            title: false,
            content: {
                style: {
                    whiteSpace: 'pre-wrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                },
                content: name,
            },
        },
    };
    return <Pie {...config} />;
}

export default ProjectsDonut