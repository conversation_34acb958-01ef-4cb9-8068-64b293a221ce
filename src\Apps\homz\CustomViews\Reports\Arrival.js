import React, { useEffect, useState } from "react";
import {
  But<PERSON>,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Row,
  Col,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import { formatMoney } from "../../../../Utils/functions";
import "./print.css";
import DocumentHead from "./DocumentHead";

const Arrival = (props) => {
  const [dateRange, setDateRange] = useState(null);

  const { checkIns, guests, apartments, company } = props;

  const columns = [
    {
      title: "Arrival",
      dataIndex: "arrival_date",
      key: "arrival_date",
      render: (text) => moment(text).format("DD MMM YY - HH:mm"),
    },
    {
      title: "Departure",
      dataIndex: "departure_date",
      key: "departure_date",
      render: (text, r) =>
        moment(r.departure_date).add(r.extensions, "days").format("DD MMM YY"),
    },
    {
      title: "Apt no",
      key: "apartment_number",
      dataIndex: "apartment_number",
      render: (text) =>
        text && text.label && text.label.split(" ").reverse().pop(),
    },
    {
      title: "Guest Name",
      dataIndex: "guest",
      key: "guest",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Checkin ID",
      dataIndex: "_id",
      key: "_id",
      render: (text, record) =>
        record.ref_number ? `${text} - ${record.ref_number}` : text,
    },
    {
      title: "Company",
      dataIndex: "company",
      key: "company",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Checked In By",
      dataIndex: "entrant",
      key: "entrant",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Rate",
      dataIndex: "apartment_rate",
      key: "apartment_rate",
      render: (text) => text && formatMoney(text),
    },
    {
      title: "No of Nights",
      dataIndex: "company",
      key: "company",
      render: (text, record) =>
        record.departure_date && record.arrival_date
          ? moment(record.departure_date)
              .add(record.extensions, "days")
              .startOf("day")
              .diff(moment(record.arrival_date).startOf("day"), "days")
          : 0,
    },
    {
      title: "Amount",
      dataIndex: "company",
      key: "company",
      render: (text, record) =>
        formatMoney(
          record.departure_date && record.arrival_date
            ? moment(record.departure_date)
                .add(record.extensions, "days")
                .startOf("day")
                .diff(moment(record.arrival_date).startOf("day"), "days") *
                record.apartment_rate
            : 0
        ),
    },
  ];

  return (
    <div>
      <Table
        size="small"
        columns={columns}
        dataSource={
          dateRange
            ? checkIns.filter((r) =>
                moment(r.arrival_date).isBetween(
                  moment(dateRange[0].$d).startOf("day"),
                  moment(dateRange[1].$d).endOf("day")
                )
              )
            : checkIns.reverse().slice(0, 10).reverse()
        }
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>
                    Guest Arrival Register
                  </Typography.Title>
                  <Table
                    size="small"
                    className="custom-table"
                    columns={columns}
                    dataSource={
                      dateRange
                        ? checkIns.filter((r) =>
                            moment(r.arrival_date).isBetween(
                              moment(dateRange[0].$d).startOf("day"),
                              moment(dateRange[1].$d).endOf("day")
                            )
                          )
                        : checkIns.reverse().slice(0, 10).reverse()
                    }
                    pagination={false}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Arrival;
