import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { message, Popconfirm } from "antd";
import React, { useRef } from "react";
import { ShoppingCartOutlined, CheckCircleOutlined } from "@ant-design/icons";
import StockOrder from "../CustomViews/StockOrder";
import { refreshRecordData } from "../../../Utils/RecordRefreshUtility";

const stock_orders = {
  CustomView: (data) => <StockOrder {...data} />,
  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
      updateOptimisticRecord,
    } = props;

    const refreshRecord = async (updatedRecord) => {
      return await refreshRecordData(updatedRecord, updateOptimisticRecord, pouchDatabase, collection, databasePrefix);
    };

    // Determine if the stock order can be approved or converted to PO
    const isDraft = record.status === "draft";
    const isApproved = record.status === "approved";
    const isConverted = record.status === "converted";

    const actionMenus = [];

    // Approve Stock Order action - only for draft orders
    if (isDraft) {
      actionMenus.push({
        key: "approve",
        name: (
          <Popconfirm
            title="Approve Stock Order"
            description="Are you sure you want to approve this stock order?"
            onConfirm={async () => {
              try {
                const updatedRecord = { ...record, status: "approved" };
                await pouchDatabase(collection, databasePrefix).saveDocument(updatedRecord, CRUD_USER);
                await refreshRecord(updatedRecord);
                message.success("Stock order approved");
              } catch (error) {
                message.error("Failed to approve stock order");
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <a key="button" type="primary">
              <CheckCircleOutlined /> Approve Order
            </a>
          </Popconfirm>
        ),
      });
    }

    // Generate Stock Purchasing entry - only for approved orders
    if (isApproved) {
      actionMenus.push({
        key: "generateStockPurchasing",
        name: (
          <BetaSchemaForm
            formRef={action}
            submitter={{
              searchConfig: {
                resetText: "Cancel",
                submitText: "Convert to Stock Purchasing",
              },
            }}
            modalProps={{ centered: true }}
            grid={true}
            trigger={
              <a key="button" type="primary">
                <ShoppingCartOutlined /> Convert to Stock Purchasing
              </a>
            }
            title={"Convert Stock Order to Stock Purchasing"}
            destroyOnClose={true}
            layoutType="ModalForm"
            initialValues={{
              date: record.date,
              supplier: record.supplier,
              items: record.items,
              notes: `Generated from Stock Order #${record._id}`,
              stockOrderId: record._id,
              branch: record.branch,
              expected_delivery_date: record.expected_delivery_date
            }}
            onFinish={async (values) => {
              try {
                // Create the stock purchasing entry
                const stockPurchasingData = {
                  ...values,
                  stockOrderId: record._id,
                };

                // Save the stock purchasing entry
                const stockPurchasingResult = await pouchDatabase(
                  "stock_purchasing",
                  databasePrefix
                ).saveDocument(stockPurchasingData, CRUD_USER);

                // Update the stock order status to converted
                const updatedRecord = {
                  ...record,
                  status: "converted",
                  stockPurchasingId: stockPurchasingResult._id
                };
                await pouchDatabase(collection, databasePrefix).saveDocument(updatedRecord, CRUD_USER);
                await refreshRecord(updatedRecord);

                message.success("Stock order converted to Stock Purchasing successfully");
                return true;
              } catch (error) {
                console.error("Error converting to Stock Purchasing:", error);
                message.error("Failed to convert to Stock Purchasing");
                return false;
              }
            }}
            columns={modules.stock_purchasing.columns.filter(
              (col) => col.dataIndex !== "status"
            )}
          />
        ),
      });
    }

    return actionMenus.length > 0 ? (
      <TableDropdown key="actionGroup" menus={actionMenus}></TableDropdown>
    ) : null;
  },
  // Set branch when saving a document
  beforeSave: (data, CRUD_USER) => {
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

    // For all documents, ensure branch is attached
    return {
      ...data,
      branch: data.branch || SELECTED_BRANCH
    };
  },

  // Buffer results to add calculated fields
  buffResults: (results) => {
    return results.map(doc => {
      const subtotal = doc.items?.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0;
      return {
        ...doc,
        subtotal,
        total: subtotal
      };
    });
  },
};

export default stock_orders;
