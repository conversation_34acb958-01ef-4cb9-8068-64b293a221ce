import React, { useState, useMemo } from 'react';
import { Table, Button, Row, Col, Input, Typography, DatePicker, Card, Statistic } from 'antd';
import { PrinterOutlined, DollarOutlined, ShoppingOutlined, PercentageOutlined } from '@ant-design/icons';
import PrintComponents from 'react-print-components';
import DocumentHead from '../../../Universal/CustomViews/Components/DocumentHead';
import { useReportsData } from './ReportsContext';
import './print.css';
import { formatNumber } from '../../../../Utils/functions';
import dayjs from 'dayjs';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { Title } = Typography;

const CategorySales = () => {
  const { sales: invoices, products, categories, loading, company } = useReportsData();
  const [keyword, setKeyword] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);

  const processedData = useMemo(() => {
    if (!invoices?.length || !products?.length || !categories?.length) return [];

    // Filter invoices by date range
    const filteredInvoices = invoices.filter(invoice => {
      return dateRange && moment(invoice.date).isSameOrAfter(moment(dateRange[0].$d), "day") &&
        moment(invoice.date).isSameOrBefore(moment(dateRange[1].$d), "day")
    });

    // Create a map of category sales
    const salesMap = new Map();

    // Initialize with all categories
    categories.forEach(category => {
      salesMap.set(category._id, {
        category: category.name,
        categoryId: category._id,
        quantity: 0,
        totalSell: 0,
        totalCost: 0,
        profit: 0,
        margin: 0,
        percentageOfTotal: 0
      });
    });

    // Add "Uncategorized" for products without a category
    salesMap.set('uncategorized', {
      category: 'Uncategorized',
      categoryId: 'uncategorized',
      quantity: 0,
      totalSell: 0,
      totalCost: 0,
      profit: 0,
      margin: 0,
      percentageOfTotal: 0
    });

    // Process each invoice
    filteredInvoices.forEach(invoice => {
      invoice.items?.forEach(item => {
        const product = products.find(p => p._id === item.product?.value);
        if (!product) return;

        const categoryId = product.category?.value || 'uncategorized';
        const existing = salesMap.get(categoryId);

        if (!existing) return; // Skip if category not found

        const itemQuantity = item.quantity || 0;
        const itemSellPrice = (item.quantity * item.price) || 0;
        const itemCostPrice = (item.quantity * (product.cost || 0));
        const itemProfit = itemSellPrice - itemCostPrice;

        existing.quantity += itemQuantity;
        existing.totalSell += itemSellPrice;
        existing.totalCost += itemCostPrice;
        existing.profit += itemProfit;

        // Calculate margin as a percentage
        if (existing.totalCost > 0) {
          existing.margin = (existing.profit / existing.totalCost) * 100;
        }

        salesMap.set(categoryId, existing);
      });
    });

    // Calculate total quantity sold across all categories
    const totalQuantitySold = Array.from(salesMap.values()).reduce(
      (sum, category) => sum + category.quantity, 0
    );

    // Calculate percentage of total for each category
    if (totalQuantitySold > 0) {
      salesMap.forEach(category => {
        category.percentageOfTotal = (category.quantity / totalQuantitySold) * 100;
      });
    }

    // Convert map to array and filter out categories with no sales
    return Array.from(salesMap.values())
      .filter(item => item.quantity > 0)
      .sort((a, b) => b.totalSell - a.totalSell);
  }, [invoices, products, categories, dateRange]);

  const columns = [
    {
      title: 'No',
      dataIndex: 'no',
      width: 50,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      sorter: (a, b) => a.category.localeCompare(b.category),
    },
    {
      title: 'Quantity Sold',
      dataIndex: 'quantity',
      align: 'right',
      sorter: (a, b) => a.quantity - b.quantity,
      render: value => formatNumber(value),
    },
    {
      title: '% of Items Sold',
      dataIndex: 'percentageOfTotal',
      align: 'right',
      sorter: (a, b) => a.percentageOfTotal - b.percentageOfTotal,
      render: value => value.toFixed(2) + '%',
    },
    {
      title: 'Total Cost Price',
      dataIndex: 'totalCost',
      align: 'right',
      sorter: (a, b) => a.totalCost - b.totalCost,
      render: value => formatNumber(value),
    },
    {
      title: 'Total Selling Price',
      dataIndex: 'totalSell',
      align: 'right',
      sorter: (a, b) => a.totalSell - b.totalSell,
      render: value => formatNumber(value),
    },
    {
      title: 'Profit',
      dataIndex: 'profit',
      align: 'right',
      sorter: (a, b) => a.profit - b.profit,
      render: value => formatNumber(value),
    },
    {
      title: 'Margin (%)',
      dataIndex: 'margin',
      align: 'right',
      sorter: (a, b) => a.margin - b.margin,
      render: value => value.toFixed(2) + '%',
    }
  ];

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    let filtered = processedData;
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter(record =>
        record.category.toLowerCase().includes(searchLower)
      );
    }
    return filtered;
  }, [processedData, keyword]);

  // Calculate totals for summary
  const totals = useMemo(() => {
    if (!filteredData.length) return {
      quantity: 0,
      totalSell: 0,
      totalCost: 0,
      profit: 0,
      margin: 0
    };

    const sums = filteredData.reduce((acc, item) => {
      acc.quantity += item.quantity || 0;
      acc.totalSell += item.totalSell || 0;
      acc.totalCost += item.totalCost || 0;
      acc.profit += item.profit || 0;
      return acc;
    }, {
      quantity: 0,
      totalSell: 0,
      totalCost: 0,
      profit: 0
    });

    // Calculate overall margin
    sums.margin = sums.totalCost > 0 ? (sums.profit / sums.totalCost) * 100 : 0;

    return sums;
  }, [filteredData]);

  const tableProps = {
    size: 'small',
    columns,
    dataSource: filteredData,
    loading,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    scroll: { y: 'calc(100vh - 350px)' },
    summary: () => {
      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={2}>Total</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totals.quantity)}</Table.Summary.Cell>
            <Table.Summary.Cell align="right">100.00%</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totals.totalCost)}</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totals.totalSell)}</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totals.profit)}</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{totals.margin.toFixed(2)}%</Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    }
  };

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Sales"
              value={formatNumber(totals.totalSell)}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Profit"
              value={formatNumber(totals.profit)}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Overall Margin"
              value={totals.margin.toFixed(2)}
              suffix="%"
              prefix={<PercentageOutlined />}
              valueStyle={{ color: totals.margin > 0 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Table
        {...tableProps}
        title={() => (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Input.Search
                placeholder="Search categories..."
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: '100%' }}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} style={{ textAlign: 'right' }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                  >
                    Print
                  </Button>
                }
              >
                <div className="print-container" style={{ margin: 20, background: 'white' }}>
                  {company && <DocumentHead company={company} />}

                  {/* Report Header */}
                  <div style={{ marginBottom: 20 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <Typography.Title level={3} style={{ margin: 0 }}>Category Sales Report</Typography.Title>
                        <Typography.Text type="secondary">Sales Performance by Category</Typography.Text>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <Typography.Text type="secondary" style={{ display: 'block' }}>
                          Generated: {dayjs().format('DD-MM-YYYY HH:mm')}
                        </Typography.Text>
                        <Typography.Text type="secondary" style={{ display: 'block' }}>
                          Prepared by: {(() => {
                            try {
                              const user = JSON.parse(localStorage.getItem('LOCAL_STORAGE_USER'));
                              return user ? `${user.first_name} ${user.last_name}` : 'System User';
                            } catch (e) {
                              return 'System User';
                            }
                          })()}
                        </Typography.Text>
                      </div>
                    </div>
                  </div>

                  {/* Divider */}
                  <div style={{ height: 2, background: '#f0f0f0', marginBottom: 20 }}></div>

                  {/* Stats Summary */}
                  <div style={{ marginBottom: 20 }}>
                    <Row gutter={[16, 16]}>
                      <Col span={6}>
                        <Card>
                          <div style={{ textAlign: 'center' }}>
                            <Typography.Title level={4}>Total Sales</Typography.Title>
                            <Typography.Title level={3} style={{ margin: 0 }}>
                              {formatNumber(totals.totalSell)}
                            </Typography.Title>
                          </div>
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <div style={{ textAlign: 'center' }}>
                            <Typography.Title level={4}>Total Cost</Typography.Title>
                            <Typography.Title level={3} style={{ margin: 0 }}>
                              {formatNumber(totals.totalCost)}
                            </Typography.Title>
                          </div>
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <div style={{ textAlign: 'center' }}>
                            <Typography.Title level={4}>Total Profit</Typography.Title>
                            <Typography.Title level={3} style={{
                              color: totals.profit >= 0 ? 'green' : 'red',
                              margin: 0
                            }}>
                              {formatNumber(totals.profit)}
                            </Typography.Title>
                          </div>
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <div style={{ textAlign: 'center' }}>
                            <Typography.Title level={4}>Profit Margin</Typography.Title>
                            <Typography.Title level={3} style={{
                              margin: 0,
                              color: totals.margin >= 0 ? 'green' : 'red'
                            }}>
                              {totals.margin.toFixed(1)}%
                            </Typography.Title>
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  </div>

                  {/* Date Range */}
                  <div style={{ marginBottom: 10 }}>
                    <Typography.Text strong>
                      Period: {dateRange && dateRange[0].format('DD-MM-YYYY')} to {dateRange[1].format('DD-MM-YYYY')}
                    </Typography.Text>
                  </div>

                  {/* Table */}
                  <Table
                    {...tableProps}
                    pagination={false}
                    scroll={false}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default CategorySales;
