export default {
  name: "Event Resources",
  icon: "ApartmentOutlined",
  path: "/events_management/resources",
  parent: "events_management",
  collection: "resources",
  singular: "Event Resource",
  columns: [
    {
      title: "Name",
      dataIndex: "name",
      valueType: "text",
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Quantity",
      dataIndex: "quantity",
      valueType: "digit",
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Price",
      dataIndex: "price",
      valueType: "digit",
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Description",
      dataIndex: "description",
      isPrintable: true,
      valueType: "textarea",
    },
  ],
};
