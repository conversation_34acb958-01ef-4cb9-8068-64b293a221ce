export default {
  name: 'Guests Registration',
  icon: 'UsergroupAddOutlined',
  path: '/guests',
  collection: 'guests',
  singular: 'Guest',
  columns: [{
    title: 'ID',
    dataIndex: '_id',
    hideInForm: true
  }, {
    title: 'Name',
    hideInForm: true,
    render: function render(_, record) {
      return record.title + ". " + record.sur_name + " " + record.first_name;
    }
  }, {
    title: 'Title',
    dataIndex: 'title',
    sorter: true,
    hideInTable: true,
    valueType: 'select',
    isRequired: true,
    width: 'lg',
    colProps: {
      xs: 12,
      md: 4
    },
    valueEnum: {
      Mr: {
        text: 'Mr'
      },
      Mrs: {
        text: 'Mrs'
      },
      Ms: {
        text: 'Ms'
      },
      Miss: {
        text: 'Miss'
      }
    }
  }, {
    title: 'Sur Name',
    dataIndex: 'sur_name',
    sorter: true,
    hideInTable: true,
    isRequired: true,
    width: 'lg',
    colProps: {
      xs: 12,
      md: 10
    }
  }, {
    title: 'First Name',
    dataIndex: 'first_name',
    sorter: true,
    hideInTable: true,
    width: 'lg',
    colProps: {
      xs: 12,
      md: 10
    }
  }, {
    title: 'Email',
    dataIndex: 'email',
    copyable: true,
    valueType: 'email',
    colProps: {
      md: 8
    }
  }, {
    title: 'Phone',
    dataIndex: 'phone',
    sorter: true,
    copyable: true,
    isRequired: true,
    type: 'phone',
    colProps: {
      md: 8
    }
  }, {
    title: 'Mobile',
    dataIndex: 'mobile',
    sorter: true,
    type: 'phone',
    hideInTable: true,
    colProps: {
      md: 8
    }
  }, {
    title: 'ID Type',
    dataIndex: 'id_type',
    initialValue: 'National ID',
    isRequired: true,
    filters: true,
    onFilter: true,
    valueType: 'select',
    hideInTable: true,
    valueEnum: {
      "National ID": {
        text: 'National ID'
      },
      "Driving Permit": {
        text: 'Driving Permit'
      },
      "Passport": {
        text: 'Passport'
      }
    },
    colProps: {
      md: 6
    }
  }, {
    title: 'ID Number',
    dataIndex: 'id_number',
    sorter: true,
    valueType: 'text',
    hideInTable: true,
    colProps: {
      md: 6
    }
  }, {
    title: 'Place Of Issue',
    dataIndex: 'country',
    sorter: true,
    valueType: 'select',
    hideInTable: true,
    colProps: {
      md: 6
    }
  }, {
    title: 'Date of issue',
    dataIndex: 'date_of_issue',
    sorter: true,
    valueType: 'date',
    hideInTable: true,
    width: 'lg',
    colProps: {
      md: 6
    }
  },  {
    title: 'Designation',
    dataIndex: 'designation',
    sorter: true,
    valueType: 'text',
    hideInTable: true,
    colProps: {
      md: 12
    }
  }, {
    title: 'Date Of Birth',
    dataIndex: 'date_of_birth',
    sorter: true,
    valueType: 'date',
    hideInTable: true,
    width: 'lg',
    colProps: {
      md: 12
    }
  }, {
    title: 'Nationality',
    dataIndex: 'nationality',
    sorter: true,
    hideInTable: true,
    valueType: 'select',
    colProps: {
      md: 12
    }
  }, {
    title: 'Address',
    dataIndex: 'address',
    sorter: true,
    valueType: 'text',
    colProps: {
      md: 12
    }
  }]
};