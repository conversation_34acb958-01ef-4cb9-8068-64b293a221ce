export default {
  name: "Transfers",
  icon: "UsergroupAddOutlined",
  path: "hotel/transfers",
  collection: "transfers",
  singular: "Transfer",
  parent: "hotel",
  removeCreate: true,
  hideInMenu:true,
  columns: [
    {
      type: "dbSelect",
      collection: "rooms",
      label: ["number", "short_desc"],
      dataIndex: "room_number",
      isPrintable: true,
      isRequired: true,
      filterOptions: {
        dataIndex: "room_status",
        value: ["Occupied", "Booked", "House Use", "Out Of Order"],
        direction: "out",
      },
      title: "Room",
    },
    {
      title: "Transfer Date",
      dataIndex: "transfer_date",
      noBackDate: true,
      sorter: true,
      isPrintable: true,
      formItemProps: {
        rules: [{ required: true }],
      },
      valueType: "date",
      width: "lg",
      colProps: {
        md: 12,
      },
    },
  ],
};
