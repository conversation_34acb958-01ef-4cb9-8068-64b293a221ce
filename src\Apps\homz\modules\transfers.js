
export default {
  name: 'Transfers',
  icon: 'UsergroupAddOutlined',
  path: '/transfers',
  collection: 'transfers',
  singular: 'Transfer',
  removeCreate:true,
  columns: [ {
    type: 'dbSelect',
    collection: 'apartments',
    label: ["number", 'short_desc'],
    dataIndex: "apartment_number",
    isRequired: true,
    filterOptions:{dataIndex:"apartment_status",value:["Occupied","Booked","House Use","Out Of Order"],direction:"out"},
    title: 'Apartment'  
  },{
    title: 'Transfer Date',
    dataIndex: 'transfer_date',
    sorter: true,
    valueType: 'date',
    width: 'lg',
    colProps: {
      md: 12
    }
  }]
};