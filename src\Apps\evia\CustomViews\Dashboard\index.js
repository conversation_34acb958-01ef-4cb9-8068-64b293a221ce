import {
  Row,
  Col,
  Card,
  Space,
  Typography,
  Progress,
  Divider,
  Tag,
} from "antd";
import React, { useEffect, useState } from "react";
import {
  IdcardOutlined,
  MedicineBoxTwoTone,
  UsergroupAddOutlined,
  ReconciliationOutlined,
  ShopOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import numeral from "numeral";
import moment from "moment";
import { formatMoney, numberFormat } from "../../../../Utils/functions";
import MonthlyCollection from "./MonthlyCollection";
import PieChart from "./PieChart";
var Text = Typography.Text,
  Title = Typography.Title;

const Dashboard = (props) => {
  const { pouchDatabase, databasePrefix, currentUser } = props;

  const [checkIns, setCheckIns] = useState([]);
  const [guests, setGuests] = useState([]);
  const [receipts, setReceipts] = useState([]);
  const [eventsReceipts, setEventsReceipts] = useState([]);
  const [ordersReceipts, setOrdersReceipts] = useState([]);
  const [rooms, setApartments] = useState([]);
  const [expenses, setExpenses] = useState([]);

  const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH")
    ? localStorage.getItem("SELECTED_BRANCH")
    : "none";

  useEffect(function () {
    pouchDatabase("checkins", databasePrefix)
      .getAllData()
      .then(function (res) {
        setCheckIns(res);
      });
  }, []);

  useEffect(function () {
    pouchDatabase("rooms", databasePrefix)
      .getAllData()
      .then(function (res) {
        setApartments(res);
      });
  }, []);

  useEffect(function () {
    pouchDatabase("guests", databasePrefix)
      .getAllData()
      .then(function (res) {
        setGuests(res);
      });
  }, []);

  useEffect(function () {
    pouchDatabase("receipts", databasePrefix)
      .getAllData()
      .then(function (res) {
        setReceipts(res);
      });
  }, []);

  useEffect(function () {
    pouchDatabase("events_receipts", databasePrefix)
      .getAllData()
      .then(function (res) {
        setEventsReceipts(res);
      });
  }, []);
  useEffect(function () {
    pouchDatabase("order_receipts", databasePrefix)
      .getAllData()
      .then(function (res) {
        const resToday = res.filter((r) =>
          moment(r.date).isSame(new Date(), "day")
        );
        setOrdersReceipts(
          SELECTED_BRANCH === "none"
            ? resToday
            : resToday.filter((r) => r.branch === SELECTED_BRANCH)
        );
      });
  }, []);

  useEffect(function () {
    pouchDatabase("expenses", databasePrefix)
      .getAllData()
      .then(function (res) {
        setExpenses(res);
      });
  }, []);

  

  return (
    <Row gutter={[10, 10]}>
      <Col xs={24} sm={24} md={12}>
        <Card
          style={{
            bordered: true,
            size: "small",
            style: {
              height: 80,
            },
          }}
        >
          <Space
            style={{
              fontSize: 30,
            }}
          >
            <DollarOutlined
              style={{
                color: "#391085",
                fontSize: 30,
              }}
            />
            {/* <Col span={12}> */}
            <div>
              <Text style={{ fontSize: 15 }} level={3}>
                Collection Today
              </Text>
              <br />
              <Text style={{ fontSize: 20 }} level={2}>
                <strong>
                  {numberFormat(
                    ordersReceipts
                      .filter((r) =>
                        moment(r.createdAt).isSame(new Date(), "day")
                      )
                      .reduce((p, c) => p + Number(c.amount), 0)
                  )}
                </strong>
              </Text>
            </div>
            {/* </Col> */}
          </Space>
        </Card>
      </Col>
      <Col xs={24} sm={24} md={12}>
        <Card
          style={{
            bordered: true,
            size: "small",
            style: {
              height: 80,
            },
          }}
        >
          <Space
            style={{
              fontSize: 30,
            }}
          >
            <DollarOutlined
              style={{
                color: "#391085",
                fontSize: 30,
              }}
            />
            {/* <Col span={12}> */}
            <div>
              <Text style={{ fontSize: 15 }} level={3}>
                Today's Expenditure
              </Text>
              <br />
              <Text style={{ fontSize: 20 }} level={2}>
                <strong>
                  {numberFormat(
                    expenses
                      .filter((r) =>
                        moment(r.createdAt).isSame(new Date(), "day")
                      )
                      .reduce((p, c) => p + Number(c.amount), 0)
                  )}
                </strong>
              </Text>
            </div>
            {/* </Col> */}
          </Space>
        </Card>
      </Col>
      {/* <Col xs={{ span: 24 }} md={{ span: 24 }} lg={{ span: 16 }}>
        <Card
          //   extra={tooltip}
          title="Monthly Collection"
        >
          <div style={{ height: 300 }}>
            <MonthlyCollection
              pouchDatabase={pouchDatabase}
              databasePrefix={databasePrefix}
              // currentUser={currentUser}
            />
          </div>
        </Card>
      </Col> */}
      {/* <Col xs={{ span: 24 }} md={{ span: 24 }} lg={{ span: 8 }}>
        <Card
          //   extra={tooltip}
          title="Weekly Collection"
        >
          <div style={{ height: 300 }}>
            <PieChart
              pouchDatabase={pouchDatabase}
              databasePrefix={databasePrefix}
              by="profit"
              receipts={receipts}
              eventsReceipts={eventsReceipts}
              ordersReceipts={ordersReceipts}
            />
          </div>
        </Card>
      </Col> */}
    </Row>
  );
};

export default Dashboard;
