import { numberFormat } from "../../../Utils/functions";

export default {
  name: "Event Receipts",
  icon: "UsergroupAddOutlined",
  path: "events_management/receipts",
  parent: "events_management",
  collection: "events_receipts",
  singular: "Event Receipt",
  removeCreate: true,
  columns: [
    {
      title: "Order",
      dataIndex: "order",
      type: "dbSelect",
      collection: "orders",
      label: ["_id"],
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Payment Date",
      dataIndex: "date",
      valueType: "date",
      isRequired: true,
      isPrintable: true,
      noBackDate: true,
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      isPrintable: true,
      width: "lg",
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Cheque: {
          text: "Cheque",
        },
        Bank: {
          text: "Bank",
        },
        "Mobile digit": {
          text: "Mobile digit",
        },
      },
    },
    {
      title: "Amount Paid",
      dataIndex: "amount",
      isRequired: true,
      isPrintable: true,
      type: "digit",
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      width: "lg",
      isPrintable: true,
      colProps: {
        md: 24,
      },
    },

    {
      valueType: "dependency",
      hideInTable: true,
      title: "Total",
      width: "lg",
      fieldProps: {
        name: ["order", "amount"],
      },
      columns: ({ order = null, amount = 0 }) => {
        

        let venue_charges = 0,
          resources_charges = 0,
          previous_receipts = 0,
          total = 0;

        venue_charges = order.price;
        resources_charges = order.items
          ? order.items.reduce(
              (pv, cv) => (pv + cv ? cv.cost * cv.quantity : 0),
              0
            )
          : 0;

        

        previous_receipts = order.receipts;

        total = venue_charges + resources_charges - previous_receipts - amount;

        return [
          {
            dataIndex: "balance",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "digit",
            render: function render(v) {
              return numberFormat(v);
            },

            renderFormItem: function renderFormItem() {
              return (
                "Venue Charges : " +
                numberFormat(venue_charges) +
                " | Resourses :  " +
                numberFormat(resources_charges) +
                " | Previous Receipts :  (" +
                numberFormat(previous_receipts) +
                ") | Balance :  " +
                numberFormat(total) +
                " "
              );
            },
          },
        ];
      },
    },
  ],
};
