import React, { useEffect, useState } from "react";
import {
  Button,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Row,
  Col,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import { formatMoney } from "../../../../Utils/functions";
import "./print.css";
import DocumentHead from "./DocumentHead";

const Occupancy = (props) => {
  const [dateRange, setDateRange] = useState(null);

  const { checkIns, guests, rooms, company } = props;

  const columns = [
    {
      title: "Room no",
      key: "room_number",
      dataIndex: "room_number",
      render: (text) =>
        text && text.label && text.label.split(" ").reverse().pop(),
    },
    {
      title: "Guest Name",
      dataIndex: "guest",
      key: "guest",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Company",
      dataIndex: "company",
      key: "company",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Checkin ID",
      dataIndex: "_id",
      key: "_id",
      render: (text, record) =>
        record.ref_number ? `${text} - ${record.ref_number}` : text,
    },
    {
      title: "Arrival",
      dataIndex: "arrival_date",
      key: "arrival_date",
      render: (text) => moment(text).format("DD MMM YY"),
    },
    {
      title: "Departure",
      dataIndex: "departure_date",
      key: "departure_date",
      render: (text, r) =>
        moment(r.departure_date)
          .add(r.extensions, "days")
          .format("DD MMM YY - HH:mm"),
    },
    {
      title: "No of Nights",
      dataIndex: "company",
      key: "company",
      render: (text, record) =>
        record.departure_date && record.arrival_date
          ? moment(record.departure_date)
              .add(record.extensions, "days")
              .startOf("day")
              .diff(moment(record.arrival_date).startOf("day"), "days")
          : 0,
    },
    {
      title: "Rate",
      dataIndex: "room_rate",
      key: "room_rate",
      render: (text) => text && formatMoney(text),
    },
    {
      title: "Amount",
      dataIndex: "room_rate",
      key: "room_rate",
      render: (text, record) =>
        formatMoney(
          (record.departure_date && record.arrival_date
            ? moment(record.departure_date)
                .add(record.extensions, "days")
                .startOf("day")
                .diff(moment(record.arrival_date).startOf("day"), "days")
            : 0) * record.room_rate
        ),
    },
  ];

  return (
    <div>
      <Table
        size="small"
        columns={columns}
        dataSource={
          dateRange
            ? checkIns.filter(
                (r) =>
                  r.checked_out &&
                  moment(r.departure_date)
                    .add(r.extensions, "days")
                    .isBetween(
                      moment(dateRange[0].$d).startOf("day"),
                      moment(dateRange[1].$d).endOf("day")
                    )
              )
            : checkIns.reverse().slice(0, 10).reverse()
        }
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                initialValue={[moment().startOf("month"), moment()]}
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Daily Report</Typography.Title>
                  <Table
                    className="custom-table"
                    size="small"
                    columns={columns}
                    dataSource={
                      dateRange
                        ? checkIns.filter(
                            (r) =>
                              r.checked_out &&
                              moment(r.departure_date)
                                .add(r.extensions, "days")
                                .isBetween(
                                  moment(dateRange[0].$d).startOf("day"),
                                  moment(dateRange[1].$d).endOf("day")
                                )
                          )
                        : checkIns.reverse().slice(0, 10).reverse()
                    }
                    pagination={false}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Occupancy;
