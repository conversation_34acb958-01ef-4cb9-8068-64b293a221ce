import React, { useState } from "react";
import { <PERSON><PERSON>, Table, Typography, DatePicker, Row, Col, Radio } from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";
import "./print.css";
import DocumentHead from "./DocumentHead";

const Collection = (props) => {
  const [dateRange, setDateRange] = useState([Date(), Date()]);
  const [status, setStatus] = useState("All");

  const { checkIns, guests, rooms, receipts, company } = props;

  const currentBranch = localStorage.getItem("SELECTED_BRANCH");

  const columns = [
    {
      title: "Date",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text) => moment(text).format("DD MMM YY - HH:mm"),
    },
    {
      title: "Receipt ID",
      dataIndex: "_id",
      key: "_id",
    },
    {
      title: "Guest Name",
      dataIndex: "invoice",
      key: "guest",
      render: (text) => text && text.guest.label,
    },
    {
      title: "Mode of Payment",
      dataIndex: "method_of_payment",
      key: "method_of_payment",
    },
    {
      title: "Amount",
      dataIndex: "amount",
      key: "amount",
      render: (t, record) => numberFormat(record.amount),
    },
  ];

  const [targetDate, setTargetDate] = useState(null);

  const tProps = {
    size: "small",
    columns: columns,
    pagination: false,
    dataSource: receipts
      .filter((r) => r.branch === currentBranch)
      // .filter((r)=> status === 'All' ? true : r.department === status)
      .filter(
        (r) =>
          moment(r.createdAt).isSameOrAfter(moment(dateRange[0].$d), "day") &&
          moment(r.createdAt).isSameOrBefore(moment(dateRange[1].$d), "day")
      ),
    summary: (pageData) => {
      

      return (
        <Table.Summary fixed style={{ fontSize: 20 }}>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={columns.length - 1}>
              Total
            </Table.Summary.Cell>
            <Table.Summary.Cell>
              {numberFormat(pageData.reduce((a, b) => a + Number(b.amount), 0))}
            </Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
  };

  return (
    <div>
      <Table
        {...tProps}
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>
                    Collection Report
                  </Typography.Title>
                  <Table {...tProps} />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Collection;
