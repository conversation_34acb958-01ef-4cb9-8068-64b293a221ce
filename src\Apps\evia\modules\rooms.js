export default {
  name: "Rooms",
  icon: "UsergroupAddOutlined",
  path: "hotel/rooms",
  collection: "rooms",
  singular: "Room",
  parent: "hotel",
  columns: [
    {
      title: "Room Number",
      dataIndex: "number",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Short Description",
      dataIndex: "short_desc",
      valueType: "text",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Detailed Description",
      dataIndex: "detailed_desc",
      valueType: "textarea",
      hideInTable: true,
      isPrintable: true,
    },
    {
      title: "Rate",
      dataIndex: "rate",
      valueType: "digit",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Status",
      dataIndex: "room_status",
      filters: true,
      valueType: "select",
      isPrintable: true,
      valueEnum: {
        Occupied: {
          text: "Occupied",
        },
        Booked: {
          text: "Booked",
        },
        // 'Available': {
        //   text: 'Available'
        // },
        Vacant: {
          text: "Vacant",
        },
        Mess: {
          text: "Mess",
        },
        "House Use": {
          text: "House Use",
        },
        "Out Of Order": {
          text: "Out Of Order",
        },
      },
    },
  ],
};
