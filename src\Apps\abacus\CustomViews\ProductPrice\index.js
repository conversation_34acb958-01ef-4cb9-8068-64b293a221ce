import React, { useEffect, useState, useMemo } from "react";
import "./styles.css";
import {
  Descriptions,
  Divider,
  Layout,
  Typography,
  Button,
  Flex,
  Card,
  Row,
  Col,
  Table,
  Tag,
  Space,
  Empty,
  Statistic,
  Avatar,
  Tooltip,
  Timeline
} from "antd";
import {
  TagsOutlined,
  PrinterOutlined,
  ShoppingOutlined,
  InfoCircleOutlined,
  BarChartOutlined,
  HistoryOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined
} from "@ant-design/icons";
import moment from "moment";
import PouchDB from "pouchdb";
import PrintComponents from "react-print-components";
import { numberFormat } from "../../../../Utils/functions";
import { PageHeader } from "@ant-design/pro-components";
import { Line } from "@ant-design/plots";

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const ProductPrice = (props) => {
  const { data, singular, columns } = props;
  const [product, setProduct] = useState(null);
  const [priceHistory, setPriceHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch product details and price history
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch product details
        if (data.product && data.product.value) {
          const productsDB = new PouchDB("products");
          const productDoc = await productsDB.get(data.product.value);
          setProduct(productDoc);
        }

        // Fetch price history for this product
        const pricesDB = new PouchDB("product_prices");
        const pricesResult = await pricesDB.allDocs({ include_docs: true });
        const productPrices = pricesResult.rows
          .filter(row => row.doc && !row.doc._id.startsWith('_') && 
                  row.doc.product && row.doc.product.value === data.product.value)
          .map(row => row.doc)
          .sort((a, b) => new Date(a.date) - new Date(b.date));
        
        setPriceHistory(productPrices);

        // Fetch company info for printing
        const companyDB = new PouchDB("company");
        const companyResult = await companyDB.allDocs({ include_docs: true });
        if (companyResult.rows.length > 0) {
          setCompany(companyResult.rows[0].doc);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data.product]);

  // Calculate price change statistics
  const priceStats = useMemo(() => {
    if (!priceHistory.length || priceHistory.length < 2) {
      return { 
        change: 0, 
        percentChange: 0, 
        trend: 'stable',
        oldest: priceHistory[0]?.price || 0,
        newest: priceHistory[0]?.price || 0
      };
    }

    const oldest = priceHistory[0].price;
    const newest = priceHistory[priceHistory.length - 1].price;
    const change = newest - oldest;
    const percentChange = oldest !== 0 ? (change / oldest) * 100 : 0;
    const trend = change > 0 ? 'up' : change < 0 ? 'down' : 'stable';

    return { change, percentChange, trend, oldest, newest };
  }, [priceHistory]);

  // Prepare data for price trend chart
  const priceTrendData = useMemo(() => {
    return priceHistory.map(priceRecord => ({
      date: moment(priceRecord.date).format('YYYY-MM-DD'),
      price: priceRecord.price
    }));
  }, [priceHistory]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable price history component
  const PrintablePriceHistory = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>PRODUCT PRICE HISTORY</Title>
            <Text>Product: {product?.name || data.product?.label || 'Unknown Product'}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Current Price Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Product">{product?.name || data.product?.label || 'Unknown Product'}</Descriptions.Item>
        <Descriptions.Item label="SKU">{product?.sku || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Current Price">{numberFormat(data.price)}</Descriptions.Item>
        <Descriptions.Item label="Price Set Date">{formatDate(data.date)}</Descriptions.Item>
        {product?.cost && (
          <>
            <Descriptions.Item label="Cost Price">{numberFormat(product.cost)}</Descriptions.Item>
            <Descriptions.Item label="Profit Margin">
              {product.cost ? ((data.price - product.cost) / product.cost * 100).toFixed(1) + '%' : 'N/A'}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>

      <Divider />

      <Title level={4}>Price History</Title>
      <Table 
        dataSource={priceHistory}
        rowKey="_id"
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Date',
            dataIndex: 'date',
            key: 'date',
            render: date => formatDate(date)
          },
          {
            title: 'Price',
            dataIndex: 'price',
            key: 'price',
            render: price => numberFormat(price)
          },
          {
            title: 'Change',
            key: 'change',
            render: (_, record, index) => {
              if (index === 0) return '-';
              const prevPrice = priceHistory[index - 1].price;
              const change = record.price - prevPrice;
              const percentChange = prevPrice !== 0 ? (change / prevPrice) * 100 : 0;
              
              return (
                <span style={{ color: change > 0 ? '#52c41a' : change < 0 ? '#f5222d' : 'inherit' }}>
                  {change > 0 ? '+' : ''}{numberFormat(change)} ({percentChange.toFixed(1)}%)
                </span>
              );
            }
          }
        ]}
      />

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        backIcon={<TagsOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={`Price: ${numberFormat(data.price)}`}
        subTitle={product?.name || data.product?.label || 'Product Price'}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Price History
              </Button>
            }
          >
            <PrintablePriceHistory />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={8}>
            <Card title={<><InfoCircleOutlined /> Product Information</>} className="price-card">
              {product ? (
                <Flex align="center" vertical>
                  <Avatar 
                    shape="square" 
                    size={80} 
                    icon={<ShoppingOutlined />} 
                    style={{ backgroundColor: '#1890ff', marginBottom: 16 }} 
                  />
                  <Typography.Title level={4} style={{ margin: 0, textAlign: 'center' }}>
                    {product.name}
                  </Typography.Title>
                  {product.sku && (
                    <Typography.Text type="secondary" style={{ textAlign: 'center', marginBottom: 16 }}>
                      SKU: {product.sku}
                    </Typography.Text>
                  )}
                  <Descriptions bordered column={1} size="small" style={{ width: '100%' }}>
                    {product.category && (
                      <Descriptions.Item label="Category">
                        <Tag color="blue">{product.category.label}</Tag>
                      </Descriptions.Item>
                    )}
                    {product.brand && (
                      <Descriptions.Item label="Brand">
                        <Tag color="purple">{product.brand.label}</Tag>
                      </Descriptions.Item>
                    )}
                    <Descriptions.Item label="Unit">
                      {product.measurements ? `${product.measurements} ${product.units || 'pc'}` : (product.units || 'pc')}
                    </Descriptions.Item>
                    <Descriptions.Item label="Cost Price">
                      {numberFormat(product.cost || 0)}
                    </Descriptions.Item>
                  </Descriptions>
                  <Button 
                    type="link" 
                    style={{ marginTop: 16 }}
                    onClick={() => window.location.href = `#/product_management/products/${product._id}`}
                  >
                    View Product Details
                  </Button>
                </Flex>
              ) : (
                <Empty description="Product information not available" />
              )}
            </Card>
          </Col>

          <Col xs={24} lg={16}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Card className="price-card">
                  <Statistic 
                    title="Current Price" 
                    value={numberFormat(data.price)} 
                    prefix={<DollarOutlined />} 
                  />
                  <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)', marginTop: 4 }}>
                    Set on {formatDate(data.date)}
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card className="price-card">
                  <Statistic 
                    title="Price Change" 
                    value={numberFormat(priceStats.change)} 
                    prefix={
                      priceStats.trend === 'up' ? <RiseOutlined style={{ color: '#52c41a' }} /> : 
                      priceStats.trend === 'down' ? <FallOutlined style={{ color: '#f5222d' }} /> :
                      <span/>
                    }
                    valueStyle={{ 
                      color: priceStats.trend === 'up' ? '#52c41a' : 
                             priceStats.trend === 'down' ? '#f5222d' : 
                             'inherit' 
                    }}
                  />
                  <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)', marginTop: 4 }}>
                    {priceHistory.length > 1 ? 
                      `Since ${formatDate(priceHistory[0].date)}` : 
                      'No previous prices'}
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card className="price-card">
                  <Statistic 
                    title="Profit Margin" 
                    value={product && product.cost ? ((data.price - product.cost) / product.cost * 100).toFixed(1) : 0} 
                    suffix="%" 
                    prefix={<DollarOutlined />}
                    valueStyle={{ 
                      color: product && product.cost && data.price > product.cost ? '#52c41a' : '#f5222d'
                    }}
                  />
                  <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)', marginTop: 4 }}>
                    {product && product.cost ? 
                      `Profit: ${numberFormat(data.price - product.cost)}` : 
                      'No cost information'}
                  </div>
                </Card>
              </Col>
            </Row>

            <Card 
              title={<><BarChartOutlined /> Price Trend</>} 
              className="price-card"
              style={{ marginTop: 16 }}
            >
              {priceTrendData.length > 1 ? (
                <Line
                  data={priceTrendData}
                  xField="date"
                  yField="price"
                  point={{
                    size: 5,
                    shape: 'diamond',
                  }}
                  label={{
                    style: {
                      fill: '#aaa',
                    },
                  }}
                  yAxis={{
                    label: {
                      formatter: (v) => `${numberFormat(v)}`,
                    },
                  }}
                />
              ) : (
                <Empty description="Not enough price history data to show trend" />
              )}
            </Card>
          </Col>
        </Row>

        <Card 
          title={<><HistoryOutlined /> Price History</>} 
          className="price-card"
          style={{ marginTop: 24 }}
        >
          {priceHistory.length > 0 ? (
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <Table
                  dataSource={priceHistory}
                  rowKey="_id"
                  pagination={false}
                  columns={[
                    {
                      title: 'Date',
                      dataIndex: 'date',
                      key: 'date',
                      render: date => formatDate(date),
                      sorter: (a, b) => new Date(a.date) - new Date(b.date),
                      defaultSortOrder: 'descend'
                    },
                    {
                      title: 'Price',
                      dataIndex: 'price',
                      key: 'price',
                      render: price => numberFormat(price)
                    },
                    {
                      title: 'Change',
                      key: 'change',
                      render: (_, record, index) => {
                        if (index === 0) return '-';
                        const prevPrice = priceHistory[index - 1].price;
                        const change = record.price - prevPrice;
                        const percentChange = prevPrice !== 0 ? (change / prevPrice) * 100 : 0;
                        
                        return (
                          <span style={{ color: change > 0 ? '#52c41a' : change < 0 ? '#f5222d' : 'inherit' }}>
                            {change > 0 ? '+' : ''}{numberFormat(change)} ({percentChange.toFixed(1)}%)
                          </span>
                        );
                      }
                    }
                  ]}
                />
              </Col>
              <Col xs={24} lg={12}>
                <Timeline
                  mode="left"
                  items={priceHistory.map((price, index) => {
                    let color = 'blue';
                    let dot = null;
                    let label = formatDate(price.date);
                    
                    if (index > 0) {
                      const prevPrice = priceHistory[index - 1].price;
                      const change = price.price - prevPrice;
                      
                      if (change > 0) {
                        color = 'green';
                        dot = <RiseOutlined style={{ fontSize: '16px' }} />;
                      } else if (change < 0) {
                        color = 'red';
                        dot = <FallOutlined style={{ fontSize: '16px' }} />;
                      }
                    }
                    
                    return {
                      color,
                      dot,
                      label,
                      children: (
                        <Card size="small" style={{ marginBottom: 8 }}>
                          <Statistic
                            title="Price"
                            value={numberFormat(price.price)}
                            valueStyle={{ fontSize: '16px' }}
                          />
                          {index > 0 && (
                            <div style={{ marginTop: 8 }}>
                              <Text type="secondary">
                                Change: {' '}
                                <Text
                                  style={{
                                    color: price.price > priceHistory[index - 1].price
                                      ? '#52c41a'
                                      : price.price < priceHistory[index - 1].price
                                      ? '#f5222d'
                                      : 'inherit',
                                  }}
                                >
                                  {price.price > priceHistory[index - 1].price ? '+' : ''}
                                  {numberFormat(price.price - priceHistory[index - 1].price)}
                                  {' '}
                                  ({((price.price - priceHistory[index - 1].price) / priceHistory[index - 1].price * 100).toFixed(1)}%)
                                </Text>
                              </Text>
                            </div>
                          )}
                        </Card>
                      )
                    };
                  })}
                />
              </Col>
            </Row>
          ) : (
            <Empty description="No price history available" />
          )}
        </Card>
      </Content>
    </>
  );
};

export default ProductPrice;
