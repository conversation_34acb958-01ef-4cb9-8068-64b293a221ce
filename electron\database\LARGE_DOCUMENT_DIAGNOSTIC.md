# Large Document Diagnostic Guide

## Problem Summary

You're experiencing a `document_too_large` error (HTTP 413) when syncing receipts database:
- **Error**: Document `MBSZFGK2` is too large to sync
- **Result**: 30 documents read, 0 written, 30 failures
- **Database**: `prosy_m9xlo8hv_receipts`

## Immediate Solution Implemented

### 1. Dual Document Size Filtering
The SyncService now uses different size limits for LAN vs Remote sync:

```javascript
maxDocumentSize: {
  lan: 5242880,     // 5MB max for LAN
  remote: 5242880   // 5MB max for remote
}
```

**Why 5MB Limit?**
- **Generous Limit**: Allows most documents including those with attachments
- **CouchDB Compatible**: Well within CouchDB's default limits
- **Consistent Behavior**: Same limit for both LAN and Remote ensures consistency

### 2. Intelligent Filtering
Documents are filtered based on size limit:

```javascript
// Both LAN and Remote Sync: Skip documents > 5MB
const sizeLimit = this.getDocumentSizeLimit(syncType); // Returns 5MB for both
if (docSize > sizeLimit) {
  console.warn(`Skipping large document for ${syncType} sync ${doc._id}: ${docSize}KB`);
  return false;
}
```

### 3. Optimized Sync Settings
- **Batch Size**: Reduced from 50 to 10 documents per batch
- **Timeout**: Increased from 10s to 30s for large documents
- **LAN Limit**: 5MB (5,242,880 bytes)
- **Remote Limit**: 5MB (5,242,880 bytes)

### 3. Enhanced Logging
The system now logs when large documents are skipped, helping you identify problematic documents.

## Diagnostic Steps

### Step 1: Identify Large Documents
Use the enhanced analysis method to find problematic documents:

```javascript
// In your Electron main process
const analysis = await syncService.analyzeLargeDocuments('prosy_m9xlo8hv_receipts');

console.log('Documents exceeding LAN limit (5MB):', analysis.problematicDocuments.lan);
console.log('Documents exceeding Remote limit (5MB):', analysis.problematicDocuments.remote);
console.log('Documents exceeding both limits:', analysis.problematicDocuments.both);

// Summary
console.log(`Total: ${analysis.totalDocuments}, LAN issues: ${analysis.limits.lan.exceeding}, Remote issues: ${analysis.limits.remote.exceeding}`);
```

### Step 2: Check Document Content
Look for these common causes of large documents:

1. **Base64 Images**: Receipt images stored as base64 strings
2. **Large Attachments**: Company logos or receipt scans
3. **Accumulated Arrays**: Payment history or status updates that grow over time
4. **Nested Objects**: Complex nested data structures

### Step 3: Manual Document Inspection
To manually check the problematic document `MBSZFGK2`:

```javascript
// In browser console or Node.js
const PouchDB = require('pouchdb');
const db = new PouchDB('prosy_m9xlo8hv_receipts');

db.get('MBSZFGK2', { attachments: true })
  .then(doc => {
    console.log('Document size:', JSON.stringify(doc).length, 'bytes');
    console.log('Has attachments:', !!doc._attachments);
    console.log('Attachment count:', doc._attachments ? Object.keys(doc._attachments).length : 0);
    console.log('Document structure:', Object.keys(doc));
  });
```

## Common Solutions

### Solution 1: Move Attachments to External Storage
Instead of storing images in documents, use external file storage:

```javascript
// Instead of base64 in document
const receipt = {
  _id: 'RECEIPT123',
  amount: 100,
  image: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...' // LARGE!
};

// Use external storage
const receipt = {
  _id: 'RECEIPT123',
  amount: 100,
  imageUrl: '/uploads/receipts/RECEIPT123.jpg' // Reference only
};
```

### Solution 2: Compress Images
Reduce image quality before storing:

```javascript
// In ImageUpload component, reduce quality
const canvas = document.createElement('canvas');
const ctx = canvas.getContext('2d');
// ... resize and compress image
const compressedBase64 = canvas.toDataURL('image/jpeg', 0.7); // 70% quality
```

### Solution 3: Split Large Documents
Break large documents into smaller related documents:

```javascript
// Instead of one large receipt with all data
const receipt = {
  _id: 'RECEIPT123',
  amount: 100,
  payments: [/* large array */],
  statusHistory: [/* large array */]
};

// Split into multiple documents
const receipt = { _id: 'RECEIPT123', amount: 100 };
const payments = { _id: 'RECEIPT123_payments', receiptId: 'RECEIPT123', payments: [...] };
const history = { _id: 'RECEIPT123_history', receiptId: 'RECEIPT123', statusHistory: [...] };
```

## Prevention Strategies

### 1. Document Size Monitoring
Add size checks when saving documents:

```javascript
const saveDocument = async (doc) => {
  const size = JSON.stringify(doc).length;
  if (size > 2097152) { // 2MB warning
    console.warn(`Large document detected: ${doc._id} (${Math.round(size/1024)}KB)`);
  }
  if (size > 5242880) { // 5MB limit
    throw new Error(`Document too large: ${doc._id} (${Math.round(size/1024)}KB)`);
  }
  return await db.put(doc);
};
```

### 2. Image Optimization
Implement automatic image compression:

```javascript
const optimizeImage = (file) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Resize if too large
      const maxWidth = 800;
      const maxHeight = 600;
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }
      
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);
      
      // Compress to 70% quality
      const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.7);
      resolve(compressedDataUrl);
    };
    
    img.src = URL.createObjectURL(file);
  });
};
```

## Next Steps

1. **Monitor Logs**: Watch for "Skipping large document" messages
2. **Analyze Documents**: Use the diagnostic method to identify problematic documents
3. **Implement Fixes**: Apply appropriate solutions based on document content
4. **Test Sync**: Verify that sync now works without errors

The system will now handle large documents gracefully by skipping them during sync while maintaining functionality for normal-sized documents.
