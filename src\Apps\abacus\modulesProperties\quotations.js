import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { LabelFormatter } from "../../../Utils/functions";
import { ScheduleOutlined } from "@ant-design/icons";
import { message } from "antd";
import { useRef } from "react";
// import Quotation from "../CustomViews/Quotation/";
import Quotation from '../CustomViews/Quotation';

const quotations = {
  CustomView: (data) => <Quotation {...data} />,
  MoreActions: (props) => {
    const action = useRef();

    const APP_USER_PERMISSIONS = JSON.parse(
      localStorage.getItem("APP_USER_PERMISSIONS")
    );

    const CanCreateInvoice =
      APP_USER_PERMISSIONS.root ||
      APP_USER_PERMISSIONS.permissions.find((p) => p.module === "invoices")
        .create;

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    return (
      CanCreateInvoice && (
        <TableDropdown
          key="actionGroup"
          menus={[
            {
              key: "Generate Invoice",
              name: (
                <BetaSchemaForm
                  formRef={action}
                  submitter={{
                    searchConfig: {
                      resetText: "Close",
                      submitText: "Save",
                    },
                  }}
                  modalProps={{ centered: true }}
                  grid={true}
                  trigger={
                    <a key="button" type="primary">
                      <ScheduleOutlined /> Generate Invoice
                    </a>
                  }
                  title={"Invoice"}
                  destroyOnClose={true}
                  layoutType="ModalForm"
                  onFinish={async (values) => {
                    if (record) {
                      await pouchDatabase(
                        modules.invoices.collection,
                        databasePrefix
                      ).saveDocument({ ...values, branch: record.branch, }, CRUD_USER);

                      message.success(`Invoice Created.`);
                    }
                    return true;
                  }}
                  columns={[...modules.invoices.columns]}
                  initialValues={{

                    customer: record.customer,
                    items: record.items,
                    date: new Date().toISOString(),
                    taxable: record.taxable,
                    discount: record.discount,
                    discountable: record.discountable,
                  }}
                />
              ),
            },
          ]}
        />
      )
    );
  },
  //   MoreActions: (props) => {
  //     const action = useRef();

  //     const {
  //       pouchDatabase,
  //       collection,
  //       databasePrefix,
  //       record,
  //       CRUD_USER,
  //       singular,
  //       modules,
  //     } = props;

  //     return (
  //       <TableDropdown
  //         key="actionGroup"
  //         menus={[
  //           {
  //             key: "Make Payment",
  //             name: (
  //               <BetaSchemaForm
  //                 formRef={action}
  //                 submitter={{
  //                   searchConfig: {
  //                     resetText: "Close",
  //                     submitText: "Save",
  //                   },
  //                 }}
  //                 modalProps={{ centered: true }}
  //                 grid={true}
  //                 trigger={
  //                   <a key="button" type="primary">
  //                     Make Payment
  //                   </a>
  //                 }
  //                 title={"Make Payment"}
  //                 destroyOnClose={true}
  //                 layoutType="ModalForm"
  //                 initialValues={{
  //                   customer: {
  //                     ...record.customer,
  //                     value: record.customer._id,
  //                     label: LabelFormatter(["label"], record.customer).join(" "),
  //                   },
  //                   invoice: {
  //                     ...record,
  //                     value: record._id,
  //                     label: record._id,
  //                   },
  //                 }}
  //                 onFinish={async (values) => {
  //                   await pouchDatabase(
  //                     modules.receipts.collection,
  //                     databasePrefix
  //                   ).saveDocument(values, CRUD_USER);
  //                   return true;
  //                 }}
  //                 columns={[...modules.receipts.columns]}
  //               />
  //             ),
  //           },
  //         ]}
  //       ></TableDropdown>
  //     );
  //   },
};

export default quotations;
