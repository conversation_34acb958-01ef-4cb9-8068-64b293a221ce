import React, { useEffect, useState, useMemo } from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Descriptions,
  Divider,
  Space,
  Button,
  Statistic,
  Avatar,
  Empty,
  Skeleton,
  Flex,
  Badge,
  Tag,
  Alert,
  Tooltip
} from "antd";
import {
  DollarOutlined,
  CalendarOutlined,
  FileTextOutlined,
  PrinterOutlined,
  ShopOutlined,
  InfoCircleOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined
} from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import moment from "moment";
import PouchDB from "pouchdb";
import { numberFormat } from "../../../../Utils/functions";
import PrintComponents from "react-print-components";
import "./styles.css";

const { Title, Text, Paragraph } = Typography;

const StockPayment = (props) => {
  const { data, singular } = props;
  const [invoice, setInvoice] = useState(null);
  const [supplier, setSupplier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch invoice and supplier data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch invoice details
        if (data.invoice && data.invoice.value) {
          const invoiceDB = new PouchDB("stock_purchasing");
          const invoiceDoc = await invoiceDB.get(data.invoice.value);
          setInvoice(invoiceDoc);
          
          // Fetch supplier details if invoice has supplier
          if (invoiceDoc.supplier && invoiceDoc.supplier.value) {
            const supplierDB = new PouchDB("suppliers");
            const supplierDoc = await supplierDB.get(invoiceDoc.supplier.value);
            setSupplier(supplierDoc);
          }
        }

        // Fetch company info for printing
        const companyDB = new PouchDB("company");
        const companyResult = await companyDB.allDocs({ include_docs: true });
        if (companyResult.rows.length > 0) {
          setCompany(companyResult.rows[0].doc);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data.invoice]);

  // Calculate payment statistics
  const statistics = useMemo(() => {
    if (!invoice) return { invoiceTotal: 0, remainingBalance: 0 };
    
    const invoiceTotal = invoice.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;
    const remainingBalance = invoiceTotal - (data.amount || 0);
    
    return {
      invoiceTotal,
      remainingBalance
    };
  }, [invoice, data.amount]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable payment component
  const PrintablePayment = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>PAYMENT RECEIPT</Title>
            <Text>Receipt #: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Row gutter={24}>
        <Col span={12}>
          <Title level={5}>Supplier Information</Title>
          {supplier ? (
            <div>
              <Text strong>{supplier.name}</Text><br />
              {supplier.address && <><Text>{supplier.address}</Text><br /></>}
              {supplier.phone && <><Text>Phone: {supplier.phone}</Text><br /></>}
              {supplier.email && <><Text>Email: {supplier.email}</Text><br /></>}
            </div>
          ) : (
            <Text>No supplier information available</Text>
          )}
        </Col>
        <Col span={12}>
          <Title level={5}>Payment Information</Title>
          <div>
            <Text>Date: {formatDate(data.date)}</Text><br />
            <Text>Receipt #: {data._id}</Text><br />
            <Text>Invoice #: {invoice ? invoice._id : 'N/A'}</Text><br />
            <Text>Amount Paid: {numberFormat(data.amount)}</Text>
          </div>
        </Col>
      </Row>

      <Divider />

      <Title level={5}>Payment Details</Title>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Payment Date">{formatDate(data.date)}</Descriptions.Item>
        <Descriptions.Item label="Amount Paid">{numberFormat(data.amount)}</Descriptions.Item>
        <Descriptions.Item label="Invoice Total">{invoice ? numberFormat(statistics.invoiceTotal) : 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Remaining Balance">{invoice ? numberFormat(statistics.remainingBalance) : 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Description">{data.description || 'No description provided'}</Descriptions.Item>
      </Descriptions>

      <div style={{ marginTop: 30, textAlign: 'center' }}>
        <div style={{ marginBottom: 50 }}>
          <Text>Received with thanks</Text>
        </div>
        <Row>
          <Col span={12} style={{ textAlign: 'center', borderTop: '1px solid #000' }}>
            <Text>Authorized Signature</Text>
          </Col>
        </Row>
      </div>

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Payment #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color="success" key="payment">
            <CheckCircleOutlined /> Payment
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Receipt
              </Button>
            }
          >
            <PrintablePayment />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Skeleton active paragraph={{ rows: 6 }} />
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card 
                title={<><InfoCircleOutlined /> Payment Information</>}
                className="stock-card"
              >
                <Descriptions column={1} size="small" bordered>
                  <Descriptions.Item label="Payment ID">{data._id}</Descriptions.Item>
                  <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
                  <Descriptions.Item label="Amount">
                    <Text strong style={{ color: '#52c41a' }}>{numberFormat(data.amount)}</Text>
                  </Descriptions.Item>
                  {data.description && (
                    <Descriptions.Item label="Description">{data.description}</Descriptions.Item>
                  )}
                </Descriptions>

                {supplier && (
                  <>
                    <Divider orientation="left">Supplier</Divider>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                      <Avatar 
                        icon={<ShopOutlined />} 
                        style={{ backgroundColor: '#722ed1', marginRight: 8 }} 
                      />
                      <Text strong>{supplier.name}</Text>
                    </div>
                    {supplier.phone && (
                      <div className="info-item">
                        <Text type="secondary">Phone:</Text> {supplier.phone}
                      </div>
                    )}
                    {supplier.email && (
                      <div className="info-item">
                        <Text type="secondary">Email:</Text> {supplier.email}
                      </div>
                    )}
                    <div style={{ marginTop: 16 }}>
                      <Button 
                        type="link" 
                        onClick={() => window.location.href = `#/inventory/suppliers/${supplier._id}`}
                        style={{ padding: 0 }}
                      >
                        View Supplier Details
                      </Button>
                    </div>
                  </>
                )}
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              <Card 
                title={<><FileTextOutlined /> Invoice Details</>}
                className="stock-card"
              >
                {invoice ? (
                  <>
                    <Descriptions column={1} size="small" bordered>
                      <Descriptions.Item label="Invoice Number">
                        <Space>
                          {invoice._id}
                          <Button 
                            type="link" 
                            size="small"
                            onClick={() => window.location.href = `#/inventory/stock_purchasing/${invoice._id}`}
                          >
                            View Invoice
                          </Button>
                        </Space>
                      </Descriptions.Item>
                      <Descriptions.Item label="Invoice Date">{formatDate(invoice.date)}</Descriptions.Item>
                      <Descriptions.Item label="Invoice Total">{numberFormat(statistics.invoiceTotal)}</Descriptions.Item>
                      <Descriptions.Item label="Amount Paid">{numberFormat(data.amount)}</Descriptions.Item>
                      <Descriptions.Item label="Remaining Balance">
                        <Text 
                          strong 
                          style={{ color: statistics.remainingBalance > 0 ? '#f5222d' : '#52c41a' }}
                        >
                          {numberFormat(statistics.remainingBalance)}
                        </Text>
                      </Descriptions.Item>
                    </Descriptions>

                    <Divider orientation="left">Payment Status</Divider>
                    
                    {statistics.remainingBalance <= 0 ? (
                      <Alert
                        message="Fully Paid"
                        description="This invoice has been fully paid."
                        type="success"
                        showIcon
                        icon={<CheckCircleOutlined />}
                      />
                    ) : (
                      <Alert
                        message="Partially Paid"
                        description={`This invoice has a remaining balance of ${numberFormat(statistics.remainingBalance)}.`}
                        type="warning"
                        showIcon
                      />
                    )}
                  </>
                ) : (
                  <Empty description="No invoice information available" />
                )}
              </Card>

              <Card 
                title={<><DollarOutlined /> Payment Summary</>}
                className="stock-card"
                style={{ marginTop: 16 }}
              >
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="Amount Paid"
                      value={numberFormat(data.amount)}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="Invoice Total"
                      value={numberFormat(statistics.invoiceTotal)}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="Remaining Balance"
                      value={numberFormat(statistics.remainingBalance)}
                      valueStyle={{ color: statistics.remainingBalance > 0 ? '#f5222d' : '#52c41a' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default StockPayment;
