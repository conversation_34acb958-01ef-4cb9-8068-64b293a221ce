import React, { useEffect, useState } from "react";
import ReceiptTemplate from "../../../Universal/CustomViews/ReceiptTemplate";
import PouchDb from "pouchdb-browser";

const OrderReceipt = ({ data }) => {
  const [guest, setGuest] = useState(null);
  const [invoice, setInvoice] = useState(null);
  const [receipt, setReceipt] = useState(null);

  useEffect(() => {
    const guestDB = new PouchDb("guests");
    data.order && invoice && invoice.guest
      ? guestDB.get(invoice.guest.value).then((data) => {
        setGuest(data);
      })
      : setGuest(null);
  });

  useEffect(() => {
    const invoiceDB = new PouchDb("orders");
    data.order
      ? invoiceDB.get(data.order._id).then((data) => {
        setInvoice(data);
      })
      : setInvoice(null);
  });

  useEffect(() => {
    if (data.order) {
      const receiptDB = new PouchDb("order_receipts");
      receiptDB.allDocs({ include_docs: true })
        .then((res) => {
          const filteredData = res.rows.filter(
            (row) =>
              row &&
              row.doc &&
              row.doc._id &&
              !row.doc._id.startsWith('_') &&
              row.doc.order &&
              row.doc.order._id === data.order._id
          );

          setReceipt(filteredData);
        })
        .catch((error) => {
          console.error("Error fetching order receipts:", error);
          setReceipt(null);
        });
    } else {
      setReceipt(null);
    }
  }, [data.order?._id]); // Added proper dependencies

  

  const newdata = {
    ...data,
    date: data.date,
    client: { title: "Guest", name: guest },
    paid:
      receipt && receipt.length > 0
        ? receipt.reduce((previous, current) => {
          return previous + parseInt(current.doc.amount);
        }, 0)
        : 0,
    operator: { title: "Cashier", name: data.entrant && data.entrant.label },
    invoice: {
      ...invoice,
      items: invoice
        ? [
          ...invoice.items.map((item) => {
            return {
              name: item.item.label.split(" - ")[0],
              price: item.cost,
              quantity: item.quantity,
            };
          }),
        ]
        : [],
    },
  };
  return (
    <div>
      <ReceiptTemplate data={newdata} documentTitle={"Order Receipt"} />
    </div>
  );
};

export default OrderReceipt;
