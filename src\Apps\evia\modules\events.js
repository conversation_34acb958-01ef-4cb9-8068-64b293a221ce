import PouchDb from "pouchdb-browser";
import { formatMoney, numberFormat } from "../../../Utils/functions";
import moment from "moment";

export default {
  name: "Events",
  icon: "CalendarOutlined",
  path: "events_management/events",
  collection: "events",
  singular: "Event",
  parent: "events_management",
  columns: [
    {
      type: "dbSelect",
      collection: "guests",
      label: ["title", "sur_name", "first_name"],
      dataIndex: "guest",
      title: "Event Host",
      width: "lg",
      valueType: "text",
      isRequired: true,
      filters: true,
      onFilter: true,
      isPrintable: true,
    },
    {
      title: "Event Name",
      dataIndex: "eventName",
      valueType: "text",
      sorter: true,
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Start Date",
      dataIndex: "start_date",
      valueType: "dateTime",
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "End Date",
      dataIndex: "end_date",
      valueType: "dateTime",
      isRequired: true,
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "Number of Attendees",
      dataIndex: "attendees",
      valueType: "digit",
      isPrintable: true,
      colProps: {
        md: 8,
      },
    },
    {
      title: "venue",
      dataIndex: "venue",
      type: "dbSelect",
      collection: "venues",
      isPrintable: true,
      label: ["name"],
      colProps: {
        md: 8,
      },
    },
    //dependency schema column for price with all attributes

    // {
    //   valueType: "dependency",
    //   hideInTable: true,
    //   fieldProps: {
    //     name: ["venue", "attributes"],
    //   },
    //   columns: (ps) => {
    //     const venuesDB = new PouchDb("venues");

    //     
    //     venuesDB
    //       .get(45)
    //       // .allDocs({
    //       //   include_docs: true,
    //       //   attachments: true,
    //       //   startkey: record.venue._id,
    //       //   endkey: record.venue._id + "\uffff",
    //       // })
    //       .then((data) => {
    //         // let venue = data.rows[0].doc;
    //         
    //         // value = venue.price;
    //         return [
    //           {
    //             dataIndex: "Price",
    //             title: "Price",
    //             width: "m",
    //             valueType: "digit",
    //           },
    //         ];
    //       });
    //   },
    // },

    {
      title: "Price",
      dataIndex: "price",
      valueType: "digit",
      isPrintable: true,
      dependencies: ["venue"],
      fieldProps: (form) => {
        const venuesDB = new PouchDb("venues");
        venuesDB
          .get(form.getFieldValue("venue").value)
          // .allDocs({
          //   include_docs: true,
          //   attachments: true,
          //   startkey: record.venue._id,
          //   endkey: record.venue._id + "\uffff",
          // })
          .then((data) => {
            // let venue = data.rows[0].doc;

            // value = venue.price;
            return {
              // name: ["price"],
              value: data.price,
            };
          });
        // if (form.getFieldValue("title") === "disabled") {
        //   return {
        //     disabled: true,
        //     placeholder: "disabled",
        //   };
        // } else {
        //   return {
        //     placeholder: "normal",
        //   };
        // }
      },

      colProps: {
        md: 8,
      },
    },

    {
      valueType: "dependency",
      hideInTable: true,
      name: ["start_date", "end_date"],
      colProps: {
        md: 8,
      },
      columns: ({ start_date, end_date }) => {
        return [
          {
            title: "Duration (Hours)",
            dataIndex: "duration",
            valueType: "digit",
            tooltip: "Duration of event in hours",
            fieldProps: {
              disabled: true,
              value: moment(end_date).diff(moment(start_date), "hours"),
              // (end_date - start_date) / 3600000,
            },
            colProps: {
              md: 8,
            },
          },
        ];
      },
    },

    {
      title: "Resorces",
      valueType: "formList",
      width: "lg",
      colProps: {
        md: 24,
      },
      hideInTable: true,
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      dataIndex: "items",
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              title: "Resoure",
              dataIndex: "item",
              type: "dbSelect",
              collection: "resources",
              label: ["name"],
              colProps: {
                md: 8,
              },
            },
            {
              title: "Qty",
              dataIndex: "quantity",
              valueType: "digit",
              colProps: {
                md: 4,
              },
            },
            {
              title: "Unit Cost",
              colProps: {
                md: 6,
              },
              dataIndex: "cost",
              valueType: "digit",
            },
            {
              valueType: "dependency",
              colProps: {
                md: 6,
              },
              fieldProps: {
                name: ["cost", "quantity"],
              },
              columns: function columns(_ref2) {
                var cost = _ref2.cost,
                  quantity = _ref2.quantity;
                return [
                  {
                    title: "Total",
                    dataIndex: "total",
                    colProps: {
                      md: 6,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (cost ? cost : 0) * (quantity ? quantity : 0),
                    },
                    valueType: "digit",
                  },
                ];
              },
            },
          ],
        },
      ],
    },

    {
      valueType: "dependency",
      title: "G Total",
      isPrintable: true,
      width: "lg",
      colProps: {
        md: 24,
      },
      // hideInTable: true,
      render: function render(record, _ref4) {
        var items = _ref4.items;
        var total = 0;
        items &&
          items.map(function (item) {
            return (total = item
              ? (item.cost ? item.cost : 0) *
              (item.quantity ? item.quantity : 0)
              : total);
          });
        return (total + _ref4.price).toLocaleString();
      },
      fieldProps: {
        name: ["items"],
        gap: 10, // value: items && items.map(item => total = item ? total + ((((item.tax ? item.tax : 0) / 100)) * ((item.cost ? item.cost : 0) * (item.quantity ? item.quantity : 0))) + ((item.cost ? item.cost : 0) * (item.quantity ? item.quantity : 0)) : total)
      },
      columns: function columns(_ref5) {
        var items = _ref5.items;
        var total = 0;
        items &&
          items.map(function (item) {
            total = item
              ? total +
              (item.cost ? item.cost : 0) *
              (item.quantity ? item.quantity : 0)
              : total;
          });
        return [
          {
            Title: "Grand Total",
            dataIndex: "total",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "digit",
            render: function render(v) {
              return formatMoney(v);
            },
            renderFormItem: function renderFormItem() {
              return "Total : " + total;
            },
          },
        ];
      },
    },

    {
      title: "Balance",
      dataIndex: "balance",
      valueType: "digit",
      hideInForm: true,
      isPrintable: true,
      render: function render(v, record) {


        const balance = record.items
          ? record.items.reduce(
            (total, item) =>
              total +
              (item.cost ? item.cost : 0) *
              (item.quantity ? item.quantity : 0),
            0
          )
          : 0;

        return numberFormat(record.price + balance - record.receipts);
      },
    },

    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      isPrintable: true,
      colProps: {
        md: 24,
      },
    },
  ],
};
