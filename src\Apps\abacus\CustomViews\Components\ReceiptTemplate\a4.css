.tm_invoice_wrap {
  max-width: 210mm;
  margin: 20px auto;
  padding: 20px;
  background: white;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.tm_invoice {
  font-family: 'Arial', sans-serif;
  color: #333;
}

.tm_invoice_in {
  padding: 20px;
}

.tm_f12 {
  font-size: 12px;
  line-height: 1.4;
  color: #666;
}

.tm_invoice_info {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.tm_invoice_number, .tm_invoice_date {
  font-size: 14px;
  margin: 5px 0;
}

.tm_table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.tm_table th {
  background: #f8f9fa;
  padding: 10px;
  text-align: left;
  border-bottom: 2px solid #eee;
}

.tm_table td {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.tm_table tr:last-child td {
  border-bottom: none;
}

.tm_invoice_footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.tm_right_footer table {
  width: 300px;
}

.tm_right_footer td {
  padding: 8px;
}

.tm_right_footer tr:last-child td {
  font-weight: bold;
  border-top: 2px solid #eee;
}

@media print {
  .tm_invoice_wrap {
    box-shadow: none;
    margin: 0;
    padding: 10px;
  }
}