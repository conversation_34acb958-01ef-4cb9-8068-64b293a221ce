export default {
  name: 'Setting<PERSON>',
  icon: 'KeyOutlined',
  path: '/settings',
  collection: 'settings',
  singular: 'Setting',
  columns: [{
    type: 'text',
    dataIndex: 'usd_rate',
    title: 'UGX to USD Conversion Rate'
  }, {
    valueType: 'group',
    title: "Banking Information",
    colProps: {
      md: 24
    },
    columns: [{
      type: 'text',
      title: 'Account Name',
      dataIndex: 'acc_name',
      colProps: {
        md: 24
      }
    }, {
      type: 'text',
      title: 'Account Number',
      dataIndex: 'acc_no',
      colProps: {
        md: 12
      }
    }, {
      type: 'text',
      title: 'Bank Name',
      dataIndex: 'bank_name',
      colProps: {
        md: 12
      }
    }, {
      type: 'text',
      title: 'Bank Address',
      dataIndex: 'bank_address',
      colProps: {
        md: 12
      }
    }, {
      type: 'text',
      title: 'Swift Code',
      dataIndex: 'swift_code',
      colProps: {
        md: 12
      }
    }]
  }]
};