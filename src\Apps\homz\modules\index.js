// const { guests } = require('./guests')
// const { invoices } = require('./invoices')
// const { checkins } = require('./checkins')
// const { receipts } = require('./receipts')
// const { apartments } = require('./apartments')
// const { reservations } = require('./reservations')
// const { companies } = require('./companies')
import guests from './guests';
import invoices from './invoices';
import checkins from './checkins';
import receipts from './receipts';
import apartments from './apartments';
import reservations from './reservations';
import companies from './companies';
import extensions from './extensions';
import transfers from './transfers';
export default {
  reservations: reservations,
  guests: guests,
  checkins: checkins,
  invoices: invoices,
  receipts: receipts,
  apartments: apartments,
  companies: companies,
  extensions: extensions,
  transfers: transfers
};