import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { LabelFormatter } from "../../../Utils/functions";
import { useRef } from "react";
import Invoice from "../CustomViews/Invoice";
import PouchDb from "pouchdb-browser";
import { message } from "antd";

const invoices = {
  CustomView: (data) => <Invoice {...data} />,
  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        menus={[
          {
            key: "Make Payment",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Close",
                    submitText: "Save",
                  },
                }}
                modalProps={{ centered: true }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Make Payment
                  </a>
                }
                title={"Make Payment"}
                destroyOnClose={true}
                layoutType="ModalForm"
                initialValues={{
                  customer: {
                    ...record.customer,
                    value: record.customer.value,
                    label: LabelFormatter(["label"], record.customer).join(" ")
                  },
                  invoice: {
                    ...record,
                    value: record._id,
                    label: record._id,
                  },
                }}
                onFinish={async (values) => {
                  await pouchDatabase(
                    modules.receipts.collection,
                    databasePrefix
                  ).saveDocument({
                    ...values,
                    branch: localStorage.getItem("SELECTED_BRANCH")
                  }, CRUD_USER);
                  return true;
                }}
                columns={[...modules.receipts.columns]}
              />
            ),
          },
        ]}
      ></TableDropdown>
    );
  },
  afterSave: async (data) => {
    // if the invoice is paid, generate a receipt

    


    if (!data.not_paid) {
      // Generate receipt account for tax if applicable

      
      const accountsDB = new PouchDb("accounts");
      const accounts = (await accountsDB.allDocs({ include_docs: true, binary: true }).then((res) => res.rows.map((r) => r.doc))).
        filter(account => !account._id.startsWith("_"));

      const receipt = {
        ...data,
        amount: data.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) + (data.taxable ? 0.18 : 0) * data.items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        date: new Date().toISOString(),
        pMethod: "cash",
        account: accounts.length > 0 ? {
          label: accounts[0].label,
          value: accounts[0]._id,
          ...accounts[0],
        } : undefined,
        description: `Payment for ${data.customer.label} (INV${data._id}) : [Auto Gen]`,
        _id: Date.now().toString(36).toUpperCase(),
        invoice: {
          label: "INV-" + data._id + " " + data.customer.label,
          value: data._id,
          ...data,
        },
      };

      const receiptsDB = new PouchDb("receipts");
      receiptsDB.put(receipt).then(() => {
        message.success("Receipt Also Added");
      });

    }
  },
};

export default invoices;
