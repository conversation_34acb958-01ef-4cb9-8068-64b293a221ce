import React, { useEffect, useState } from "react";
import InvoiceTemplate from "../../../Universal/CustomViews/Components/InvoiceTemplate";
import PouchDb from "pouchdb-browser";

const OrderInvoice = ({ data, documentTitle }) => {
  

  const [guest, setGuest] = useState(null);

  useEffect(() => {
    const guestDB = new PouchDb("guests");
    data.guest
      ? guestDB.get(data.guest.value).then((data) => {
          setGuest(data);
        })
      : setGuest(null);
  }, [data]);

  const newdata = {
    date: data.createdAt,
    id: data._id,
    client: { title: "Guest", name: guest }, //data.guest,
    operator: { title: "Waiter", name: data.waiter.label },
    items: [
      ...data.items.map((item) => {
        return {
          name: item.item.label,
          price: item.cost,
          quantity: item.quantity,
        };
      }),
    ],
  };

  return (
    <div>
      <InvoiceTemplate data={newdata} documentTitle={documentTitle} />
    </div>
  );
};

export default OrderInvoice;
