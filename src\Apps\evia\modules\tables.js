export default {
  name: "Tables",
  icon: "TableOutlined",
  path: "restaurant/tables",
  collection: "tables",
  singular: "Table",
  parent: "restaurant",
  columns: [
    {
      title: "Table Name",
      dataIndex: "tableName",
      valueType: "text",
      isRequired: true,
      isPrintable: true,
    },
    // {
    //   title: "Status",
    //   dataIndex: "tableStatus",
    //   valueType: "select",
    //   filters: true,
    //   valueEnum: {
    //     Occupied: {
    //       text: "Occupied",
    //     },
    //     Vacant: {
    //       text: "Vacant",
    //     },
    //   },
    // },
  ],
};
