import { Descriptions, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";
import moment from "moment";
import { servicesColumns } from "../../modulesProperties/checkins";
import { numberFormat } from "../../../../Utils/functions";

const Checkin = ({ pouchDatabase, databasePrefix, data }) => {
  const [guest, setGuest] = useState({});
  const [room, setRoom] = useState({});

  const organization = JSON.parse(
    localStorage.getItem(LOCAL_STORAGE_ORGANIZATION)
  );

  

  useEffect(function () {
    data.guest &&
      pouchDatabase("guests", databasePrefix)
        .getDocument(data.guest.value)
        .then(function (res) {
          setGuest(res);
        });
  }, []);
  useEffect(function () {
    data.guest &&
      pouchDatabase("rooms", databasePrefix)
        .getDocument(data.room_number.value)
        .then(function (res) {
          setRoom(res);
        });
  }, []);

  return (
    <div>
      <Descriptions
        title="Guest Information"
        size="small"
        labelStyle={{ fontWeight: 600 }}
      >
        <Descriptions.Item label="Name">{`${guest.title}. ${guest.sur_name} ${guest.first_name}`}</Descriptions.Item>
        <Descriptions.Item label="Phone">{guest.phone}</Descriptions.Item>
        <Descriptions.Item label="Mobile">{guest.mobile}</Descriptions.Item>
        <Descriptions.Item label="Email">{guest.email}</Descriptions.Item>
        <Descriptions.Item label="Address">{guest.address}</Descriptions.Item>
        <Descriptions.Item label="Date Of Birth">
          {moment(guest.date_of_birth).format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Nationality">
          {guest.nationality && guest.nationality.value}
        </Descriptions.Item>
        <Descriptions.Item label="Age">
          {moment().diff(moment(guest.date_of_birth), "years")}
        </Descriptions.Item>
      </Descriptions>
      <Descriptions
        title="ID Information"
        size="small"
        labelStyle={{ fontWeight: 600 }}
        style={{ marginTop: "10px" }}
      >
        <Descriptions.Item label="ID">{guest.id_type}</Descriptions.Item>
        <Descriptions.Item label="ID Number">
          {guest.id_number}
        </Descriptions.Item>
        <Descriptions.Item label="Place Of Issue">
          {guest.country && guest.country.label}
        </Descriptions.Item>
        <Descriptions.Item label="Date of issue">
          {guest.date_of_issue}
        </Descriptions.Item>
      </Descriptions>
      <Descriptions
        title="Checkin Information"
        size="small"
        labelStyle={{ fontWeight: 600 }}
      >
        <Descriptions.Item label="Checkin Date">
          {moment(data.arrival_date).format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Checkin Time">
          {moment(data.arrival_date).format("HH:mm")}
        </Descriptions.Item>
        <Descriptions.Item label="Room Rate">
          {numberFormat(data.room_rate)}
        </Descriptions.Item>
        <Descriptions.Item label="Checkout Date">
          {moment(data.departure_date).format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Checkout Time">
          {moment(data.departure_date).format("HH:mm")}
        </Descriptions.Item>
        <Descriptions.Item label="Checked out ?">
          {data.checked_out ? "Yes" : "No"}
        </Descriptions.Item>
        <Descriptions.Item label="Room Number">
          {room.number} ({room.short_desc})
        </Descriptions.Item>
        
        {data.extensions && (
          <Descriptions.Item label="Extensions">
            {data.extensions / data.room_rate} day(s)
          </Descriptions.Item>
        )}
        <Descriptions.Item label="Total Bill">
          <strong> {numberFormat(data.check_in_total)} </strong>
            
          </Descriptions.Item>
        </Descriptions>
        {data.transfers && data.transfers.length > 0 &&
        <>
        <Typography.Title level={4}> Transfers </Typography.Title>
        <table>
            <thead>
              <tr>
                <td>
                  <strong>Date</strong>
                </td>
                <td>
                  <strong>Transferred To</strong>
                </td>
                <td>
                  <strong>Rate</strong>
                </td>
              </tr>
            </thead>
            <tbody>
{
  data.transfers.map((transfer, index) => <tr>
  <td>
    <strong>{transfer.transfer_date}</strong>
  </td>
  <td>{transfer.room_number.label}</td>
  <td>{numberFormat(data.room_rate)}</td>
</tr>

  )
}
            </tbody>
            </table>
        </>
}

      {data.items && (
        <>
          <Typography.Title level={4}> Other Bills </Typography.Title>
          <table>
            <thead>
              <tr>
                <td>
                  <strong>Item</strong>
                </td>
                <td>
                  <strong>Quntity</strong>
                </td>
                <td>
                  <strong>Unit Price</strong>
                </td>
                <td>
                  <strong>Total</strong>
                </td>
              </tr>
            </thead>
            <tbody>
              {data.items.map((item) => (
                <tr>
                  <td>
                    <strong>{item.item}</strong>
                  </td>
                  <td>{item.quantity}</td>
                  <td>{item.cost}</td>
                  <td>{item.quantity * item.cost}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </>
      )}
    </div>
  );
};

export default Checkin;
