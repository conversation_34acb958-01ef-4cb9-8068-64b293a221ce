/* Base styles */
.receipt-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: "NoirPro-Regular", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.title-section strong {
  font-family: "NoirPro-SemiBold", sans-serif;
}

.section-title {
  font-family: "NoirPro-Medium", sans-serif;
  margin-bottom: 8px;
  color: #666;
}

.info-row strong {
  font-family: "NoirPro-Medium", sans-serif;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.logo-section {
  flex: 0 0 200px;
}

.title-section {
  flex: 1;
  text-align: center;
}

.company-details {
  flex: 0 0 250px;
  font-size: 12px;
  line-height: 1.5;
  text-align: right;
}

.receipt-number {
  font-size: 14px;
  margin: 10px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin: 15px 0;
}

.customer-section,
.amount-section {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  line-height: 1.2;
}

.amount-words {
  margin: 10px 0;
  padding: 8px 0;
  border-top: 1px dashed #ddd;
  border-bottom: 1px dashed #ddd;
  font-style: italic;
  color: #555;
  line-height: 1.4;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #666;
}

.info-row {
  margin-bottom: 3px;
}

.invoice-items {
  margin: 30px 0;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.items-table th,
.items-table td {
  border: 1px solid #ddd;
  padding: 1px 13px;
  padding: 1px 13px;
}

.items-table th {
  background-color: #f5f5f5;
  font-family: "NoirPro-Medium", sans-serif;
}

.items-table .sn-column {
  width: 50px;
  text-align: center;
}

.items-table .amount {
  text-align: right;
}

.previous-receipts {
  margin: 30px 0;
}

.payment-summary {
  margin: 30px 0;
}

.summary-table {
  width: 50%;
  margin-left: auto;
}

.summary-table td {
  padding: 8px;
}

.signature-section {
  margin-top: 50px;
}

.signature-box {
  text-align: center;
  margin: 20px;
}

.signature-line {
  margin-top: 30px;
  border-top: 1px solid #000;
  width: 200px;
}

/* Print specific styles */
@media print {

  /* Ensure fonts are embedded */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  @page {
    size: A4;
  }

  .receipt-container {
    padding: 0;
    max-width: 100%;
    font-family: "NoirPro-Regular", sans-serif !important;
  }

  .title-section strong {
    font-family: "NoirPro-SemiBold", sans-serif !important;
    font-size: 25px !important;
    font-weight: normal !important;
  }

  .section-title {
    font-family: "NoirPro-Medium", sans-serif !important;
    color: #666 !important;
    font-weight: normal !important;
  }

  .info-row strong {
    font-family: "NoirPro-Medium", sans-serif !important;
    font-weight: normal !important;
  }

  .amount-words {
    font-family: "NoirPro-Light", sans-serif !important;
    font-style: italic !important;
    color: #555 !important;
    font-weight: normal !important;
  }

  /* Force background colors and borders to print */
  .amount-section,
  .customer-section {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
    background-color: #f8f9fa !important;
    border-radius: 5px !important;
  }

  /* Ensure table styles print correctly */
  .ant-table {
    font-family: "NoirPro-Regular", sans-serif !important;
  }

  .ant-table-thead>tr>th {
    font-family: "NoirPro-Medium", sans-serif !important;
    background-color: #f5f5f5 !important;
    font-weight: normal !important;
  }

  .items-table th,
  .items-table td {
    padding: 5px !important;
    border: 1px solid #ddd !important;
  }

  .items-table th {
    background-color: #f5f5f5 !important;
  }

  /* Print specific adjustments */
  .customer-section,
  .amount-section {
    line-height: 1.2 !important;
    padding: 12px !important;
  }

  .info-row {
    margin-bottom: 3px !important;
  }

  .section-title {
    margin-bottom: 8px !important;
  }
}