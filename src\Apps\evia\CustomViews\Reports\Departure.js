import React, { useEffect, useState } from "react";
import {
  But<PERSON>,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Row,
  Col,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import { formatMoney, numberFormat } from "../../../../Utils/functions";
import "./print.css";
import DocumentHead from "./DocumentHead";

const Departure = (props) => {
  const [dateRange, setDateRange] = useState(null);

  const { checkIns, guests, rooms, company } = props;

  

  const columns = [
    {
      title: "Departure",
      dataIndex: "departure_date",
      key: "departure_date",
      render: (text, r) => moment(r.checkout_date).format("DD MMM YY - HH:mm"),
    },
    {
      title: "Arrival",
      dataIndex: "arrival_date",
      key: "arrival_date",
      render: (text) => moment(text).format("DD MMM YY"),
    },
    {
      title: "Room no",
      key: "room_number",
      dataIndex: "room_number",
      render: (text) =>
        text && text.label && text.label.split(" ").reverse().pop(),
    },
    {
      title: "Guest Name",
      dataIndex: "guest",
      key: "guest",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Checkin ID",
      dataIndex: "_id",
      key: "_id",
      render: (text, record) =>
        record.ref_number ? `${text} - ${record.ref_number}` : text,
    },
    {
      title: "Company",
      dataIndex: "company",
      key: "company",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Checked Out By",
      dataIndex: "updatedBy",
      key: "updatedBy",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Rate",
      dataIndex: "room_rate",
      key: "room_rate",
      render: (text) => text && formatMoney(text),
    },
    {
      title: "Nights",
      dataIndex: "nights",
      key: "nights",
      render: (t, record) =>
        moment(
          record.extensions
            ? record.extensions.departure_date
            : record.departure_date
        ).diff(moment(record.arrival_date), "days"),
    },
    {
      title: "Amount",
      dataIndex: "company",
      key: "company",
      render: (text, record) =>
        numberFormat(
          record.check_in_total
        ),
    },
  ];

  return (
    <div>
      <Table
        size="small"
        columns={columns}
        dataSource={
          dateRange
            ? checkIns.filter(
                (r) =>
                  r.checked_out &&
                  moment(r.departure_date).isSameOrAfter(
                    moment(dateRange[0].$d),
                    "day"
                  ) &&
                  moment(r.departure_date).isSameOrBefore(
                    moment(dateRange[1].$d),
                    "day"
                  )
              )
            : checkIns
                .filter((r) => r.checked_out)
                .reverse()
                .slice(0, 10)
                .reverse()
        }
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                initialValue={[moment().startOf("month"), moment()]}
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>
                    Guest Departure Report
                  </Typography.Title>
                  <Table
                    size="small"
                    columns={columns}
                    dataSource={
                      dateRange
                        ? checkIns.filter(
                            (r) =>
                              r.checked_out &&
                              moment(r.checkout_date).isSameOrAfter(
                                moment(dateRange[0].$d),
                                "day"
                              ) &&
                              moment(r.checkout_date).isSameOrBefore(
                                moment(dateRange[1].$d),
                                "day"
                              )
                          )
                        : checkIns
                            .filter((r) => r.checked_out)
                            .reverse()
                            .slice(0, 10)
                            .reverse()
                    }
                    pagination={false}
                    className="custom-table"
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Departure;
