const moment = require("moment");
exports.product_prices = {
  name: "Product Prices",
  icon: "TagsOutlined",
  path: "/product_management/pricing",
  parent: "product_management",
  collection: "product_prices",
  singular: "Price",
  multi_Branch: true,
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      isRequired: true,
      initialValue: moment().startOf("day"), // Use moment.js to get the current date
    },
    {
      dataIndex: "product",
      title: "Product",
      type: "dbSelect",
      valueType: "select",
      collection: "products",
      label: ["name", " ", "sku", " - ", "measurements", "units"],
      isRequired: true,
    },
    { valueType: "money", dataIndex: "price", title: "Selling Price", isRequired: true },
  ],
};
