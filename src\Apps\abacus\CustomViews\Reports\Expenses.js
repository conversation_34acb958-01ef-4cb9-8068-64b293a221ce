import React, { useState, useMemo } from 'react';
import { Table, Button, Row, Col, Input, Typography, DatePicker } from 'antd';
import { PrinterOutlined } from '@ant-design/icons';
import PrintComponents from 'react-print-components';
import DocumentHead from '../../../Universal/CustomViews/Components/DocumentHead';
import { useReportsData } from './ReportsContext';
import './print.css';
import { formatNumber } from '../../../../Utils/functions';
import dayjs from 'dayjs';
import moment from 'moment';

const { RangePicker } = DatePicker;

const Expenses = () => {
  const { expense: expenses, loading, company } = useReportsData();
  const [keyword, setKeyword] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);

  const processedData = useMemo(() => {
    if (!expenses?.length) return [];

    return expenses
      .filter(expense => {
        return dateRange && moment(expense.date).isSameOrAfter(moment(dateRange[0].$d), "day") &&
          moment(expense.date).isSameOrBefore(moment(dateRange[1].$d), "day")
      })
      .map(expense => ({
        ...expense,
        key: expense._id
      }));
  }, [expenses, dateRange]);

  const columns = [
    {
      title: 'No',
      dataIndex: 'no',
      width: 50,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Date',
      dataIndex: 'date',
      render: date => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      render: category => category?.label,
    },
    {
      title: 'Description',
      dataIndex: 'description',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      align: 'right',
      render: value => formatNumber(value),
    }
  ];

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    let filtered = processedData;
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter(record =>
        JSON.stringify(record).toLowerCase().includes(searchLower)
      );
    }
    return filtered.sort((a, b) => dayjs(b.date).unix() - dayjs(a.date).unix());
  }, [processedData, keyword]);

  const tableProps = {
    size: 'small',
    columns,
    dataSource: filteredData,
    loading,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    scroll: { y: 'calc(100vh - 350px)' },
    summary: (pageData) => {
      const totalAmount = pageData.reduce((sum, row) => sum + (row.amount || 0), 0);

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            <Table.Summary.Cell colSpan={4}>Total</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totalAmount)}</Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    }
  };

  return (
    <div>
      <Table
        {...tableProps}
        title={() => (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Input.Search
                placeholder="Search..."
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: '100%' }}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={8} style={{ textAlign: 'right' }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Expense Report</Typography.Title>
                  <Typography.Text>
                    Period: {dateRange[0].format('DD/MM/YYYY')} - {dateRange[1].format('DD/MM/YYYY')}
                  </Typography.Text>
                  <Table
                    {...tableProps}
                    pagination={false}
                    scroll={false}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Expenses;