const { ipcMain, dialog, shell, app, BrowserWindow } = require('electron');
const nodemailer = require('nodemailer');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const DatabaseHandler = require('./database/DatabaseHandler');
const SyncService = require('./database/SyncService');

/**
 * PERFORMANCE & SECURITY: Enhanced IPC handlers for Electron main process
 * Provides secure communication between renderer and main process
 */

class IPCHandlers {
  constructor() {
    // Initialize database services
    this.databaseHandler = new DatabaseHandler();
    this.syncService = new SyncService(this.databaseHandler);

    this.setupHandlers();
    this.setupDatabaseHandlers();
  }

  setupHandlers() {
    // App information handlers
    ipcMain.handle('get-app-version', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node,
        chromeVersion: process.versions.chrome
      };
    });

    // System information handlers
    ipcMain.handle('get-system-info', () => {
      return {
        platform: process.platform,
        arch: process.arch,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
        uptime: os.uptime(),
        userInfo: os.userInfo()
      };
    });

    // Memory usage monitoring
    ipcMain.handle('get-memory-usage', () => {
      const memoryUsage = process.memoryUsage();
      return {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) // MB
      };
    });

    // Window control handlers
    ipcMain.handle('minimize-window', (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win) win.minimize();
    });

    ipcMain.handle('maximize-window', (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win) {
        if (win.isMaximized()) {
          win.unmaximize();
        } else {
          win.maximize();
        }
      }
    });

    ipcMain.handle('close-window', (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win) win.close();
    });

    // Secure email handler
    ipcMain.handle('send-email', async (event, emailData) => {
      try {
        const { from, to, subject, html } = emailData;

        const transporter = nodemailer.createTransport({
          host: 'smtp.yandex.com',
          port: 465,
          secure: true,
          auth: {
            user: '<EMAIL>',
            pass: '@*********' // Consider using environment variables
          },
          tls: {
            rejectUnauthorized: false
          }
        });

        const mailOptions = {
          from: from || '"Haclab Company Limited" <<EMAIL>>',
          to: to,
          subject: subject,
          html: html
        };

        const info = await transporter.sendMail(mailOptions);
        return { success: true, messageId: info.messageId };
      } catch (error) {
        console.error('Email sending failed:', error);
        return { success: false, error: error.message };
      }
    });

    // Secure file operations
    ipcMain.handle('select-file', async (event, options = {}) => {
      try {
        const result = await dialog.showOpenDialog({
          properties: ['openFile'],
          filters: options.filters || [
            { name: 'All Files', extensions: ['*'] }
          ],
          ...options
        });

        if (!result.canceled && result.filePaths.length > 0) {
          const filePath = result.filePaths[0];
          const stats = await fs.stat(filePath);

          return {
            success: true,
            filePath: filePath,
            fileName: path.basename(filePath),
            fileSize: stats.size,
            lastModified: stats.mtime
          };
        }

        return { success: false, canceled: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('save-file', async (event, data) => {
      try {
        const { content, defaultPath, filters } = data;

        const result = await dialog.showSaveDialog({
          defaultPath: defaultPath,
          filters: filters || [
            { name: 'All Files', extensions: ['*'] }
          ]
        });

        if (!result.canceled && result.filePath) {
          await fs.writeFile(result.filePath, content, 'utf8');
          return { success: true, filePath: result.filePath };
        }

        return { success: false, canceled: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Performance monitoring
    ipcMain.handle('get-performance-metrics', () => {
      return {
        timestamp: Date.now(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        uptime: process.uptime()
      };
    });

    // External link handler
    ipcMain.handle('open-external', async (event, url) => {
      try {
        await shell.openExternal(url);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // App restart handler
    ipcMain.handle('restart-app', () => {
      app.relaunch();
      app.exit();
    });

    // Clear cache handler
    ipcMain.handle('clear-cache', async (event) => {
      try {
        const win = BrowserWindow.fromWebContents(event.sender);
        if (win) {
          await win.webContents.session.clearCache();
          await win.webContents.session.clearStorageData();
          return { success: true };
        }
        return { success: false, error: 'Window not found' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    console.log('✅ IPC Handlers initialized successfully');
  }

  setupDatabaseHandlers() {
    // Database initialization
    ipcMain.handle('db-initialize', async (event, { name, databasePrefix, lan_details, branch }) => {
      return await this.databaseHandler.initializeDatabase(name, databasePrefix, lan_details, branch);
    });

    // CRUD operations
    ipcMain.handle('db-save', async (event, { dbKey, data, user }) => {
      try {
        const result = await this.databaseHandler.saveDocument(dbKey, data, user);
        return { success: true, id: result.id, doc: result.doc };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-get', async (event, { dbKey, id }) => {
      try {
        const doc = await this.databaseHandler.getDocument(dbKey, id);
        return { success: true, doc };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-get-all', async (event, { dbKey, options }) => {
      try {
        const docs = await this.databaseHandler.getAllDocuments(dbKey, options);
        return { success: true, docs };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-delete', async (event, { dbKey, id, user }) => {
      try {
        const result = await this.databaseHandler.deleteDocument(dbKey, id, user);
        return { success: true, id: result.id, rev: result.rev };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-bulk-save', async (event, { dbKey, docs, user }) => {
      try {
        const results = await this.databaseHandler.bulkSave(dbKey, docs, user);
        return { success: true, results };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-find', async (event, { dbKey, selector, options }) => {
      try {
        const docs = await this.databaseHandler.findDocuments(dbKey, selector, options);
        return { success: true, docs };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-all-docs', async (event, { dbKey, options }) => {
      try {
        const result = await this.databaseHandler.allDocs(dbKey, options);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Sync operations
    ipcMain.handle('db-sync-this', async (event, { dbKey, targetUrl, options }) => {
      try {
        const result = await this.databaseHandler.syncThis(dbKey, targetUrl, options);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-start', async (event, { databases }) => {
      return await this.syncService.startSyncService(databases);
    });

    ipcMain.handle('sync-stop', async () => {
      return await this.syncService.stopSyncService();
    });

    ipcMain.handle('sync-force-full', async () => {
      return await this.syncService.forceFullSync();
    });

    ipcMain.handle('sync-lan-specific', async (event, { dbKey, lanUrl }) => {
      try {
        const result = await this.syncService.performSpecificLanSync(dbKey, lanUrl);
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-status', async () => {
      return this.syncService.getSyncStatus();
    });

    ipcMain.handle('sync-add-database', async (event, { dbKey }) => {
      return await this.syncService.addDatabase(dbKey);
    });

    ipcMain.handle('sync-remove-database', async (event, { dbKey }) => {
      return await this.syncService.removeDatabase(dbKey);
    });

    ipcMain.handle('sync-restart-database', async (event, { dbKey }) => {
      try {
        // Stop and restart the database sync
        this.syncService.stopDatabaseSync(dbKey);
        this.syncService.startDatabaseSync(dbKey);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-update-lan-details', async (event, { dbKey, lan_details }) => {
      try {
        const result = await this.databaseHandler.updateLanDetails(dbKey, lan_details);

        // If LAN details were updated successfully, restart sync for this database
        if (result.success) {
          // Stop and restart the database sync with new LAN details
          this.syncService.stopDatabaseSync(dbKey);
          this.syncService.startDatabaseSync(dbKey);
          console.log(`[IPCHandlers] Sync restarted for ${dbKey} after LAN details update`);
        }

        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-trigger-instant', async (event, { dbKey, operationType }) => {
      try {
        // Trigger both LAN and remote instant sync
        await this.syncService.performInstantLanSync(dbKey);

        // Add a small delay before remote sync to prevent overwhelming
        setTimeout(() => {
          this.syncService.performInstantRemoteSync(dbKey);
        }, 500);

        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });



    ipcMain.handle('sync-trigger', async (event, { dbKey }) => {
      try {
        const result = await this.syncService.forceSyncDatabase(dbKey);
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-set-remote-url', async (event, { url }) => {
      try {
        this.syncService.setRemoteConnectionString(url);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-test-remote-connectivity', async () => {
      return { success: false, error: 'Remote connectivity test removed in simplified version' };
    });

    ipcMain.handle('sync-get-remote-url', async () => {
      return { success: true, url: 'https://therick:<EMAIL>/' };
    });

    // Database management
    ipcMain.handle('db-close', async (event, { dbKey }) => {
      return await this.databaseHandler.closeDatabase(dbKey);
    });

    ipcMain.handle('db-close-all', async () => {
      return await this.databaseHandler.closeAllDatabases();
    });

    ipcMain.handle('db-get-all-databases', async () => {
      return this.databaseHandler.getAllDatabases();
    });

    // Attachment operations
    ipcMain.handle('db-get-attachment', async (event, { dbKey, id, name }) => {
      try {
        const attachment = await this.databaseHandler.getAttachment(dbKey, id, name);
        // getAttachment returns null for not found, which is a valid response
        return { success: true, attachment };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-put-attachment', async (event, { dbKey, id, name, rev, attachment, type }) => {
      try {
        const result = await this.databaseHandler.putAttachment(dbKey, id, name, rev, attachment, type);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-remove-attachment', async (event, { dbKey, id, name, rev }) => {
      try {
        const result = await this.databaseHandler.removeAttachment(dbKey, id, name, rev);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Additional database operations
    ipcMain.handle('db-authenticate', async (event, { dbKey, username, password }) => {
      try {
        const result = await this.databaseHandler.authenticate(dbKey, username, password);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-dump', async (event, { dbKey }) => {
      return { success: false, error: 'Database dump method removed in simplified version' };
    });

    ipcMain.handle('db-create-index', async (event, { dbKey, index }) => {
      try {
        const result = await this.databaseHandler.createIndex(dbKey, index);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-info', async (event, { dbKey }) => {
      return { success: false, error: 'Database info method removed in simplified version' };
    });

    ipcMain.handle('db-compact', async (event, { dbKey }) => {
      return { success: false, error: 'Database compact method removed in simplified version' };
    });

    ipcMain.handle('db-sync-stats', async (event, { dbKey }) => {
      try {
        const stats = await this.databaseHandler.getSyncStats(dbKey);
        return { success: true, stats };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-force-sync', async (event, { dbKey }) => {
      try {
        const result = await this.databaseHandler.forceSync(dbKey);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Logging operations
    ipcMain.handle('db-add-log', async (event, { databasePrefix, logData }) => {
      try {
        const result = await this.databaseHandler.addLog(databasePrefix, logData);
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Real-time updates - forward database changes to renderer
    this.databaseHandler.on('documentChanged', (data) => {
      // Broadcast to all renderer processes
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('db-document-changed', data);
      });
    });

    // Forward sync events to renderer
    this.syncService.on('syncComplete', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('sync-complete', data);
      });
    });

    this.syncService.on('syncError', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('sync-error', data);
      });
    });

    this.syncService.on('syncProgress', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('sync-progress', data);
      });
    });

    console.log('✅ Database IPC Handlers initialized successfully');
  }

  // Cleanup method
  async removeAllHandlers() {
    const handlers = [
      'get-app-version',
      'get-system-info',
      'get-memory-usage',
      'minimize-window',
      'maximize-window',
      'close-window',
      'send-email',
      'select-file',
      'save-file',
      'get-performance-metrics',
      'open-external',
      'restart-app',
      'clear-cache',
      // Database handlers
      'db-initialize',
      'db-save',
      'db-get',
      'db-get-all',
      'db-all-docs',
      'db-delete',
      'db-bulk-save',
      'db-find',
      'db-sync-this',
      'db-get-attachment',
      'db-put-attachment',
      'db-remove-attachment',
      'db-authenticate',
      'db-dump',
      'db-create-index',
      'db-info',
      'db-compact',
      'db-sync-stats',
      'db-force-sync',
      'sync-start',
      'sync-stop',
      'sync-force-full',
      'sync-lan-specific',
      'sync-status',
      'sync-add-database',
      'sync-remove-database',
      'sync-restart-database',
      'db-update-lan-details',
      'sync-trigger-instant',
      'sync-trigger',
      'sync-set-remote-url',
      'sync-test-remote-connectivity',
      'sync-get-remote-url',
      'db-close',
      'db-close-all',
      'db-get-all-databases',
      'db-add-log'
    ];

    handlers.forEach(handler => {
      ipcMain.removeHandler(handler);
    });

    // Cleanup database services
    if (this.syncService) {
      await this.syncService.stopSyncService();
    }
    if (this.databaseHandler) {
      await this.databaseHandler.closeAllDatabases();
    }
  }
}

module.exports = IPCHandlers;
