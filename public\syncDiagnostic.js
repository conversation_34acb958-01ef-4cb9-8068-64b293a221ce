/**
 * Electron PouchDB Sync Diagnostic Tool
 * Run this in browser console to diagnose sync issues
 */

window.syncDiagnostic = {

  /**
   * Check if we're in Electron environment
   */
  checkElectronEnvironment() {
    const hasElectron = !!(window.require && window.require('electron'));
    console.log('🔍 Electron Environment:', hasElectron ? '✅ Available' : '❌ Not Available');
    return hasElectron;
  },

  /**
   * Get IPC renderer if available
   */
  getIpcRenderer() {
    try {
      const { ipcRenderer } = window.require('electron');
      return ipcRenderer;
    } catch (error) {
      console.error('❌ Failed to get ipcRenderer:', error);
      return null;
    }
  },

  /**
   * Check sync service status
   */
  async checkSyncStatus() {
    console.log('\n🔍 Checking Sync Service Status...');
    const ipcRenderer = this.getIpcRenderer();
    if (!ipcRenderer) return;

    try {
      const status = await ipcRenderer.invoke('sync-status');
      console.log('📊 Sync Status:', status);
      return status;
    } catch (error) {
      console.error('❌ Failed to get sync status:', error);
    }
  },

  /**
   * Check database configuration
   */
  async checkDatabaseConfig() {
    console.log('\n🔍 Checking Database Configuration...');
    const ipcRenderer = this.getIpcRenderer();
    if (!ipcRenderer) return;

    try {
      const databases = await ipcRenderer.invoke('db-get-all-databases');
      console.log('📋 Initialized Databases:', databases);

      // Check LAN details in localStorage
      const lanDetails = localStorage.getItem('lan_details');
      console.log('🌐 LAN Details in localStorage:', lanDetails ? '✅ Present' : '❌ Missing');
      if (lanDetails) {
        try {
          const parsed = JSON.parse(lanDetails);
          console.log('🌐 LAN Configuration:', {
            host: parsed.host,
            port: parsed.port,
            username: parsed.username,
            hasPassword: !!parsed.password
          });
        } catch (e) {
          console.error('❌ Invalid LAN details JSON:', e);
        }
      }

      return { databases, lanDetails };
    } catch (error) {
      console.error('❌ Failed to check database config:', error);
    }
  },

  /**
   * Test instant sync trigger
   */
  async testInstantSync(dbKey = null) {
    console.log('\n🔍 Testing Instant Sync...');
    const ipcRenderer = this.getIpcRenderer();
    if (!ipcRenderer) return;

    // Use first available database if none specified
    if (!dbKey) {
      try {
        const databases = await ipcRenderer.invoke('db-get-all-databases');
        dbKey = databases[0];
      } catch (error) {
        console.error('❌ Failed to get databases:', error);
        return;
      }
    }

    if (!dbKey) {
      console.error('❌ No database available for testing');
      return;
    }

    try {
      console.log(`🚀 Triggering instant sync for: ${dbKey}`);
      const result = await ipcRenderer.invoke('sync-trigger-instant', {
        dbKey,
        operationType: 'test'
      });
      console.log('📊 Instant Sync Result:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to trigger instant sync:', error);
    }
  },

  /**
   * Test force full sync
   */
  async testForceSync() {
    console.log('\n🔍 Testing Force Full Sync...');
    const ipcRenderer = this.getIpcRenderer();
    if (!ipcRenderer) return;

    try {
      console.log('🚀 Triggering force full sync...');
      const result = await ipcRenderer.invoke('sync-force-full');
      console.log('📊 Force Sync Result:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to trigger force sync:', error);
    }
  },

  /**
   * Test remote connectivity
   */
  async testRemoteConnectivity() {
    console.log('\n🔍 Testing Remote Connectivity...');
    const ipcRenderer = this.getIpcRenderer();
    if (!ipcRenderer) return;

    try {
      console.log('🌐 Testing remote server connection...');
      const result = await ipcRenderer.invoke('sync-test-remote-connectivity');
      console.log('📊 Remote Connectivity Result:', result);

      if (result.success) {
        console.log(`🌐 Remote Server: ${result.reachable ? '✅ Reachable' : '❌ Not Reachable'}`);
      }

      return result;
    } catch (error) {
      console.error('❌ Failed to test remote connectivity:', error);
    }
  },

  /**
   * Get current remote URL
   */
  async getRemoteUrl() {
    console.log('\n🔍 Getting Remote URL...');
    const ipcRenderer = this.getIpcRenderer();
    if (!ipcRenderer) return;

    try {
      const result = await ipcRenderer.invoke('sync-get-remote-url');
      console.log('📊 Remote URL:', result.success ? result.url : 'Not configured');
      return result;
    } catch (error) {
      console.error('❌ Failed to get remote URL:', error);
    }
  },

  /**
   * Check network connectivity to LAN server
   */
  async testLanConnectivity() {
    console.log('\n🔍 Testing LAN Connectivity...');

    const lanDetails = localStorage.getItem('lan_details');
    if (!lanDetails) {
      console.error('❌ No LAN details found in localStorage');
      return false;
    }

    try {
      const config = JSON.parse(lanDetails);
      const testUrl = `http://${config.host}:${config.port}/`;

      console.log(`🌐 Testing connection to: ${testUrl}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(testUrl, {
        method: 'HEAD',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const isReachable = response.status !== 0;
      console.log(`📊 LAN Server Status: ${isReachable ? '✅ Reachable' : '❌ Not Reachable'} (Status: ${response.status})`);
      return isReachable;

    } catch (error) {
      console.error('❌ LAN connectivity test failed:', error.message);
      return false;
    }
  },

  /**
   * Run comprehensive diagnostic
   */
  async runFullDiagnostic() {
    console.log('🔧 Starting Electron PouchDB Sync Diagnostic...\n');

    const results = {
      electron: this.checkElectronEnvironment(),
      syncStatus: null,
      databaseConfig: null,
      lanConnectivity: null,
      instantSync: null,
      forceSync: null
    };

    if (!results.electron) {
      console.log('❌ Cannot continue - not in Electron environment');
      return results;
    }

    results.syncStatus = await this.checkSyncStatus();
    results.databaseConfig = await this.checkDatabaseConfig();
    results.lanConnectivity = await this.testLanConnectivity();
    results.remoteConnectivity = await this.testRemoteConnectivity();
    results.remoteUrl = await this.getRemoteUrl();
    results.instantSync = await this.testInstantSync();
    results.forceSync = await this.testForceSync();

    console.log('\n📋 Diagnostic Summary:');
    console.log('  🔧 Electron Environment:', results.electron ? '✅' : '❌');
    console.log('  🔄 Sync Service:', results.syncStatus ? '✅' : '❌');
    console.log('  📊 Database Config:', results.databaseConfig ? '✅' : '❌');
    console.log('  🌐 LAN Connectivity:', results.lanConnectivity ? '✅' : '❌');
    console.log('  🌍 Remote Connectivity:', results.remoteConnectivity?.reachable ? '✅' : '❌');
    console.log('  🔗 Remote URL:', results.remoteUrl?.url ? '✅' : '❌');
    console.log('  ⚡ Instant Sync:', results.instantSync?.success ? '✅' : '❌');
    console.log('  🚀 Force Sync:', results.forceSync?.success ? '✅' : '❌');

    return results;
  },

  /**
   * Quick sync test with a test document
   */
  async quickSyncTest() {
    console.log('\n🔍 Running Quick Sync Test...');

    try {
      // Get database instance
      const DB = window.DB || window.pouchDatabase;
      if (!DB) {
        console.error('❌ Database function not available');
        return;
      }

      const testDb = DB('test_sync', localStorage.getItem('DATABASE_PREFIX') || '');

      // Create test document
      const testDoc = {
        _id: 'sync_test_' + Date.now(),
        name: 'Sync Test Document',
        timestamp: new Date().toISOString(),
        test: true
      };

      console.log('📝 Creating test document...');
      const result = await testDb.save(testDoc);
      console.log('✅ Test document created:', result);

      // Wait a moment for sync to trigger
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('🔄 Sync should have been triggered automatically');
      return result;

    } catch (error) {
      console.error('❌ Quick sync test failed:', error);
    }
  }
};

// Auto-run diagnostic if in Electron environment
if (window.require && window.require('electron')) {
  console.log('🔧 Electron PouchDB Sync Diagnostic Tool Loaded');
  console.log('📋 Available Commands:');
  console.log('  • syncDiagnostic.runFullDiagnostic() - Complete diagnostic');
  console.log('  • syncDiagnostic.checkSyncStatus() - Check sync service');
  console.log('  • syncDiagnostic.testLanConnectivity() - Test LAN connection');
  console.log('  • syncDiagnostic.testRemoteConnectivity() - Test remote connection');
  console.log('  • syncDiagnostic.getRemoteUrl() - Get current remote URL');
  console.log('  • syncDiagnostic.testInstantSync() - Test instant sync');
  console.log('  • syncDiagnostic.quickSyncTest() - Create test document and sync');
  console.log('\n💡 Run: syncDiagnostic.runFullDiagnostic()');
}
