import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Layout,
  Typography,
  Tag,
  Button,
  Flex,
  FloatButton,
  Card,
  Row,
  Col,
  Statistic,
  Space,
} from "antd";
import {
  DollarOutlined,
  PrinterOutlined,
  ShopOutlined,
  CalendarOutlined,
  FileTextOutlined,
  BankOutlined,
  CarOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PrintComponents from "react-print-components";
import RenderBlob from "../../../../Components/RenderBlob";
import { numberFormat } from "../../../../Utils/functions";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const Expense = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [supplier, setSupplier] = useState(null);
  const [job, setJob] = useState(null);
  const [company, setCompany] = useState(null);
  const [category, setCategory] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!pouchDatabase || databasePrefix === undefined) {
          console.error("pouchDatabase or databasePrefix not available");
          setLoading(false);
          return;
        }

        // Fetch supplier data
        if (data.supplier && data.supplier.value) {
          try {
            const supplierData = await pouchDatabase("suppliers", databasePrefix).getDocument(data.supplier.value);
            setSupplier(supplierData);
          } catch (error) {
            console.warn("Error fetching supplier:", error);
          }
        }

        // Fetch job data
        if (data.job && data.job.value) {
          try {
            const jobData = await pouchDatabase("jobs", databasePrefix).getDocument(data.job.value);
            setJob(jobData);
          } catch (error) {
            console.warn("Error fetching job:", error);
          }
        }

        // Fetch expense category data
        if (data["expense-category"] && data["expense-category"].value) {
          try {
            const categoryData = await pouchDatabase("expense_categories", databasePrefix).getDocument(data["expense-category"].value);
            setCategory(categoryData);
          } catch (error) {
            console.warn("Error fetching category:", error);
          }
        }

        // Fetch company data (organizations)
        try {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            setCompany(organizationsData[0]);
          }
        } catch (error) {
          console.warn("Error fetching company data:", error);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Get payment method tag color
  const getPaymentMethodColor = (method) => {
    switch (method) {
      case "Cash":
        return "green";
      case "Credit Card":
        return "blue";
      case "Cheque":
        return "purple";
      case "MTN Mobile Money":
      case "Airtel Mobile Money":
        return "orange";
      case "Bank Deposit":
        return "cyan";
      default:
        return "default";
    }
  };

  // Printable expense component
  const PrintableExpense = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>EXPENSE VOUCHER</Title>
            <Text>Voucher ID: {data._id}</Text>
            <br />
            <Text>Date: {moment(data.date).format("DD MMM YYYY")}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Expense Details</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Date">
          {moment(data.date).format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Amount">
          {numberFormat(data.amount)}
        </Descriptions.Item>
        <Descriptions.Item label="Supplier">
          {supplier ? supplier.name : data.supplier?.label || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Payment Method">
          {data.method_of_payment || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Category" span={2}>
          {category ? category.name : data["expense-category"]?.label || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Description" span={2}>
          {data.description || "No description provided"}
        </Descriptions.Item>
      </Descriptions>

      {job && (
        <>
          <Divider />
          <Title level={4}>Related Job</Title>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Job ID">{job._id}</Descriptions.Item>
            <Descriptions.Item label="Customer">
              {job.customer ? job.customer.label : ""}
            </Descriptions.Item>
            <Descriptions.Item label="Vehicle">
              {job.vehicle ? job.vehicle.label : ""}
            </Descriptions.Item>
          </Descriptions>
        </>
      )}

      <Divider />

      <div style={{ marginTop: "50px", display: "flex", justifyContent: "space-between" }}>
        <div>
          <p>Prepared By: _________________________</p>
        </div>
        <div>
          <p>Approved By: _________________________</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading expense information...</div>;
  }

  return (
    <>
      <PageHeader
        backIcon={<DollarOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title="Expense Voucher"
        subTitle={`#${data._id}`}
        tags={<Tag color={getPaymentMethodColor(data.method_of_payment)}>{data.method_of_payment}</Tag>}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Voucher
              </Button>
            }
          >
            <PrintableExpense />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <Card className="expense-card">
              <Statistic
                title="Amount"
                value={data.amount}
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                prefix={<DollarOutlined />}
                suffix=""
              />
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="expense-card">
              <Statistic
                title="Date"
                value={moment(data.date).format("DD MMM YYYY")}
                valueStyle={{ color: '#1890ff' }}
                prefix={<CalendarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="expense-card">
              <Statistic
                title="Payment Method"
                value={data.method_of_payment}
                valueStyle={{ color: '#722ed1' }}
                prefix={<BankOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24} lg={12}>
            <Card
              title={<><ShopOutlined /> Supplier Information</>}
              className="expense-card"
            >
              {supplier ? (
                <Descriptions column={1} bordered>
                  <Descriptions.Item label="Name">{supplier.name}</Descriptions.Item>
                  {supplier.phone && (
                    <Descriptions.Item label="Phone">{supplier.phone}</Descriptions.Item>
                  )}
                  {supplier.email && (
                    <Descriptions.Item label="Email">{supplier.email}</Descriptions.Item>
                  )}
                  {supplier.address && (
                    <Descriptions.Item label="Address">{supplier.address}</Descriptions.Item>
                  )}
                </Descriptions>
              ) : (
                <Text type="secondary">
                  {data.supplier?.label ? `Supplier: ${data.supplier.label}` : "No supplier information available"}
                </Text>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              title={<><FileTextOutlined /> Expense Details</>}
              className="expense-card"
            >
              <Descriptions column={1} bordered>
                <Descriptions.Item label="Category">
                  {category ? category.name : data["expense-category"]?.label || "N/A"}
                </Descriptions.Item>
                <Descriptions.Item label="Description">
                  {data.description || "No description provided"}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {job && (
          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            <Col xs={24}>
              <Card
                title={<><CarOutlined /> Related Job</>}
                className="expense-card"
              >
                <Descriptions column={{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }} bordered>
                  <Descriptions.Item label="Job ID">{job._id}</Descriptions.Item>
                  <Descriptions.Item label="Customer">
                    {job.customer ? job.customer.label : ""}
                  </Descriptions.Item>
                  <Descriptions.Item label="Vehicle">
                    {job.vehicle ? job.vehicle.label : ""}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>
        )}
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Voucher"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableExpense />
        </PrintComponents>
      </div>
    </>
  );
};

export default Expense;
