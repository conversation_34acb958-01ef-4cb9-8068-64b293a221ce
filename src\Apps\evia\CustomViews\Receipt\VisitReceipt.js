import PouchDb from "pouchdb-browser";
import React, { useEffect, useState } from "react";
import ReceiptTemplate from "../../../Universal/CustomViews/ReceiptTemplate";
import moment from "moment";

const VisitReceipt = ({ data }) => {
  const [guest, setGuest] = useState(null);
  const [invoice, setInvoice] = useState(null);
  const [receipt, setReceipt] = useState(null);

  useEffect(() => {
    if (data.checkin && invoice && invoice.guest) {
      const guestDB = new PouchDb("guests");
      guestDB.get(invoice.guest.value)
        .then((guestData) => {
          setGuest(guestData);
        })
        .catch((error) => {
          console.error("Error fetching guest:", error);
          setGuest(null);
        });
    } else {
      setGuest(null);
    }
  }, [data.checkin, invoice?.guest?.value]); // Added proper dependencies

  useEffect(() => {
    if (data.checkin) {
      const invoiceDB = new PouchDb("checkins");
      invoiceDB.get(data.checkin._id)
        .then((invoiceData) => {
          setInvoice(invoiceData);
        })
        .catch((error) => {
          console.error("Error fetching invoice:", error);
          setInvoice(null);
        });
    } else {
      setInvoice(null);
    }
  }, [data.checkin?._id]); // Added proper dependencies

  useEffect(() => {
    if (data.checkin) {
      const receiptDB = new PouchDb("receipts");
      receiptDB.allDocs({ include_docs: true })
        .then((res) => {
          const filteredData = res.rows.filter(
            (row) =>
              row &&
              row.doc &&
              row.doc.checkin &&
              row.doc.checkin._id === data.checkin._id &&
              row.doc.date &&
              moment(row.doc.date).isSameOrBefore(moment(data.date))
          );
          setReceipt(filteredData);
        })
        .catch((error) => {
          console.error("Error fetching receipts:", error);
          setReceipt(null);
        });
    } else {
      setReceipt(null);
    }
  }, [data.checkin?._id, data.date]); // Added proper dependencies

  

  const newdata = {
    ...data,
    date: data.date,
    client: { title: "Guest", name: guest },
    paid:
      receipt && receipt.length > 0
        ? receipt.reduce((previous, current) => {
            return previous + parseInt(current.doc.amount);
          }, 0)
        : 0,
    operator: { title: "Cashier", name: data.entrant.label },
    invoice: {
      ...invoice,
      items: invoice
        ? [
            {
              name: `Accommodation in ${invoice.room_number.label}`,
              price: invoice.room_rate,
              quantity: moment(invoice.departure_date)
                .startOf("day")
                .diff(moment(invoice.arrival_date).startOf("day"), "days"),
            },
            ...((invoice.extensions && [
              {
                name: "Visit Extension",
                price: invoice.room_rate,
                quantity: invoice.extensions / invoice.room_rate,
              },
            ]) ||
              []),
            ...((invoice.items &&
              invoice.items.map((item) => {
                return {
                  name: item.item,
                  price: item.cost,
                  quantity: item.quantity,
                };
              })) ||
              []),
          ]
        : [],
    },
  };
  return (
    <div>
      <ReceiptTemplate data={newdata} documentTitle={" Receipt"} />
    </div>
  );
};

export default VisitReceipt;
