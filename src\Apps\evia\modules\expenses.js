const moment = require("moment");
exports.expenses = {
  name: "Expenses",
  icon: "KeyOutlined",
  path: "/expenditure/expenses",
  parent: "expenditure",
  collection: "expenses",
  singular: "Expense",
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      isPrintable: true,
      noBackDate: true,
      isRequired: true,
      initialValue: moment().startOf("day"), // Use moment.js to get the current date
    },
    {
      dataIndex: "supplier",
      title: "Supplier",
      type: "dbSelect",
      valueType: "select",
      collection: "suppliers",
      isRequired: true,
      isPrintable: true,
      label: ["name"],
    },
    {
      dataIndex: "expense-category",
      title: "Expense Category",
      type: "dbSelect",
      valueType: "select",
      collection: "expense_categories",
      isPrintable: true,
      isRequired: true,
      label: ["name"],
    },
    {
      title: "Method Of Payment",
      dataIndex: "method_of_payment",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isPrintable: true,
      isRequired: true,
      width: "lg",
      colProps: {
        md: 12,
      },
      valueEnum: {
        Cash: {
          text: "Cash",
        },
        "Credit Card": {
          text: "Credit Card",
        },
        Cheque: {
          text: "Cheque",
        },
        "Mobile digit": {
          text: "Mobile digit",
        },
      },
    },
    {
      valueType: "textarea",
      isPrintable: true,
      dataIndex: "description",
      title: "Description",
    },
    {
      valueType: "digit",
      isRequired: true,
      isPrintable: true,
      dataIndex: "amount",
      title: "Amount",
    },
  ],
};
