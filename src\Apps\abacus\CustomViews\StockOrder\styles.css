.stock-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.stock-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.stock-card .ant-card-body {
  padding: 16px;
}

/* Print styles */
.print-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.print-header {
  text-align: center;
  margin-bottom: 20px;
}

.print-header h1 {
  margin-bottom: 5px;
}

.print-supplier {
  margin-bottom: 20px;
}

.print-supplier h2 {
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
  margin-bottom: 10px;
}

.print-items {
  margin-bottom: 20px;
}

.print-items h2 {
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
  margin-bottom: 10px;
}

.print-table {
  width: 100%;
  border-collapse: collapse;
}

.print-table th, .print-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.print-table th {
  background-color: #f2f2f2;
}

.print-notes {
  margin-top: 20px;
}

.print-notes h2 {
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
  margin-bottom: 10px;
}
