import React, { useEffect, useState } from "react";
import {
  But<PERSON>,
  Space,
  Table,
  Tag,
  Typography,
  DatePicker,
  Row,
  Col,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import { PrinterOutlined } from "@ant-design/icons";
import "./print.css";
import DocumentHead from "./DocumentHead";
import { numberFormat } from "../../../../Utils/functions";

const Arrival = (props) => {
  // const [dateRange, setDateRange] = useState(null);
  const [dateRange, setDateRange] = useState([Date(), Date()]);

  const { checkIns, guests, rooms, company } = props;

  const columns = [
    {
      title: "Arrival",
      dataIndex: "arrival_date",
      key: "arrival_date",
      render: (text) => moment(text).format("DD MMM YY - HH:mm"),
    },
    {
      title: "Departure",
      dataIndex: "departure_date",
      key: "departure_date",
      render: (text, r) =>
        moment(r.departure_date).add(r.extensions, "days").format("DD MMM YY"),
    },
    {
      title: "Room no",
      key: "room_number",
      dataIndex: "room_number",
      render: (text) =>
        text && text.label && text.label.split(" ").reverse().pop(),
    },
    {
      title: "Guest Name",
      dataIndex: "guest",
      key: "guest",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Checkin ID",
      dataIndex: "_id",
      key: "_id",
      render: (text, record) =>
        record.ref_number ? `${text} - ${record.ref_number}` : text,
    },
    {
      title: "Checked In By",
      dataIndex: "entrant",
      key: "entrant",
      render: (text) => text && text.label && text.label,
    },
    {
      title: "Rate",
      dataIndex: "room_rate",
      key: "room_rate",
      render: (text) => text && numberFormat(text),
    },
    {
      title: "Nights",
      dataIndex: "nights",
      key: "nights",
      render: (t, record) =>
        moment(
          record.extensions
            ? record.extensions.departure_date
            : record.departure_date
        )
          .startOf("day")
          .diff(moment(record.arrival_date).startOf("day"), "days"),
    },
    {
      title: "Amount",
      dataIndex: "company",
      key: "company",
      render: (text, record) =>
        numberFormat(
          record.check_in_total
        ),
    },
  ];

  return (
    <div>
      <Table
        size="small"
        columns={columns}
        dataSource={
          dateRange
            ? checkIns.filter(
                (r) =>
                  moment(r.arrival_date).isSameOrAfter(
                    moment(dateRange[0].$d),
                    "day"
                  ) &&
                  moment(r.arrival_date).isSameOrBefore(
                    moment(dateRange[1].$d),
                    "day"
                  )
              )
            : checkIns.reverse().slice(0, 10).reverse()
        }
        title={() => (
          <Row justify="space-between">
            <Col span={12}>
              <DatePicker.RangePicker
                onChange={(dates) => {
                  
                  setDateRange(dates);
                }}
              />
            </Col>
            <Col span={12} style={{ textAlign: "end" }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                    style={{ marginBottom: 16 }}
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>
                    Guest Arrival Register
                  </Typography.Title>
                  <Table
                    size="small"
                    className="custom-table"
                    columns={columns}
                    dataSource={
                      dateRange
                        ? checkIns.filter(
                            (r) =>
                              moment(r.arrival_date).isSameOrAfter(
                                moment(dateRange[0].$d),
                                "day"
                              ) &&
                              moment(r.arrival_date).isSameOrBefore(
                                moment(dateRange[1].$d),
                                "day"
                              )
                          )
                        : checkIns.reverse().slice(0, 10).reverse()
                    }
                    pagination={false}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Arrival;
