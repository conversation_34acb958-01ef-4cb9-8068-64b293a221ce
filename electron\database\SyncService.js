/**
 * Enhanced Sync Service for Electron Main Process
 * Coordinated database synchronization with live sync, retries, and no filtering
 *
 * FEATURES:
 * - Coordinated sync: LAN first, then Remote for consistency
 * - Live sync enabled for real-time updates
 * - Retry mechanism with exponential backoff
 * - No document size limits for both LAN and Remote
 * - No branch filtering - all documents sync regardless of branch
 * - Unified configuration for consistent behavior between LAN and Remote
 * - Sync verification and consistency checking
 * - Design documents (_design/*) are excluded from sync to prevent conflicts
 * - Frontend databases handle design documents for querying and views
 */

const { EventEmitter } = require('events');

// Remote database connection string
const REMOTE_DATABASE_CONNECTION_STRING = "https://therick:<EMAIL>/";

class SyncService extends EventEmitter {
  constructor(databaseHandler) {
    super();
    this.databaseHandler = databaseHandler;
    this.activeSyncs = new Set();
    this.syncIntervals = new Map(); // Track intervals for cleanup
    this.syncTimeouts = new Map(); // For debouncing sync triggers
    this.syncConnections = new Map(); // Connection caching
    this.connectionTimeouts = new Map(); // Auto-cleanup timers
    this.performanceMetrics = new Map(); // Simple performance tracking
    this.recentActivity = new Map(); // Track activity for adaptive intervals
    this.isRunning = false;

    // Optimized sync settings to reduce conflicts
    this.settings = {
      lanSyncInterval: 15000,   // 15 seconds for LAN sync (reduced frequency)
      remoteSyncInterval: 30000, // 30 seconds for remote sync (reduced frequency)
      syncTimeout: 60000,       // 60 second timeout for reliability
      batchSize: 25,            // Smaller batch size to reduce conflicts
      maxDocumentSize: {
        lan: null,              // No size limits for LAN
        remote: null            // No size limits for remote
      },
      retrySettings: {
        enabled: true,
        maxRetries: 3,
        backoffMultiplier: 2,
        initialDelay: 2000      // 2 second initial delay
      },
      liveSync: false           // Disable live sync to reduce conflicts
    };

    // Listen for sync triggers from DatabaseHandler
    this.databaseHandler.on('syncTrigger', (data) => {
      this.handleSyncTrigger(data);
    });
  }

  /**
   * Start the background sync service
   */
  async startSyncService(databases) {
    if (!this.isRunning) {
      this.isRunning = true;
      // console.log('[SyncService] Starting sync service for the first time');
    } else {
      console.log('[SyncService] Sync service already running, adding new databases');
    }

    // Start simple sync intervals for each database
    for (const dbKey of databases) {
      this.startDatabaseSync(dbKey);
    }

    console.log(`[SyncService] Sync service managing ${databases.length} databases`);
    return { success: true };
  }



  /**
   * Get document size limit based on sync type (now returns null for no limits)
   */
  getDocumentSizeLimit(syncType) {
    if (syncType === 'lan' || syncType === 'lan_specific') {
      return this.settings.maxDocumentSize.lan;
    } else if (syncType === 'remote') {
      return this.settings.maxDocumentSize.remote;
    }
    // No limits by default
    return null;
  }

  /**
   * Create document filter for sync operations (no size limits, no branch filtering)
   */
  createDocumentFilter(syncType) {
    const syncTypeLabel = syncType.toUpperCase();

    return (doc) => {
      // Skip design documents only
      if (doc._id.startsWith('_design/')) {
        console.log(`[SyncService] Skipping design document for ${syncTypeLabel} sync: ${doc._id}`);
        return false;
      }

      // Log document sync for debugging
      // console.log(`[SyncService] Syncing document for ${syncTypeLabel}: ${doc._id}`);
      return true;
    };
  }

  /**
   * Start sync intervals for a specific database
   * With live sync enabled, intervals serve as fallback/heartbeat
   */
  startDatabaseSync(dbKey) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) {
      console.warn(`[SyncService] Database ${dbKey} not found, cannot start sync`);
      return;
    }

    // Clear existing intervals for this database
    this.stopDatabaseSync(dbKey);

    const intervals = {};

    if (this.settings.liveSync) {
      // With live sync, start continuous sync connections
      console.log(`[SyncService] Starting live sync for ${dbKey}`);
      this.startLiveSync(dbKey);

      // Keep heartbeat intervals for connection health monitoring
      if (dbConfig.lanString) {
        intervals.lanHeartbeat = setInterval(() => {
          this.checkLanSyncHealth(dbKey);
        }, this.settings.lanSyncInterval);
      }

      intervals.remoteHeartbeat = setInterval(() => {
        this.checkRemoteSyncHealth(dbKey);
      }, this.settings.remoteSyncInterval);
    } else {
      // Fallback to interval-based sync
      if (dbConfig.lanString) {
        console.log(`[SyncService] Starting adaptive interval LAN sync for ${dbKey} (${dbConfig.lanString})`);
        intervals.lanSync = this.createAdaptiveInterval(dbKey, 'lan');
      } else {
        console.log(`[SyncService] No LAN configuration for ${dbKey}, skipping LAN sync`);
      }

      console.log(`[SyncService] Starting adaptive interval remote sync for ${dbKey}`);
      intervals.remoteSync = this.createAdaptiveInterval(dbKey, 'remote');
    }

    // Store intervals for cleanup
    this.syncIntervals.set(dbKey, intervals);
  }

  /**
   * Create adaptive sync interval that adjusts based on activity
   */
  createAdaptiveInterval(dbKey, syncType) {
    let currentInterval = syncType === 'lan' ? this.settings.lanSyncInterval : this.settings.remoteSyncInterval;

    const adaptiveSync = () => {
      const activity = this.getRecentActivity(dbKey);

      // High activity: sync more frequently (minimum 2 seconds)
      if (activity.changesPerMinute > 10) {
        currentInterval = Math.max(2000, currentInterval * 0.8);
      }
      // Medium activity: normal intervals
      else if (activity.changesPerMinute > 2) {
        currentInterval = syncType === 'lan' ? this.settings.lanSyncInterval : this.settings.remoteSyncInterval;
      }
      // Low activity: sync less frequently (maximum 30 seconds)
      else if (activity.changesPerMinute < 1) {
        currentInterval = Math.min(30000, currentInterval * 1.2);
      }

      // Perform the appropriate sync
      if (syncType === 'lan') {
        this.performLanSync(dbKey);
      } else {
        this.performRemoteSync(dbKey);
      }

      // Schedule next sync with updated interval
      return setTimeout(adaptiveSync, currentInterval);
    };

    // Start the adaptive sync
    return setTimeout(adaptiveSync, currentInterval);
  }

  /**
   * Stop sync intervals for a specific database
   */
  stopDatabaseSync(dbKey) {
    const intervals = this.syncIntervals.get(dbKey);
    if (intervals) {
      // Clear traditional sync intervals (now adaptive timeouts)
      if (intervals.lanSync) clearTimeout(intervals.lanSync);
      if (intervals.remoteSync) clearTimeout(intervals.remoteSync);

      // Clear heartbeat intervals for live sync
      if (intervals.lanHeartbeat) clearTimeout(intervals.lanHeartbeat);
      if (intervals.remoteHeartbeat) clearTimeout(intervals.remoteHeartbeat);

      this.syncIntervals.delete(dbKey);
    }

    // Clear any pending sync timeouts (debouncing)
    if (this.syncTimeouts.has(dbKey)) {
      clearTimeout(this.syncTimeouts.get(dbKey));
      this.syncTimeouts.delete(dbKey);
    }

    // Stop live sync connections
    this.stopLiveSync(dbKey);
  }

  /**
   * Start live sync connections for a database
   */
  startLiveSync(dbKey) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) return;

    // Start live LAN sync if available
    if (dbConfig.lanString) {
      this.startLiveLanSync(dbKey);
    }

    // Start live remote sync
    this.startLiveRemoteSync(dbKey);
  }

  /**
   * Get or create a cached sync connection
   */
  getSyncConnection(dbKey, syncType) {
    const connectionKey = `${dbKey}_${syncType}`;

    if (!this.syncConnections.has(connectionKey)) {
      console.log(`[SyncService] Creating new sync connection for ${connectionKey}`);
      const connection = this.createSyncConnection(dbKey, syncType);
      this.syncConnections.set(connectionKey, connection);

      // Auto-cleanup after 5 minutes of inactivity
      this.connectionTimeouts.set(connectionKey,
        setTimeout(() => this.cleanupConnection(connectionKey), 300000)
      );
    } else {
      // Reset cleanup timer on reuse
      if (this.connectionTimeouts.has(connectionKey)) {
        clearTimeout(this.connectionTimeouts.get(connectionKey));
        this.connectionTimeouts.set(connectionKey,
          setTimeout(() => this.cleanupConnection(connectionKey), 300000)
        );
      }
    }

    return this.syncConnections.get(connectionKey);
  }

  /**
   * Create a new sync connection
   */
  createSyncConnection(dbKey, syncType) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) return null;

    let targetUrl;
    if (syncType === 'lan') {
      if (!dbConfig.lanString) return null;
      targetUrl = dbConfig.lanString + dbConfig.databasePrefix + dbConfig.name;
    } else if (syncType === 'remote') {
      const remoteUrl = this.remoteConnectionString || REMOTE_DATABASE_CONNECTION_STRING;
      if (!remoteUrl) return null;
      targetUrl = remoteUrl + dbConfig.databasePrefix + dbConfig.name;
    }

    return {
      dbKey,
      syncType,
      targetUrl,
      localDb: dbConfig.db,
      createdAt: Date.now(),
      lastUsed: Date.now()
    };
  }

  /**
   * Cleanup inactive connection
   */
  cleanupConnection(connectionKey) {
    console.log(`[SyncService] Cleaning up inactive connection: ${connectionKey}`);
    this.syncConnections.delete(connectionKey);
    this.connectionTimeouts.delete(connectionKey);
  }

  /**
   * Stop live sync connections for a database
   */
  stopLiveSync(dbKey) {
    // Clean up connections for this database
    const connectionsToRemove = [];
    for (const [connectionKey] of this.syncConnections) {
      if (connectionKey.startsWith(dbKey + '_')) {
        connectionsToRemove.push(connectionKey);
      }
    }

    connectionsToRemove.forEach(connectionKey => {
      this.cleanupConnection(connectionKey);
    });
  }

  /**
   * Start live LAN sync connection
   */
  async startLiveLanSync(dbKey) {
    // This will be called to establish persistent LAN sync
    await this.performLanSync(dbKey);
  }

  /**
   * Start live remote sync connection
   */
  async startLiveRemoteSync(dbKey) {
    // This will be called to establish persistent remote sync
    await this.performRemoteSync(dbKey);
  }

  /**
   * Check LAN sync connection health
   */
  checkLanSyncHealth(dbKey) {
    // Heartbeat check for LAN sync - restart if needed
    console.log(`[SyncService] LAN sync heartbeat check for ${dbKey}`);
  }

  /**
   * Check remote sync connection health
   */
  checkRemoteSyncHealth(dbKey) {
    // Heartbeat check for remote sync - restart if needed
    console.log(`[SyncService] Remote sync heartbeat check for ${dbKey}`);
  }



  /**
   * Handle sync triggers from database operations with debouncing and coordination
   */
  async handleSyncTrigger({ dbKey, operation }) {
    if (['create_update', 'delete', 'bulk_create', 'force_sync'].includes(operation)) {
      console.log(`[SyncService] Debounced sync triggered for ${dbKey} (${operation})`);

      // Track activity for adaptive intervals
      this.trackActivity(dbKey, operation);

      // Clear existing timeout for this database (debouncing)
      if (this.syncTimeouts.has(dbKey)) {
        clearTimeout(this.syncTimeouts.get(dbKey));
      }

      // Debounce: wait 200ms for more changes to batch them together
      this.syncTimeouts.set(dbKey, setTimeout(async () => {
        console.log(`[SyncService] Executing debounced sync for ${dbKey}`);
        await this.performCoordinatedSync(dbKey, operation);
        this.syncTimeouts.delete(dbKey);
      }, 200));
    }
  }

  /**
   * Track activity for adaptive sync intervals
   */
  trackActivity(dbKey, operation) {
    const now = Date.now();
    if (!this.recentActivity.has(dbKey)) {
      this.recentActivity.set(dbKey, []);
    }

    const activity = this.recentActivity.get(dbKey);
    activity.push({ operation, timestamp: now });

    // Keep only last 5 minutes of activity
    const fiveMinutesAgo = now - 300000;
    this.recentActivity.set(dbKey,
      activity.filter(a => a.timestamp > fiveMinutesAgo)
    );
  }

  /**
   * Get recent activity for adaptive intervals
   */
  getRecentActivity(dbKey) {
    const activity = this.recentActivity.get(dbKey) || [];
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    const recentChanges = activity.filter(a => a.timestamp > oneMinuteAgo);

    return {
      changesPerMinute: recentChanges.length,
      totalChanges: activity.length,
      lastChange: activity.length > 0 ? activity[activity.length - 1].timestamp : 0
    };
  }

  /**
   * Perform coordinated sync: LAN first, then Remote with performance tracking
   */
  async performCoordinatedSync(dbKey, operation) {
    const syncKey = `${dbKey}_coordinated`;
    const startTime = Date.now();

    // Prevent concurrent coordinated syncs for the same database
    if (this.activeSyncs.has(syncKey)) {
      console.log(`[SyncService] Coordinated sync already in progress for ${dbKey}`);
      return;
    }

    this.activeSyncs.add(syncKey);

    try {
      console.log(`[SyncService] Starting coordinated sync for ${dbKey}`);

      // Step 1: Perform LAN sync first
      const lanStartTime = Date.now();
      await this.performLanSync(dbKey);
      const lanTime = Date.now() - lanStartTime;
      console.log(`[SyncService] LAN sync completed for ${dbKey} in ${lanTime}ms, starting Remote sync`);

      // Step 2: Wait a moment for LAN sync to settle
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 3: Perform Remote sync
      const remoteStartTime = Date.now();
      await this.performRemoteSync(dbKey);
      const remoteTime = Date.now() - remoteStartTime;
      const totalTime = Date.now() - startTime;

      console.log(`[SyncService] Coordinated sync completed for ${dbKey} in ${totalTime}ms (LAN: ${lanTime}ms, Remote: ${remoteTime}ms)`);

      // Update performance metrics
      this.updateSyncMetrics(dbKey, { lanTime, remoteTime, totalTime, operation });

      // Log slow syncs for performance monitoring
      if (totalTime > 5000) {
        console.warn(`[SyncService] Slow coordinated sync for ${dbKey}: ${totalTime}ms (LAN: ${lanTime}ms, Remote: ${remoteTime}ms)`);
      }

      // Emit coordinated sync complete event
      this.emit('coordinatedSyncComplete', { dbKey, operation, performance: { lanTime, remoteTime, totalTime } });

    } catch (error) {
      console.error(`[SyncService] Coordinated sync failed for ${dbKey}:`, error);
      this.emit('coordinatedSyncError', { dbKey, operation, error: error.message });
    } finally {
      this.activeSyncs.delete(syncKey);
    }
  }

  /**
   * Update sync performance metrics
   */
  updateSyncMetrics(dbKey, metrics) {
    if (!this.performanceMetrics.has(dbKey)) {
      this.performanceMetrics.set(dbKey, {
        totalSyncs: 0,
        averageLanTime: 0,
        averageRemoteTime: 0,
        averageTotalTime: 0,
        slowSyncs: 0,
        lastSyncTime: 0
      });
    }

    const stats = this.performanceMetrics.get(dbKey);
    stats.totalSyncs++;
    stats.lastSyncTime = Date.now();

    // Update averages using running average
    if (metrics.lanTime) {
      stats.averageLanTime = ((stats.averageLanTime * (stats.totalSyncs - 1)) + metrics.lanTime) / stats.totalSyncs;
    }
    if (metrics.remoteTime) {
      stats.averageRemoteTime = ((stats.averageRemoteTime * (stats.totalSyncs - 1)) + metrics.remoteTime) / stats.totalSyncs;
    }
    if (metrics.totalTime) {
      stats.averageTotalTime = ((stats.averageTotalTime * (stats.totalSyncs - 1)) + metrics.totalTime) / stats.totalSyncs;

      // Track slow syncs (> 5 seconds)
      if (metrics.totalTime > 5000) {
        stats.slowSyncs++;
      }
    }
  }

  /**
   * Perform LAN sync
   */
  async performLanSync(dbKey) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) {
      console.warn(`[SyncService] Database ${dbKey} not found for LAN sync`);
      return;
    }

    if (!dbConfig.lanString) {
      console.warn(`[SyncService] No LAN configuration for ${dbKey}, skipping LAN sync`);
      return;
    }

    const syncKey = `${dbKey}_lan`;
    if (this.activeSyncs.has(syncKey)) {
      // console.log(`[SyncService] LAN sync already in progress for ${dbKey}`);
      return;
    }

    this.activeSyncs.add(syncKey);

    try {
      const { name, lanString, databasePrefix } = dbConfig;
      const localDb = dbConfig.db;
      const targetUrl = lanString + databasePrefix + name;

      // console.log(`[SyncService] Performing LAN sync for ${dbKey} to ${targetUrl}`);

      const syncOptions = {
        live: this.settings.liveSync,
        retry: this.settings.retrySettings.enabled,
        include_docs: true,
        batch_size: this.settings.batchSize,
        timeout: this.settings.syncTimeout,
        conflicts: true,
        filter: this.createDocumentFilter('lan'),
        back_off_function: (delay) => {
          const maxDelay = this.settings.retrySettings.initialDelay *
                          Math.pow(this.settings.retrySettings.backoffMultiplier, this.settings.retrySettings.maxRetries);
          return Math.min(delay * this.settings.retrySettings.backoffMultiplier, maxDelay);
        }
      };

      await localDb.sync(targetUrl, syncOptions)
        .on('change', async (info) => {
          // console.log(`[SyncService] LAN sync change for ${dbKey}:`, info.change?.docs?.length || 0, 'docs');
          // Handle conflicts using DatabaseHandler (excluding design documents)
          if (info.change && info.change.docs) {
            for (const doc of info.change.docs) {
              if (doc._conflicts && !doc._id.startsWith('_design/')) {
                await this.databaseHandler.handleConflicts({ doc }, 'lan');
              }
            }
          }
        })
        .on('complete', (info) => {
          // console.log(`[SyncService] LAN sync completed for ${dbKey}:`);
        })
        .on('error', (err) => {
          // console.error(`[SyncService] LAN sync error for ${dbKey}:`, err);
        });

      // console.log(`[SyncService] LAN sync finished for ${dbKey}`);
      this.emit('syncComplete', { dbKey, syncType: 'lan' });

    } catch (error) {
      // console.error(`[SyncService] LAN sync failed for ${dbKey}:`, error);

      // Provide more specific error information
      let errorMessage = error.message;
      if (error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Cannot connect to LAN server - server may be down or unreachable';
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'LAN server hostname not found - check network configuration';
      } else if (error.message.includes('unauthorized') || error.status === 401) {
        errorMessage = 'Authentication failed - check LAN credentials';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - check network connectivity';
      }

      this.emit('syncError', {
        dbKey,
        syncType: 'lan',
        error: errorMessage,
        originalError: error.message
      });
    } finally {
      this.activeSyncs.delete(syncKey);
    }
  }

  /**
   * Perform remote sync
   */
  async performRemoteSync(dbKey) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) {
      console.warn(`[SyncService] Database ${dbKey} not found for remote sync`);
      return;
    }

    const remoteUrl = this.remoteConnectionString || REMOTE_DATABASE_CONNECTION_STRING;
    if (!remoteUrl) {
      console.warn(`[SyncService] No remote URL configured for ${dbKey}, skipping remote sync`);
      return;
    }

    const syncKey = `${dbKey}_remote`;
    if (this.activeSyncs.has(syncKey)) {
      // console.log(`[SyncService] Remote sync already in progress for ${dbKey}`);
      return;
    }

    this.activeSyncs.add(syncKey);

    try {
      const { name, databasePrefix } = dbConfig;
      const localDb = dbConfig.db;
      const targetUrl = remoteUrl + databasePrefix + name;

      // console.log(`[SyncService] Performing remote sync for ${dbKey} to ${targetUrl}`);

      const syncOptions = {
        live: this.settings.liveSync,
        retry: this.settings.retrySettings.enabled,
        include_docs: true,
        batch_size: this.settings.batchSize,
        timeout: this.settings.syncTimeout,
        conflicts: true,
        filter: this.createDocumentFilter('remote'),
        back_off_function: (delay) => {
          const maxDelay = this.settings.retrySettings.initialDelay *
                          Math.pow(this.settings.retrySettings.backoffMultiplier, this.settings.retrySettings.maxRetries);
          return Math.min(delay * this.settings.retrySettings.backoffMultiplier, maxDelay);
        }
      };

      await localDb.sync(targetUrl, syncOptions)
        .on('change', async (info) => {
          // console.log(`[SyncService] Remote sync change for ${dbKey}:`, info.change?.docs?.length || 0, 'docs');
          // Handle conflicts using DatabaseHandler (excluding design documents)
          if (info.change && info.change.docs) {
            for (const doc of info.change.docs) {
              if (doc._conflicts && !doc._id.startsWith('_design/')) {
                await this.databaseHandler.handleConflicts({ doc }, 'remote');
              }
            }
          }
        })
        .on('complete', (info) => {
          // console.log(`[SyncService] Remote sync completed for ${dbKey}:`);
        })
        .on('error', (err) => {
          // console.error(`[SyncService] Remote sync error for ${dbKey}:`, err);
        });

      // console.log(`[SyncService] Remote sync finished for ${dbKey}`);
      this.emit('syncComplete', { dbKey, syncType: 'remote' });

    } catch (error) {
      // console.error(`[SyncService] Remote sync failed for ${dbKey}:`, error);
      this.emit('syncError', { dbKey, syncType: 'remote', error: error.message });
    } finally {
      this.activeSyncs.delete(syncKey);
    }
  }





  /**
   * Force full sync for all databases with coordination
   */
  async forceFullSync() {
    const databases = this.databaseHandler.getAllDatabases();
    console.log(`[SyncService] Starting coordinated full sync for ${databases.length} databases`);

    for (const dbKey of databases) {
      await this.performCoordinatedSync(dbKey, 'force_full_sync');
    }

    // Verify sync consistency after full sync
    await this.verifySyncConsistency();

    return { success: true };
  }

  /**
   * Verify sync consistency across all databases
   */
  async verifySyncConsistency() {
    console.log('[SyncService] Starting sync consistency verification');
    const databases = this.databaseHandler.getAllDatabases();
    const inconsistencies = [];

    for (const dbKey of databases) {
      try {
        const issues = await this.validateDataConsistency(dbKey);
        if (issues.length > 0) {
          inconsistencies.push({ dbKey, issues });
        }
      } catch (error) {
        console.error(`[SyncService] Failed to verify consistency for ${dbKey}:`, error);
        inconsistencies.push({ dbKey, issues: [`Verification failed: ${error.message}`] });
      }
    }

    if (inconsistencies.length > 0) {
      console.warn('[SyncService] Sync inconsistencies detected:', inconsistencies);
      this.emit('syncInconsistencies', inconsistencies);
    } else {
      console.log('[SyncService] All databases are consistent');
      this.emit('syncConsistencyVerified');
    }

    return inconsistencies;
  }

  /**
   * Validate data consistency for a specific database
   */
  async validateDataConsistency(dbKey) {
    const issues = [];
    const dbConfig = this.databaseHandler.databases.get(dbKey);

    if (!dbConfig) {
      issues.push('Database configuration not found');
      return issues;
    }

    try {
      // Check for unresolved conflicts
      const result = await dbConfig.db.allDocs({
        include_docs: true,
        conflicts: true
      });

      const conflictedDocs = result.rows.filter(row =>
        row.doc && row.doc._conflicts && row.doc._conflicts.length > 0
      );

      if (conflictedDocs.length > 0) {
        issues.push(`${conflictedDocs.length} documents have unresolved conflicts`);
      }

      // Check for missing timestamps
      const docsWithoutTimestamps = result.rows.filter(row =>
        row.doc && !row.doc._id.startsWith('_') &&
        (!row.doc.updatedAt || !row.doc.createdAt)
      );

      if (docsWithoutTimestamps.length > 0) {
        issues.push(`${docsWithoutTimestamps.length} documents missing timestamps`);
      }

      console.log(`[SyncService] Consistency check for ${dbKey}: ${issues.length} issues found`);

    } catch (error) {
      issues.push(`Consistency check failed: ${error.message}`);
    }

    return issues;
  }

  /**
   * Stop the sync service
   */
  async stopSyncService() {
    this.isRunning = false;

    // Clear all intervals and timeouts
    for (const [dbKey] of this.syncIntervals) {
      this.stopDatabaseSync(dbKey);
    }

    // Clear all debounce timeouts
    for (const [dbKey, timeout] of this.syncTimeouts) {
      clearTimeout(timeout);
    }
    this.syncTimeouts.clear();

    // Clean up all cached connections
    for (const [connectionKey, timeout] of this.connectionTimeouts) {
      clearTimeout(timeout);
    }
    this.connectionTimeouts.clear();
    this.syncConnections.clear();

    // Clear performance data
    this.performanceMetrics.clear();
    this.recentActivity.clear();

    // Clear active syncs
    this.activeSyncs.clear();

    console.log('[SyncService] All resources cleaned up successfully');
    return { success: true };
  }

  /**
   * Perform a specific LAN sync with a custom URL (for Login component)
   */
  async performSpecificLanSync(dbKey, lanUrl) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) {
      throw new Error(`Database ${dbKey} not found`);
    }

    const syncKey = `${dbKey}_specific_lan`;
    if (this.activeSyncs.has(syncKey)) {
      console.log(`[SyncService] Specific LAN sync already in progress for ${dbKey}`);
      return { success: true, message: 'Sync already in progress' };
    }

    this.activeSyncs.add(syncKey);

    try {

      const localDb = dbConfig.db;

      // console.log(`[SyncService] Starting specific LAN sync for ${dbKey} to ${lanUrl}`);

      const syncOptions = {
        live: this.settings.liveSync,
        retry: this.settings.retrySettings.enabled,
        include_docs: true,
        batch_size: this.settings.batchSize,
        timeout: this.settings.syncTimeout,
        conflicts: true,
        filter: this.createDocumentFilter('lan_specific'),
        back_off_function: (delay) => {
          const maxDelay = this.settings.retrySettings.initialDelay *
                          Math.pow(this.settings.retrySettings.backoffMultiplier, this.settings.retrySettings.maxRetries);
          return Math.min(delay * this.settings.retrySettings.backoffMultiplier, maxDelay);
        }
      };

      await localDb.sync(lanUrl, syncOptions)
        .on('change', async (info) => {
          // Handle conflicts using DatabaseHandler (excluding design documents)
          if (info.change && info.change.docs) {
            for (const doc of info.change.docs) {
              if (doc._conflicts && !doc._id.startsWith('_design/')) {
                await this.databaseHandler.handleConflicts({ doc }, 'lan');
              }
            }
          }
        });

      console.log(`[SyncService] Specific LAN sync completed successfully for ${dbKey}`);
      this.emit('syncComplete', { dbKey, syncType: 'specific_lan' });

      return { success: true, message: 'Specific LAN sync completed' };

    } catch (error) {
      console.error(`[SyncService] Specific LAN sync failed for ${dbKey}:`, error);

      // Provide more specific error information
      let errorMessage = error.message;
      if (error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Cannot connect to LAN server - server may be down or unreachable';
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'LAN server hostname not found - check network configuration';
      } else if (error.message.includes('unauthorized') || error.status === 401) {
        errorMessage = 'Authentication failed - check LAN credentials';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - check network connectivity';
      }

      this.emit('syncError', {
        dbKey,
        syncType: 'specific_lan',
        error: errorMessage,
        originalError: error.message
      });

      throw new Error(errorMessage);
    } finally {
      this.activeSyncs.delete(syncKey);
    }
  }

  /**
   * Add database to sync service
   */
  async addDatabase(dbKey) {
    if (!this.isRunning) {
      return { success: false, error: 'Sync service not running' };
    }
    this.startDatabaseSync(dbKey);
    return { success: true };
  }

  /**
   * Remove database from sync service
   */
  async removeDatabase(dbKey) {
    this.stopDatabaseSync(dbKey);
    return { success: true };
  }



  /**
   * Force sync for a specific database
   */
  async forceSyncDatabase(dbKey) {
    await this.performLanSync(dbKey);
    await this.performRemoteSync(dbKey);
    return { success: true };
  }

  /**
   * Perform instant LAN sync for a specific database
   */
  async performInstantLanSync(dbKey) {
    return await this.performLanSync(dbKey);
  }

  /**
   * Perform instant remote sync for a specific database
   */
  async performInstantRemoteSync(dbKey) {
    return await this.performRemoteSync(dbKey);
  }

  /**
   * Perform LAN sync with a specific URL
   */
  async performLanSyncWithUrl(dbKey, lanUrl) {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) {
      throw new Error(`Database ${dbKey} not found`);
    }

    const syncKey = `${dbKey}_lan_specific`;
    if (this.activeSyncs.has(syncKey)) {
      throw new Error(`Sync already in progress for ${dbKey}`);
    }

    this.activeSyncs.add(syncKey);

    try {

      const localDb = dbConfig.db;

      const syncOptions = {
        live: this.settings.liveSync,
        retry: this.settings.retrySettings.enabled,
        include_docs: true,
        batch_size: this.settings.batchSize,
        timeout: this.settings.syncTimeout,
        conflicts: true,
        filter: this.createDocumentFilter('lan'),
        back_off_function: (delay) => {
          const maxDelay = this.settings.retrySettings.initialDelay *
                          Math.pow(this.settings.retrySettings.backoffMultiplier, this.settings.retrySettings.maxRetries);
          return Math.min(delay * this.settings.retrySettings.backoffMultiplier, maxDelay);
        }
      };

      await localDb.sync(lanUrl, syncOptions)
        .on('change', async (info) => {
          // Handle conflicts using DatabaseHandler (excluding design documents)
          if (info.change && info.change.docs) {
            for (const doc of info.change.docs) {
              if (doc._conflicts && !doc._id.startsWith('_design/')) {
                await this.databaseHandler.handleConflicts({ doc }, 'lan_specific');
              }
            }
          }
        });
      this.emit('syncComplete', { dbKey, syncType: 'lan_specific', url: lanUrl });

      return { success: true };
    } catch (error) {
      this.emit('syncError', { dbKey, error: error.message });
      throw error;
    } finally {
      this.activeSyncs.delete(syncKey);
    }
  }

  /**
   * Set remote connection string
   */
  setRemoteConnectionString(url) {
    this.remoteConnectionString = url;
    // console.log(`[SyncService] Remote connection string updated to: ${url}`);
  }

  /**
   * Get sync status for all databases with consistency information
   */
  getSyncStatus() {
    const status = {};
    for (const dbKey of this.databaseHandler.getAllDatabases()) {
      const dbStatus = this.databaseHandler.getSyncStatus(dbKey);
      const dbConfig = this.databaseHandler.databases.get(dbKey);

      status[dbKey] = {
        ...dbStatus,
        hasLanConfig: !!dbConfig?.lanString,
        hasRemoteConfig: !!this.remoteConnectionString || !!REMOTE_DATABASE_CONNECTION_STRING,
        activeSyncs: Array.from(this.activeSyncs).filter(sync => sync.startsWith(dbKey)),
        syncSettings: {
          liveSync: this.settings.liveSync,
          retryEnabled: this.settings.retrySettings.enabled,
          batchSize: this.settings.batchSize,
          timeout: this.settings.syncTimeout
        }
      };
    }
    return status;
  }

  /**
   * Get detailed sync statistics with performance metrics
   */
  getSyncStatistics() {
    const databases = this.databaseHandler.getAllDatabases();
    const performanceData = {};

    // Collect performance metrics for each database
    databases.forEach(dbKey => {
      const metrics = this.performanceMetrics.get(dbKey);
      const activity = this.getRecentActivity(dbKey);

      performanceData[dbKey] = {
        metrics: metrics || {
          totalSyncs: 0,
          averageLanTime: 0,
          averageRemoteTime: 0,
          averageTotalTime: 0,
          slowSyncs: 0,
          lastSyncTime: 0
        },
        activity,
        hasActiveSync: Array.from(this.activeSyncs).some(sync => sync.startsWith(dbKey)),
        connectionsCached: Array.from(this.syncConnections.keys()).filter(key => key.startsWith(dbKey)).length
      };
    });

    return {
      totalDatabases: databases.length,
      activeSyncs: this.activeSyncs.size,
      syncIntervals: this.syncIntervals.size,
      cachedConnections: this.syncConnections.size,
      pendingTimeouts: this.syncTimeouts.size,
      settings: this.settings,
      isRunning: this.isRunning,
      performanceData,
      systemMetrics: {
        totalSyncs: Array.from(this.performanceMetrics.values()).reduce((sum, m) => sum + m.totalSyncs, 0),
        averagePerformance: this.calculateAveragePerformance(),
        slowSyncPercentage: this.calculateSlowSyncPercentage()
      }
    };
  }

  /**
   * Calculate average performance across all databases
   */
  calculateAveragePerformance() {
    const allMetrics = Array.from(this.performanceMetrics.values());
    if (allMetrics.length === 0) return { lan: 0, remote: 0, total: 0 };

    const totals = allMetrics.reduce((acc, metrics) => ({
      lan: acc.lan + metrics.averageLanTime,
      remote: acc.remote + metrics.averageRemoteTime,
      total: acc.total + metrics.averageTotalTime
    }), { lan: 0, remote: 0, total: 0 });

    return {
      lan: Math.round(totals.lan / allMetrics.length),
      remote: Math.round(totals.remote / allMetrics.length),
      total: Math.round(totals.total / allMetrics.length)
    };
  }

  /**
   * Calculate percentage of slow syncs
   */
  calculateSlowSyncPercentage() {
    const allMetrics = Array.from(this.performanceMetrics.values());
    if (allMetrics.length === 0) return 0;

    const totalSyncs = allMetrics.reduce((sum, m) => sum + m.totalSyncs, 0);
    const slowSyncs = allMetrics.reduce((sum, m) => sum + m.slowSyncs, 0);

    return totalSyncs > 0 ? Math.round((slowSyncs / totalSyncs) * 100) : 0;
  }

  /**
   * Analyze database for large documents
   * Helps identify documents that might cause sync issues
   * @param {string} dbKey - Database key to analyze
   * @param {string} syncType - Type of sync ('lan', 'remote', 'both')
   */
  async analyzeLargeDocuments(dbKey, syncType = 'both') {
    const dbConfig = this.databaseHandler.databases.get(dbKey);
    if (!dbConfig) {
      throw new Error(`Database ${dbKey} not found`);
    }

    try {
      console.log(`[SyncService] Analyzing large documents in ${dbKey}...`);

      const result = await dbConfig.db.allDocs({
        include_docs: true,
        attachments: false
      });

      const largeDocuments = {
        lan: [],
        remote: []
      };
      const documentSizes = [];
      const lanLimit = this.settings.maxDocumentSize.lan;
      const remoteLimit = this.settings.maxDocumentSize.remote;

      result.rows.forEach(row => {
        if (row.doc && !row.doc._id.startsWith('_')) {
          const docSize = JSON.stringify(row.doc).length;
          documentSizes.push(docSize);

          const docInfo = {
            id: row.doc._id,
            size: docSize,
            sizeKB: Math.round(docSize / 1024),
            sizeMB: Math.round(docSize / (1024 * 1024) * 100) / 100,
            hasAttachments: !!row.doc._attachments,
            attachmentCount: row.doc._attachments ? Object.keys(row.doc._attachments).length : 0
          };

          // Check against LAN limit
          if (docSize > lanLimit) {
            largeDocuments.lan.push({ ...docInfo, exceedsBy: docSize - lanLimit });
          }

          // Check against Remote limit
          if (docSize > remoteLimit) {
            largeDocuments.remote.push({ ...docInfo, exceedsBy: docSize - remoteLimit });
          }
        }
      });

      // Calculate statistics
      const totalDocs = documentSizes.length;
      const averageSize = totalDocs > 0 ? Math.round(documentSizes.reduce((a, b) => a + b, 0) / totalDocs) : 0;
      const maxSize = totalDocs > 0 ? Math.max(...documentSizes) : 0;

      const analysis = {
        database: dbKey,
        totalDocuments: totalDocs,
        averageSizeBytes: averageSize,
        averageSizeKB: Math.round(averageSize / 1024),
        maxSizeBytes: maxSize,
        maxSizeKB: Math.round(maxSize / 1024),
        maxSizeMB: Math.round(maxSize / (1024 * 1024) * 100) / 100,
        limits: {
          lan: {
            bytes: this.settings.maxDocumentSize.lan,
            kb: Math.round(this.settings.maxDocumentSize.lan / 1024),
            exceeding: largeDocuments.lan.length
          },
          remote: {
            bytes: this.settings.maxDocumentSize.remote,
            kb: Math.round(this.settings.maxDocumentSize.remote / 1024),
            exceeding: largeDocuments.remote.length
          }
        },
        problematicDocuments: {
          lan: largeDocuments.lan,
          remote: largeDocuments.remote,
          both: largeDocuments.remote.filter(doc =>
            largeDocuments.lan.some(lanDoc => lanDoc.id === doc.id)
          )
        }
      };

      console.log(`[SyncService] Analysis complete for ${dbKey}:`, {
        total: analysis.totalDocuments,
        exceedingLAN: analysis.limits.lan.exceeding,
        exceedingRemote: analysis.limits.remote.exceeding,
        avgKB: analysis.averageSizeKB,
        maxKB: analysis.maxSizeKB,
        lanLimitKB: analysis.limits.lan.kb,
        remoteLimitKB: analysis.limits.remote.kb
      });

      return analysis;

    } catch (error) {
      console.error(`[SyncService] Error analyzing documents in ${dbKey}:`, error);
      throw error;
    }
  }
}

module.exports = SyncService;
