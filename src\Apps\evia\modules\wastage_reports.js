export default {
  name: "Wastage Reports",
  icon: "TableOutlined",
  path: "restaurant/tables",
  collection: "wastage_reports",
  singular: "Wastage Report",
  parent: "restaurant",
  columns: [
    {
      title: "Time",
      dataIndex: "time",
      valueType: "time",
    },
    {
      title: "Recorded By",
      dataIndex: "recorded_by",
      type: "dbSelect",
      collection: "users",
      label: ["surname", "firstname"],
    },
    {
      title: "Food Type",
      dataIndex: "food_type",
      type: "text",
    },
    {
      title: "Reason",
      dataIndex: "reason",
      type: "textarea",
    },
    {
      title: "No of portions",
      dataIndex: "no_of_portions",
      type: "digit",
    },
    {
      title: "Size (KGs)",
      dataIndex: "size",
      type: "digit",
    },
    {
      title: "Cost",
      dataIndex: "cost",
      type: "digit",
    },
  ],
};
