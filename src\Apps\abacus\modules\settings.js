exports.settings = {
  name: "Settings",
  icon: "KeyOutlined",
  path: "/settings",
  collection: "settings",
  singular: "Setting",
  columns: [
    {
      title: "Currency",
      dataIndex: "currency",
      isRequired: true,
      sorter: true,
      hideInTable: true,
      colProps: {
        md: 6,
      },
      valueType: "select",
      valueEnum: JSON.parse(localStorage.getItem("GEO") || "{}").currencies,
    },
    {
      dataIndex: "documentHeader",
      title: "Document Header",
      valueType: "select",
      initialValue: "V1",
      valueEnum: {
        V1: { text: "V1" },
        V2: { text: "V2" },
      },
      colProps: {
        sm: 6,
      },
    },
    {
      dataIndex: "showInvoiceDetails",
      title: "Show Invoice Details",
      valueType: "switch",
      initialValue: true,
      colProps: {
        md: 12
      }
    },
    {
      dataIndex: "showPreviousReceipts",
      title: "Show Previous Receipts",
      valueType: "switch",
      initialValue: true,
      colProps: {
        md: 12
      }
    }
  ]
};