/**
 * Database Prefix Cleanup Script
 * 
 * This script helps clean up inconsistent database file naming where
 * logs databases were created with prefixes in their file paths while
 * other databases were not.
 * 
 * Usage: node scripts/cleanup-database-prefixes.js
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');

// Function to get the database directory path
function getDatabasePath() {
  try {
    // Try to get the app path (works when Electron is running)
    return path.join(app.getPath('userData'), '.database');
  } catch (error) {
    // Fallback for when running outside Electron
    const os = require('os');
    const appName = require('../package.json').name;
    return path.join(os.homedir(), 'AppData', 'Roaming', appName, '.database');
  }
}

// Function to find prefixed log databases
function findPrefixedLogDatabases(databasePath) {
  if (!fs.existsSync(databasePath)) {
    console.log('Database directory does not exist:', databasePath);
    return [];
  }

  const files = fs.readdirSync(databasePath);
  const prefixedLogDbs = files.filter(file => {
    // Look for files that end with 'logs' but have a prefix
    return file.includes('_') && file.endsWith('logs') && file !== 'logs';
  });

  return prefixedLogDbs;
}

// Function to migrate prefixed log database to standard logs database
function migratePrefixedLogDatabase(databasePath, prefixedDbName) {
  const prefixedPath = path.join(databasePath, prefixedDbName);
  const standardPath = path.join(databasePath, 'logs');

  console.log(`\nMigrating: ${prefixedDbName} -> logs`);

  try {
    // Check if standard logs database already exists
    if (fs.existsSync(standardPath)) {
      console.log('  Standard logs database already exists');
      console.log('  Creating backup of prefixed database...');
      
      const backupPath = path.join(databasePath, `${prefixedDbName}.backup.${Date.now()}`);
      fs.renameSync(prefixedPath, backupPath);
      console.log(`  Backup created: ${path.basename(backupPath)}`);
    } else {
      // Move prefixed database to standard location
      fs.renameSync(prefixedPath, standardPath);
      console.log('  Successfully migrated to standard logs database');
    }
  } catch (error) {
    console.error(`  Error migrating ${prefixedDbName}:`, error.message);
  }
}

// Main cleanup function
function cleanupDatabasePrefixes() {
  console.log('🔧 Database Prefix Cleanup Tool');
  console.log('================================\n');

  const databasePath = getDatabasePath();
  console.log('Database directory:', databasePath);

  const prefixedLogDbs = findPrefixedLogDatabases(databasePath);

  if (prefixedLogDbs.length === 0) {
    console.log('✅ No prefixed log databases found. All databases are consistent!');
    return;
  }

  console.log(`\nFound ${prefixedLogDbs.length} prefixed log database(s):`);
  prefixedLogDbs.forEach(db => console.log(`  - ${db}`));

  console.log('\n🔄 Starting migration...');
  prefixedLogDbs.forEach(db => {
    migratePrefixedLogDatabase(databasePath, db);
  });

  console.log('\n✅ Database prefix cleanup completed!');
  console.log('\nNote: After running this script, restart your application to ensure');
  console.log('all database connections use the new consistent naming scheme.');
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanupDatabasePrefixes();
}

module.exports = {
  cleanupDatabasePrefixes,
  getDatabasePath,
  findPrefixedLogDatabases
};
