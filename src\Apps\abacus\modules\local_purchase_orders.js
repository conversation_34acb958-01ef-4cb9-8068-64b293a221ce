const moment = require("moment");
const { numberFormat } = require("../../../Utils/functions");

exports.local_purchase_orders = {
  name: "Local Purchase Orders",
  icon: "ShoppingCartOutlined",
  path: "/inventory/local_purchase_orders",
  parent: "inventory",
  collection: "local_purchase_orders",
  singular: "Local Purchase Order",
  columns: [
    {
      valueType: "date",
      dataIndex: "date",
      title: "Date",
      isPrintable: true,
      initialValue: moment().startOf("day"),
    },
    // Branch will be automatically attached, not shown in form
    {
      dataIndex: "supplier",
      title: "Supplier",
      type: "dbSelect",
      valueType: "select",
      collection: "suppliers",
      label: ["name"],
      isPrintable: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "Please select a supplier",
          },
        ],
      },
    },
    {
      dataIndex: "status",
      title: "Status",
      valueType: "select",
      valueEnum: {
        draft: { text: "Draft", status: "Default" },
        approved: { text: "Approved", status: "Processing" },
        pending: { text: "Pending", status: "Warning" },
        converted: { text: "Converted to Stock", status: "Success" },
        cancelled: { text: "Cancelled", status: "Error" },
      },
      initialValue: "draft",
      isPrintable: true,
    },
    {
      dataIndex: "lpo_number",
      title: "LPO Number",
      valueType: "text",
      isPrintable: true,
      // Made optional as requested
    },
    {
      valueType: "date",
      dataIndex: "expected_delivery_date",
      title: "Expected Delivery Date",
      isPrintable: true,
    },
    {
      title: "Item Count",
      dataIndex: "items",
      hideInSearch: true,
      hideInForm: true,
      render: (items) => items && Array.isArray(items) ? items.length : 0,
    },
    {
      valueType: "formList",
      dataIndex: "items",
      title: "Items",
      hideInTable: true,
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          block: true,
          style: {
            width: "100%",
          },
          copyIconProps: false,
          creatorButtonText: "Add Item",
        },
      },
      columns: [
        {
          valueType: "group",
          colProps: {
            md: 24,
          },
          columns: [
            {
              dataIndex: "product",
              title: "Product",
              type: "dbSelect",
              valueType: "select",
              collection: "products",
              label: ["name", " ", "sku", " - ", "measurements", "units"],
              colProps: {
                md: 12,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Please Select a product`,
                  },
                ],
              },
            },
            {
              valueType: "digit",
              dataIndex: "quantity",
              title: "Quantity",
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Quantity is required`,
                  },
                ],
              },
              colProps: {
                md: 3,
              },
              dependency: {
                value: "product",
                rules: [{ required: true, message: "Please select a product" }],
              },
            },
            {
              valueType: "digit",
              dataIndex: "price",
              title: "Price",
              colProps: {
                md: 3,
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: `Price is required`,
                  },
                ],
              },
              dependency: {
                value: "product",
                rules: [{ required: true, message: "Please select a product" }],
              },
            },
            // Expected delivery date moved to general fields
            {
              valueType: "dependency",
              fieldProps: {
                name: ["price", "quantity"],
                colProps: {
                  md: 3,
                },
              },
              columns: ({ price, quantity }) => {
                return [
                  {
                    title: "Total",
                    dataIndex: "total",
                    colProps: {
                      md: 3,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (price ? price : 0) * (quantity ? quantity : 0),
                    },
                    valueType: "digit",
                  },
                ];
              },
            },
          ],
        },
      ],
    },
    {
      dataIndex: "notes",
      title: "Notes",
      valueType: "textarea",
      isPrintable: true,
    },
    {
      dataIndex: "preparedBy",
      title: "Prepared By",
      type: "dbSelect",
      valueType: "select",
      collection: "users",
      label: ["first_name", "last_name"],
      hideInForm: true,
      isPrintable: true,
    },
    {
      dataIndex: "approvedBy",
      title: "Approved By",
      type: "dbSelect",
      valueType: "select",
      collection: "users",
      label: ["first_name", "last_name"],
      hideInForm: true,
      isPrintable: true,
    },
    {
      title: "Total",
      dataIndex: "items",
      valueType: "digit", // Changed from money to digit
      hideInSearch: true,
      render: (_, record) => {
        const items = record.items;
        let total = 0;
        if (items) {
          items.forEach(
            (item) =>
            (total = item
              ? total +
              (item.price ? item.price : 0) *
              (item.quantity ? item.quantity : 0)
              : total)
          );
        }
        return numberFormat(total);
      },
      fieldProps: {
        name: ["items"],
      },
      columns: ({ items }) => {
        return [
          {
            title: "Total",
            dataIndex: "total",
            colProps: {
              md: 8,
              offset: 16,
            },
            fieldProps: {
              disabled: true,
              value: items && Array.isArray(items) ? items.reduce(
                (pv, cv) =>
                  pv +
                  (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                0
              ) : 0,
            },
            valueType: "digit",
          },
        ];
      },
    },
  ],
};
