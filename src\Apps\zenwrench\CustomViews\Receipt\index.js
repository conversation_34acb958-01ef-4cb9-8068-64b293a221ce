import React, { useEffect, useState } from "react";
import { Flex, Divider, Image, Space } from "antd";
import RenderBlob from "../../../../Components/RenderBlob";
import { toWordsUGX } from "../../../../Utils/functions";
import moment from "moment";
import DocumentFooter from "../DocumentFooter";
import { safeFilterReceiptsByInvoice, safeCalculateReceiptTotal } from "../../../../Utils/ReceiptDataUtils";

const nagoaLogo = require("../Invoice/nagoaLogo.png");

function ChildReceipt(props) {
  const { data, copy, pouchDatabase, databasePrefix } = props;

  const gridContainerStyle = {
    display: "grid",
    gridTemplateColumns: "auto 1fr",
    gap: "1px", // Optional, for spacing between columns
    fontSize: 14,
    paddingTop: 20,
  };

  const contentStyle = {
    padding: "1px",
  };

  const PaymentMethodText = props.columns.find((x) => x.dataIndex === "pMethod")
    ?.valueEnum[data.pMethod].text;

  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS"));

  
  const [company, setCompany] = useState(null);
  const [invoceTotal, setInvoiceTotal] = useState(0);
  const [invoceTotalPaid, setInvoiceTotalPaid] = useState(0);
  const [job, setJob] = useState(null);
  useEffect(() => {
    const loadCompanyData = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            let comp = organizationsData[0];

            if (comp._attachments && comp._attachments.logo) {
              try {
                const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
                comp.logo = logoBlob;
                setCompany(comp);
              } catch (logoError) {
                console.warn("Error fetching logo:", logoError);
                setCompany(comp);
              }
            } else {
              setCompany(comp);
            }
          } else {
            console.warn("No organization data found, using fallback");
            setCompany({
              name: "Your Company",
              phone: "",
              alternative_phone: "",
              email: "",
              website: "",
              address: ""
            });
          }
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setCompany({
            name: "Your Company",
            phone: "",
            alternative_phone: "",
            email: "",
            website: "",
            address: ""
          });
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        setCompany({
          name: "Your Company",
          phone: "",
          alternative_phone: "",
          email: "",
          website: "",
          address: ""
        });
      }
    };

    loadCompanyData();
  }, [pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadInvoiceTotal = async () => {
      if (!data.invoice) return;

      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const res = await pouchDatabase("invoices", databasePrefix).getDocument(data.invoice.value);
          const discount = res.discount ? res.discount : 0;
          const subTotal = res.items.reduce(
            (acc, item) => acc + item.price * item.quantity,
            0
          );
          const tax = res.taxable ? (subTotal - discount) * 0.18 : 0;

          setInvoiceTotal(subTotal - discount + tax);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
        }
      } catch (error) {
        console.error("Error loading invoice:", error);
      }
    };

    loadInvoiceTotal();
  }, [data.invoice, pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadJob = async () => {
      if (!data.invoice) return;

      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const res = await pouchDatabase("invoices", databasePrefix).getDocument(data.invoice.value);
          setJob(res.job);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
        }
      } catch (error) {
        console.error("Error loading job:", error);
      }
    };

    loadJob();
  }, [data.invoice, pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadReceiptTotal = async () => {
      if (!data.invoice) {
        setInvoiceTotalPaid(0);
        return;
      }

      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const receiptsData = await pouchDatabase("receipts", databasePrefix).getAllData();
          const receiptsRows = receiptsData.map(doc => ({ doc }));

          // Use safe utility function to filter receipts by invoice
          const filteredData = safeFilterReceiptsByInvoice(
            receiptsRows,
            data.invoice.value,
            data.date
          );

          // Use safe utility function to calculate total
          const totalPaid = safeCalculateReceiptTotal(filteredData);
          setInvoiceTotalPaid(totalPaid);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setInvoiceTotalPaid(0);
        }
      } catch (error) {
        console.error("Error fetching receipts:", error);
        setInvoiceTotalPaid(0);
      }
    };

    loadReceiptTotal();
  }, [data.invoice?.value, data.date, pouchDatabase, databasePrefix]);

  

  return !company ? null : (
    <div>
      <Flex style={{ width: "100%" }} align="center" justify="space-between">
        <div style={{ width: 250, fontSize: 12 }}>
          <RenderBlob blob={company.logo} size={250} />
          <br />
          <br />
          <p>
            {company.address}
            <br />
            Tel: {company.phone} / {company.alternative_phone} <br />
            Email: {company.email} <br />
            {company.website && company.website}
          </p>
        </div>
        {/* <Divider> */}
        <strong style={{ fontSize: 30 }}>RECEIPT</strong>
        {/* </Divider> */}
        <div style={{ width: 250, fontSize: 12 }}>
          <div style={{ marginLeft: 20, textAlign: "right" }}>
            <p>
              Receipt No : {data._id}
              <br />
              Receipt Date : {moment(data.date).format("DD-MMM-YY")}
              <br />
              <br />
              Printed On : {moment().format("d DD-MMM-YY HH:mm:a")}
            </p>
          </div>
        </div>
      </Flex>
      {/* <Divider orientation="right" style={{ marginTop: 0 }}>
        {copy} Copy
      </Divider> */}
      <div style={gridContainerStyle}>
        <div style={contentStyle}>
          {/* First column content */}
          Received With Thanks From :{" "}
        </div>
        <div
          style={
            (contentStyle,
              { borderBottom: "1px dotted black", fontWeight: "bold" })
          }
        >
          {/* Second column content */}
          {data.invoice
            ? data.invoice.label.split(" - ")[0]
            : data.customer.label}
        </div>
      </div>
      <div style={gridContainerStyle}>
        <div style={contentStyle}>
          {/* First column content */}
          The sum of :{" "}
        </div>
        <div
          style={
            (contentStyle,
              { borderBottom: "1px dotted black", fontWeight: "bold" })
          }
        >
          {/* Second column content */}
          {toWordsUGX.convert(data.amount)}
        </div>
      </div>
      <div style={gridContainerStyle}>
        <div style={contentStyle}>
          {/* First column content */}
          Towards Payment for{" "}
          {job
            ? job.job_type === "wheel_alignment"
              ? "Wheel Alignment done on "
              : job.job_type === "wheel_balance"
                ? "Wheel Balance done on "
                : job.job_type === "wasing_bay"
                  ? "Car Wash done on "
                  : job.job_type === "diagnosis"
                    ? "Diagnosis on "
                    : "work done on the"
            : " "}
          :{"  "}
        </div>
        <div
          style={
            (contentStyle,
              { borderBottom: "1px dotted black", fontWeight: "bold" })
          }
        >
          {/* Second column content */}
          {data.invoice && data.invoice.label.split(" - ")[1]}{" "}
          {data.invoice && data.invoice.label.split(" - ")[2].split(" [")[0]}
          {!data.invoice && data.description}
        </div>
      </div>
      {data.invoice && (
        <div style={gridContainerStyle}>
          <div style={contentStyle}>
            {/* First column content */}
            Invoice Number :{" "}
          </div>
          <div
            style={
              (contentStyle,
                { borderBottom: "1px dotted black", fontWeight: "bold" })
            }
          >
            {/* Second column content */}
            {data.invoice.value}
          </div>
        </div>
      )}
      <Flex
        style={{ width: "100%", padding: 20 }}
        align="center"
        justify="space-between"
      >
        <div>
          Payment Method : <strong>{PaymentMethodText}</strong>
        </div>
        <div style={{ textAlign: "right" }}>
          <table style={{ fontSize: 18, fontWeight: "bold" }}>
            {invoceTotal > 0 && (
              <tr>
                <td style={{ paddingBottom: 0 }}>Amount Payable :</td>
                <td style={{ paddingBottom: 0 }}>
                  UGX {(invoceTotal - invoceTotalPaid).toLocaleString()}
                </td>
              </tr>
            )}
            <tr>
              <td style={{ paddingBottom: 0 }}>Amount Received :</td>
              <td style={{ paddingBottom: 0 }}>
                UGX {data.amount.toLocaleString()}
              </td>
            </tr>
            {invoceTotal > 0 && data.withholding && (
              <tr>
                <td>Withholding Tax :</td>
                <td>
                  UGX{" "}
                  {(invoceTotal - invoceTotalPaid - data.amount > 0
                    ? invoceTotal - invoceTotalPaid - data.amount
                    : 0
                  ).toLocaleString()}
                </td>
              </tr>
            )}
            {invoceTotal > 0 && !data.withholding && (
              <tr>
                <td>Balance :</td>
                <td>
                  UGX{" "}
                  {(invoceTotal - invoceTotalPaid - data.amount > 0
                    ? invoceTotal - invoceTotalPaid - data.amount
                    : 0
                  ).toLocaleString()}
                </td>
              </tr>
            )}
          </table>
        </div>
      </Flex>

      <Flex
        style={{ width: "100%", padding: 20 }}
        align="center"
        justify="space-between"
      >
        <center>
          <br />
          .............................................
          <br />
          For {company.name}
        </center>
        <center>
          No refunds will be issued for this transaction.
        </center>
        <center>
          <br />
          .............................................
          <br />
          Customer Signature
        </center>
      </Flex>

      <DocumentFooter />
      {/* <Divider orientation="left">Disclaimer: No refunds will be issued for this transaction.</Divider> */}
      {/* <Divider orientation="left">Please note that all sales are final and no refunds or exchanges will be accepted.</Divider> */}
    </div>
  );
}

const Receipt = (props) => (
  <>
    <ChildReceipt {...props} copy="Customer" />
    {/* <Divider style={{ marginTop: 50, marginBottom: 30 }} />
    <ChildReceipt {...props} copy="Company" /> */}
  </>
);

export default Receipt;
