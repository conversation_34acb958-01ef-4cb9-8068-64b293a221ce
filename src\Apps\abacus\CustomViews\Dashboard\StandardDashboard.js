import { Col, Row, Card, List, Typography, Tag, Space, Divider } from "antd";
import React, { useEffect, useState, useRef, useCallback, memo } from "react";
import StatsGrid from "../../../../Components/Stats/StatsGrid";
import moment from "moment";
import UserDetailsCard from "../../../../Components/Dashboard/UserDetailsCard";

import { calculateDashboardStats, generateStatsGridData } from "./utils/dashboardCalculations";
// Removed cache and performance monitoring imports for simplified implementation
import OrganisationInformation from "../../../../Components/Dashboard/OrganisationInformation";

// Memoized child components for better performance
const MemoizedStatsGrid = memo(StatsGrid);
const MemoizedUserDetailsCard = memo(UserDetailsCard);
const MemoizedOrganisationInformation = memo(OrganisationInformation);

const { Text } = Typography;

const StandardDashboard = (props) => {
  const { pouchDatabase, databasePrefix, currentUser, modulesProperties } = props;

  // State management
  const [loading, setLoading] = useState(true);

  // Refs for cleanup
  const abortControllerRef = useRef(null);

  const [incomeData, setIncomeData] = useState([
    {
      title: "Sales this month",
      icon: "receipt",
      value: 0,
      diff: 0,
    },
    {
      title: "Sales this Week",
      icon: "coin",
      value: "0",
      diff: 0,
    },
    {
      title: "Expence this month",
      icon: "discount",
      value: "0",
      diff: 0,
    },
    {
      title: "expence this week",
      icon: "user",
      value: "0",
      diff: 0,
    },
  ]);
  const [recentSales, setRecentSales] = useState([]);
  const [recentPurchases, setRecentPurchases] = useState([]);

  // Main data loading effect - simplified
  useEffect(() => {
    let isMounted = true;

    // Abort any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

    // Fetch dashboard data
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const [invoices, expenses, purchases, products] = await Promise.all([
          pouchDatabase("invoices", databasePrefix).getAllData(),
          pouchDatabase("expenses", databasePrefix).getAllData(),
          pouchDatabase("stock_purchasing", databasePrefix).getAllData(),
          pouchDatabase("products", databasePrefix).getAllData(),
        ]);

        if (isMounted && !signal.aborted) {
          // Process the data
          const stats = calculateDashboardStats(invoices, expenses, SELECTED_BRANCH);
          const statsGridData = generateStatsGridData(stats);

          // Filter data by branch
          const filteredSales = SELECTED_BRANCH && SELECTED_BRANCH !== "none"
            ? invoices.filter(i => i.branch === SELECTED_BRANCH)
            : invoices;

          const filteredPurchases = SELECTED_BRANCH && SELECTED_BRANCH !== "none"
            ? purchases.filter(i => i.branch === SELECTED_BRANCH)
            : purchases;

          const recentSalesData = filteredSales
            .sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf())
            .slice(0, 2);

          const recentPurchasesData = filteredPurchases
            .sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf())
            .slice(0, 10);

          // Update state
          setIncomeData(statsGridData);
          setRecentSales(recentSalesData);
          setRecentPurchases(recentPurchasesData);
          setLoading(false);
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          if (isMounted) {
            setLoading(false);
          }
        }
      }
    };

    fetchDashboardData();

    return () => {
      isMounted = false;
      // Abort any ongoing requests when component unmounts or dependencies change
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [pouchDatabase, databasePrefix]);

  const renderTransactionList = (transactions, type) => {
    const calculateTotal = (items) => {
      if (!Array.isArray(items)) return 0;
      return items.reduce((sum, item) =>
        sum + ((item.price || 0) * (item.quantity || 0)), 0
      );
    };

    return (
      <List
        size="small"
        dataSource={transactions}
        renderItem={transaction => {
          const items = Array.isArray(transaction.items) ? transaction.items : [];
          const total = calculateTotal(items);

          return (
            <List.Item>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>
                    {type === 'sale'
                      ? transaction.customer?.label || 'Walk-in Customer'
                      : transaction.supplier?.label || 'Unknown Supplier'
                    }
                  </Text>
                  <Text type="secondary">
                    {moment(transaction.date).format('DD MMM YYYY')}
                  </Text>
                </Space>
                <List
                  size="small"
                  dataSource={items}
                  renderItem={item => (
                    <List.Item>
                      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                        <Text>{item.product?.label || item.item?.label || 'Unknown Item'}</Text>
                        <Space>
                          <Text>{item.quantity}x</Text>
                          {type === 'sale' && (
                            <Tag color="green">
                              UGX {((item.price || 0) * (item.quantity || 0)).toLocaleString()}
                            </Tag>
                          )}
                        </Space>
                      </Space>
                    </List.Item>
                  )}
                />
                <Divider style={{ margin: '8px 0' }} />
                {type === 'sale' && (
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <Text strong>Total</Text>
                    <Tag color="green">
                      UGX {total.toLocaleString()}
                    </Tag>
                  </Space>
                )}
              </Space>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <Row gutter={[5, 5]}>

      <Col span={24}>
        <MemoizedStatsGrid data={incomeData} loading={loading} />
      </Col>

      <Col span={24}>
        <Card>
          <Row gutter={16}>
            <Col span={12}>
              <Text strong style={{ fontSize: 16, marginBottom: 16, display: 'block' }}>
                Recent Sales
              </Text>
              {renderTransactionList(recentSales, 'sale')}
            </Col>
            <Col span={12}>
              <Text strong style={{ fontSize: 16, marginBottom: 16, display: 'block' }}>
                Recent Purchases
              </Text>
              {renderTransactionList(recentPurchases, 'purchase')}
            </Col>
          </Row>
        </Card>
      </Col>
      <Col span={24}>
        <MemoizedUserDetailsCard currentUser={currentUser} />
      </Col>

      {/* Organization Information */}
      <Col span={24}>
        <MemoizedOrganisationInformation
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
        />
      </Col>
    </Row>
  );
};

export default StandardDashboard;




