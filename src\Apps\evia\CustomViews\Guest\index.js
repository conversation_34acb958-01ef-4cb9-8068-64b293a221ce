import {
  Descriptions,
  Divider,
  <PERSON>,
  Typography,
  Button,
  Statistic,
  Tabs,
} from "antd";
import { PageHeader } from "@ant-design/pro-layout";
import React from "react";
import { DownloadOutlined } from "@ant-design/icons";
import ViewTable from "../../../../Components/ViewTable";
import moment from "moment";

const { Text, Link } = Typography;
const Guest = (props) => {
  const { data } = props;

  const sharedProps = {
    modules: props.modules,
    modulesProperties: props.modulesProperties,
    databasePrefix: props.databasePrefix,
    pouchDatabase: props.pouchDatabase,
    userPermissions: props.userPermissions,
    filterID: { column: "guest", id: props.data._id },
  };

  const { TabPane } = Tabs;

  const renderContent = (column = 2) => (
    <>
      <Descriptions size="small" labelStyle={{ fontWeight: 600 }}>
        <Descriptions.Item label="Phone">{data.phone}</Descriptions.Item>
        <Descriptions.Item label="Mobile">{data.mobile}</Descriptions.Item>
        <Descriptions.Item label="Email">{data.email}</Descriptions.Item>
        <Descriptions.Item label="Address">{data.address}</Descriptions.Item>
      </Descriptions>
      {/* <Divider /> */}
    </>
  );

  const Content = ({ children, extra }) => (
    <div className="content">
      <div className="main">{children}</div>
      <div className="extra">{extra}</div>
    </div>
  );

  return (
    <PageHeader
      className="site-page-header-responsive"
      onBack={() => window.history.back()}
      title={`${data.title==="None"?"":data.title+'.'} ${data.sur_name} ${data.first_name}`}
      // extra={[
      //   <Button key="1" type="primary" icon={<DownloadOutlined />}>
      //     Download Report
      //   </Button>,
      // ]}
      footer={
        <Tabs defaultActiveKey="1" type="card">
          <TabPane tab="Bar & Restaurant Orders" key="2">
            <ViewTable
              {...sharedProps}
              removeColumns={["createdAt"]}
              {...props.modules.orders}
              {...props.modulesProperties.orders}
            />
          </TabPane>
        </Tabs>
      }
    >
      <Content>{renderContent()}</Content>
    </PageHeader>
  );
};

export default Guest;
