/**
 * Windows-specific database adapter fix
 * This file handles the fallback chain for database adapters on Windows
 */
const PouchDB = require('pouchdb');
const PouchDBFind = require('pouchdb-find');
const fs = require('fs');
const path = require('path');

// Initialize essential plugins
PouchDB.plugin(PouchDBFind);

// Windows-specific adapter loading with better error handling
function setupAdapters() {
  // Track which adapters are available
  const adapters = {
    memory: false,
    websql: false,
    leveldb: false
  };

  // Always load memory adapter first (guaranteed to work)
  try {
    PouchDB.plugin(require('pouchdb-adapter-memory'));
    adapters.memory = true;
    console.log('✅ Memory adapter loaded successfully');
  } catch (error) {
    console.error('❌ Critical: Memory adapter failed to load:', error.message);
  }

  // Try to load WebSQL adapter (fewer native dependencies)
  try {
    PouchDB.plugin(require('pouchdb-adapter-node-websql'));
    adapters.websql = true;
    console.log('✅ WebSQL adapter loaded successfully');
  } catch (error) {
    console.warn('⚠️ WebSQL adapter failed to load:', error.message);
  }

  // Try to load LevelDB adapter last (most likely to fail on Windows)
  try {
    PouchDB.plugin(require('pouchdb-adapter-leveldb'));
    adapters.leveldb = true;
    console.log('✅ LevelDB adapter loaded successfully');
  } catch (error) {
    console.warn('⚠️ LevelDB adapter failed to load:', error.message);
  }

  return adapters;
}

// Create a database with fallback chain
function createDatabase(dbPath, name) {
  // Try memory first (guaranteed to work)
  try {
    const memoryDb = new PouchDB(name, { adapter: 'memory' });
    console.log(`✅ Memory database created for ${name}`);
    console.warn(`⚠️ WARNING: Using in-memory database for ${name}. Data will be lost on app restart!`);
    return { 
      db: memoryDb, 
      adapter: 'memory',
      warning: 'Using in-memory database. Data will be lost on app restart!'
    };
  } catch (memoryError) {
    console.error(`❌ Memory database creation failed for ${name}:`, memoryError.message);
  }

  // We should never reach here if memory adapter is working
  throw new Error(`Failed to create database with any adapter for ${name}`);
}

module.exports = {
  setupAdapters,
  createDatabase
};