import React, { useState, useMemo } from 'react';
import { Table, Button, Row, Col, Input, Typography, DatePicker, Select } from 'antd';
import { PrinterOutlined } from '@ant-design/icons';
import PrintComponents from 'react-print-components';
import DocumentHead from '../../../Universal/CustomViews/Components/DocumentHead';
import { useReportsData } from './ReportsContext';
import './print.css';
import { formatNumber } from '../../../../Utils/functions';
import dayjs from 'dayjs';
import moment from 'moment';

const { RangePicker } = DatePicker;

const Purchases = () => {
  const { purchases, products, categories, loading, company } = useReportsData();
  const [keyword, setKeyword] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('month'),
    dayjs().endOf('month')
  ]);

  const processedData = useMemo(() => {
    if (!purchases?.length) return [];

    // Create a map of products by ID for faster lookup
    const productsMap = new Map();
    products?.forEach(product => {
      productsMap.set(product._id, product);
    });

    // Create a map of categories by ID for faster lookup
    const categoriesMap = new Map();
    categories?.forEach(category => {
      categoriesMap.set(category._id, category);
    });

    const itemsList = [];
    purchases
      .filter(purchase => {
        // Ensure dateRange and its elements are valid dayjs objects before calling $d
        const startDate = dateRange && dateRange[0] ? dateRange[0] : dayjs('1970-01-01'); // Or some other default
        const endDate = dateRange && dateRange[1] ? dateRange[1] : dayjs(); // Or some other default

        return moment(purchase.date).isSameOrAfter(moment(startDate.$d || startDate.toDate()), "day") &&
          moment(purchase.date).isSameOrBefore(moment(endDate.$d || endDate.toDate()), "day");
      })
      .forEach(purchase => {
        purchase.items?.forEach((item, index) => {
          // Look up the full product information
          const productId = item.product?.value;
          const product = productId ? productsMap.get(productId) : null;

          // Get category information if available
          const categoryId = product?.category?.value;
          const category = categoryId ? categoriesMap.get(categoryId) : null;

          itemsList.push({
            key: `${purchase._id}-${index}`, // Unique key for each item row
            purchase_date: purchase.date,
            purchase_no: purchase.key || purchase._id,
            supplier: purchase.supplier?.label,
            item_name: item.product.label || 'N/A', // Assuming item.name, provide fallback
            item_sku: product?.sku || 'N/A', // Get SKU from the full product object
            item_category: category?.name || product?.category?.label || 'N/A', // Get category from the full category object
            item_quantity: item.quantity,
            item_price: item.price,
            item_total_amount: (item.quantity || 0) * (item.price || 0),
          });
        });
      });
    return itemsList;
  }, [purchases, products, categories, dateRange]);

  const columns = [
    {
      title: 'No',
      width: 50,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Date',
      dataIndex: 'purchase_date',
      render: date => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'LPO No',
      dataIndex: 'purchase_no',
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
    },
    {
      title: 'Item Name',
      dataIndex: 'item_name',
    },
    {
      title: 'SKU',
      dataIndex: 'item_sku',
    },
    {
      title: 'Category',
      dataIndex: 'item_category',
    },
    {
      title: 'Qty',
      dataIndex: 'item_quantity',
      align: 'right',
    },
    {
      title: 'Price',
      dataIndex: 'item_price',
      align: 'right',
      render: value => formatNumber(value),
    },
    {
      title: 'Amount',
      dataIndex: 'item_total_amount',
      align: 'right',
      render: value => formatNumber(value),
    }
  ];

  const filteredData = useMemo(() => {
    if (!processedData?.length) return [];

    let filtered = processedData;

    // Apply keyword search filter
    if (keyword) {
      const searchLower = keyword.toLowerCase();
      filtered = filtered.filter(record =>
        JSON.stringify(record).toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(record =>
        record.item_category === selectedCategory
      );
    }

    // Sort by purchase date, then by item name within the same purchase (optional, but good for consistency)
    return filtered.sort((a, b) => {
      const dateComparison = dayjs(b.purchase_date).unix() - dayjs(a.purchase_date).unix();
      if (dateComparison !== 0) {
        return dateComparison;
      }
      // If dates are the same, sort by purchase number, then item name
      if (a.purchase_no < b.purchase_no) return -1;
      if (a.purchase_no > b.purchase_no) return 1;
      if (a.item_name < b.item_name) return -1;
      if (a.item_name > b.item_name) return 1;
      return 0;
    });
  }, [processedData, keyword, selectedCategory]);

  // Prepare category options for the dropdown
  const categoryOptions = useMemo(() => {
    // Get unique categories from the processed data
    const uniqueCategories = new Set();
    processedData.forEach(item => {
      if (item.item_category && item.item_category !== 'N/A') {
        uniqueCategories.add(item.item_category);
      }
    });

    // Convert to array and sort alphabetically
    return Array.from(uniqueCategories).sort().map(category => ({
      label: category,
      value: category
    }));
  }, [processedData]);

  const tableProps = {
    size: 'small',
    columns,
    dataSource: filteredData,
    loading,
    pagination: {
      defaultPageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    scroll: { y: 'calc(100vh - 350px)' },
    summary: (pageData) => {
      const totalItemAmount = pageData.reduce((sum, row) => sum + (row.item_total_amount || 0), 0);

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {/* Columns: No, Date, LPO No, Supplier, Item, SKU, Category, Qty, Price, Amount (10 cols) */}
            <Table.Summary.Cell colSpan={9}>Total</Table.Summary.Cell>
            <Table.Summary.Cell align="right">{formatNumber(totalItemAmount)}</Table.Summary.Cell>
          </Table.Summary.Row>
        </Table.Summary>
      );
    }
  };

  return (
    <div>
      <Table
        {...tableProps}
        title={() => (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Input.Search
                placeholder="Search..."
                onChange={(e) => setKeyword(e.target.value)}
                style={{ width: '100%' }}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8}>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Select
                    placeholder="Filter by Category"
                    style={{ width: '100%' }}
                    allowClear
                    options={categoryOptions}
                    onChange={setSelectedCategory}
                    value={selectedCategory}
                  />
                </Col>
                <Col span={12}>
                  <RangePicker
                    value={dateRange}
                    onChange={setDateRange}
                    style={{ width: '100%' }}
                  />
                </Col>
              </Row>
            </Col>
            <Col xs={24} sm={8} style={{ textAlign: 'right' }}>
              <PrintComponents
                trigger={
                  <Button
                    icon={<PrinterOutlined />}
                    type="primary"
                  >
                    Print
                  </Button>
                }
              >
                <div style={{ margin: 20 }}>
                  {company && <DocumentHead company={company} />}
                  <Typography.Title level={3}>Purchase Ledger</Typography.Title>
                  {dateRange && dateRange[0] && <Typography.Text>
                    Period: {dateRange[0].format('DD/MM/YYYY')} - {dateRange[1].format('DD/MM/YYYY')}
                  </Typography.Text>
                  }
                  <Table
                    {...tableProps}
                    pagination={false}
                    scroll={false}
                  />
                </div>
              </PrintComponents>
            </Col>
          </Row>
        )}
      />
    </div>
  );
};

export default Purchases;
