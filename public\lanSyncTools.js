/**
 * LAN Sync Analysis Tools
 * Load this script in browser console to access LAN sync analysis tools
 */

// Import the analysis tools dynamically
async function loadLanSyncTools() {
  try {
    console.log('🔧 Loading LAN Sync Analysis Tools...');
    
    // Import the analyzer and checker modules
    const { default: lanSyncAnalyzer } = await import('/src/Utils/lanSyncAnalyzer.js');
    const { default: dataConsistencyChecker } = await import('/src/Utils/dataConsistencyChecker.js');
    
    // Make available globally
    window.lanSyncAnalyzer = lanSyncAnalyzer;
    window.dataConsistencyChecker = dataConsistencyChecker;
    
    // Convenience methods
    window.startLanAnalysis = () => lanSyncAnalyzer.startMonitoring();
    window.stopLanAnalysis = () => lanSyncAnalyzer.stopMonitoring();
    window.getLanReport = () => lanSyncAnalyzer.generateReport();
    window.resetLanAnalysis = () => lanSyncAnalyzer.reset();
    window.compareSyncSystems = () => lanSyncAnalyzer.compareWithOptimizedSync();
    window.runComprehensiveTest = (collections, prefix) => lanSyncAnalyzer.runComprehensiveTest(collections, prefix);
    
    window.checkDataConsistency = (collection, prefix = '') => 
      dataConsistencyChecker.checkCollection(collection, prefix);
    
    window.checkAllCollections = (collections, prefix = '') => 
      dataConsistencyChecker.checkMultipleCollections(collections, prefix);
    
    console.log('✅ LAN Sync Analysis Tools loaded successfully!');
    console.log('\n📋 Available Commands:');
    console.log('  🔍 ANALYSIS:');
    console.log('    • startLanAnalysis() - Start monitoring sync performance');
    console.log('    • stopLanAnalysis() - Stop monitoring');
    console.log('    • getLanReport() - Generate performance report');
    console.log('    • resetLanAnalysis() - Reset metrics');
    console.log('    • compareSyncSystems() - Compare current vs optimized');
    console.log('    • runComprehensiveTest([collections], prefix) - Full test suite');
    console.log('\n  📊 DATA CONSISTENCY:');
    console.log('    • checkDataConsistency(collection, prefix) - Check single collection');
    console.log('    • checkAllCollections([collections], prefix) - Check multiple collections');
    console.log('\n  🎯 QUICK START:');
    console.log('    • runComprehensiveTest(["expense_categories"], "zenwrench_m9jr8r_")');
    console.log('    • checkDataConsistency("expense_categories", "zenwrench_m9jr8r_")');
    
  } catch (error) {
    console.error('❌ Failed to load LAN Sync Analysis Tools:', error);
    console.log('💡 Make sure you are running this in the app context');
  }
}

// Auto-load if in browser environment
if (typeof window !== 'undefined') {
  loadLanSyncTools();
} else {
  console.log('📋 To load LAN Sync Analysis Tools, run: loadLanSyncTools()');
}
