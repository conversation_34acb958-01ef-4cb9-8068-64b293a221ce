import moment from "moment";
import checkins from "./checkins";
export default {
  name: "Visit Extensions",
  icon: "CalendarOutlined",
  path: "hotel/extensions",
  collection: "extensions",
  singular: "Visit Extension",
  parent: "hotel",
  removeCreate: true,
  columns: [
    {
      type: "dbSelect",
      isRequired: true,
      dataIndex: "checkin",
      collection: checkins.collection,
      isPrintable: true,
      title: "Check In",
      label: ["guest.label", "-", "_id"],
      width: "lg",
    },
    {
      title: "Departure Date",
      dataIndex: "departure_date",
      isPrintable: true,
      sorter: true,
      valueType: "date",
      width: "lg",
      colProps: {
        md: 12,
      },
    },
    {
      valueType: "dependency",
      hideInTable: true,
      colProps: {
        md: 4,
      },
      fieldProps: {
        name: ["checkin", "departure_date", "rate"],
      },
      columns: function columns({ checkin, departure_date, rate }) {
        return [
          {
            title: "Extended nights",
            dataIndex: "nights",
            type: "digit",
            fieldProps: {
              disabled: true,
              value: moment(departure_date)
                .startOf("day")
                .diff(moment(checkin.departure_date).startOf("day"), "days"),
            },
          },
          {
            title: "Amount",
            dataIndex: "amount",
            isRequired: true,
            type: "digit",
            fieldProps: {
              disabled: true,
              value:
                (rate ? rate : checkin.room_rate) *
                moment(departure_date)
                  .startOf("day")
                  .diff(moment(checkin.departure_date).startOf("day"), "days"),
            },
          },
        ];
      },
    },
  ],
};
