const {
  app,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ipc<PERSON><PERSON>,
  ipc<PERSON><PERSON><PERSON>,
  dialog,
} = require("electron");
const log = require("electron-log");
const { autoUpdater, AppUpdater } = require("electron-updater");

autoUpdater.logger = log;
autoUpdater.autoDownload = true;
autoUpdater.disableWebInstaller = true;
autoUpdater.autoInstallOnAppQuit = true;

class AutoUpdater {
  constructor() {
    app.whenReady().then(() => {
      autoUpdater.checkForUpdates();
      log.info(`Checking for updates. Current version ${app.getVersion()}`);
    });

    /*New Update Available*/
    autoUpdater.on("update-available", (info) => {
      log.info(`Update available. Current version ${app.getVersion()}`);
      let pth = autoUpdater.downloadUpdate();
      log.info(pth);
    });

    autoUpdater.on("update-not-available", (info) => {
      log.info(`No update available. Current version ${app.getVersion()}`);
    });

    /*Download Completion Message*/
    autoUpdater.on("update-downloaded", (info) => {
      log.info(`Update downloaded. Current version ${app.getVersion()}`);
    });

    // autoUpdater.on("update-downloaded", (_event, releaseNotes, releaseName) => {
    //   log.info(`Update downloaded. Current version ${app.getVersion()}`);
    //   const dialogOpts = {
    //     type: "info",
    //     buttons: ["Restart", "Later"],
    //     title: "Application Update",
    //     message: process.platform === "win32" ? releaseNotes : releaseName,
    //     detail:
    //       "A new version has been downloaded. Restart the application to apply the updates.",
    //   };

    //   dialog.showMessageBox(dialogOpts).then((returnValue) => {
    //     if (returnValue.response === 0) autoUpdater.quitAndInstall();
    //   });
    // });

    autoUpdater.on("download-progress", (progressObj) => {
      const { percent, transferred, total } = progressObj;
      log.info(`Download progress: ${percent}% (${transferred}/${total})`);
    });

    autoUpdater.on("error", (info) => {
      log.info(info);
    });
  }

  initialize() {
    autoUpdater.setFeedURL({
      url: "https://github.com/haclab-co/lenkit-releases/releases",
    });

    app.on("ready", () => {
      console.log("in");
      this.checkForUpdates();
    });

    autoUpdater.on("checking-for-update", () => {
      console.log("Checking for updates...");
    });

    autoUpdater.on("update-available", () => {
      console.log("Update available. Downloading...");
    });

    autoUpdater.on("update-not-available", () => {
      console.log("No updates available.");
    });

    autoUpdater.on("update-downloaded", () => {
      console.log("Update downloaded. Prompting to install...");
      this.quitAndInstall();
    });
  }

  checkForUpdates() {
    autoUpdater.checkForUpdates();
  }

  quitAndInstall() {
    autoUpdater.quitAndInstall();
  }
}

module.exports = AutoUpdater;
