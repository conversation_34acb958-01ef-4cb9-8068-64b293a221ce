#!/usr/bin/env node

/**
 * Test script to verify the Windows database fix
 * This script simulates the database initialization process
 */

const path = require('path');
const fs = require('fs');

console.log('🧪 Testing Windows Database Fix...');
console.log('Platform:', process.platform);
console.log('Node.js version:', process.version);
console.log('='.repeat(50));

// Mock Electron app object for testing
const mockApp = {
  getPath: (name) => {
    const os = require('os');
    const path = require('path');
    if (name === 'userData') {
      return path.join(os.tmpdir(), 'test-electron-app');
    }
    return os.tmpdir();
  }
};

// Test 1: Check if the DatabaseHandler can be required without crashing
console.log('\n📋 Test 1: Loading DatabaseHandler module...');
try {
  // Mock the electron app module before requiring DatabaseHandler
  const Module = require('module');
  const originalRequire = Module.prototype.require;

  Module.prototype.require = function(id) {
    if (id === 'electron') {
      return { app: mockApp };
    }
    return originalRequire.apply(this, arguments);
  };

  // Now require the DatabaseHandler
  const DatabaseHandler = require('../electron/database/DatabaseHandler');

  // Restore original require function
  Module.prototype.require = originalRequire;

  console.log('✅ DatabaseHandler loaded successfully');

  // Test 2: Check if we can create an instance
  console.log('\n📋 Test 2: Creating DatabaseHandler instance...');
  const handler = new DatabaseHandler();
  console.log('✅ DatabaseHandler instance created successfully');

  // Test 3: Check adapter availability
  console.log('\n📋 Test 3: Checking adapter availability...');
  console.log('Available adapters should include memory adapter');

  // Test 4: Simulate database initialization
  console.log('\n📋 Test 4: Testing database initialization...');

  // Create a test database name
  const testDbName = 'test_db_' + Date.now();

  handler.initializeDatabase(testDbName, 'test_', null, 'test_branch')
    .then(result => {
      console.log('✅ Database initialization result:', result);

      if (result.success) {
        console.log('✅ Database initialized successfully');
        console.log('Adapter used:', result.adapter);
        console.log('Persistent storage:', result.persistent);

        if (result.warning) {
          console.log('⚠️ Warning:', result.warning);
        }

        // Test 5: Test basic operations
        console.log('\n📋 Test 5: Testing basic database operations...');

        const testDoc = {
          name: 'Test Document',
          type: 'test',
          timestamp: new Date().toISOString()
        };

        return handler.saveDocument(`test_${testDbName}`, testDoc, { name: 'test_user' });
      } else {
        throw new Error('Database initialization failed');
      }
    })
    .then(saveResult => {
      console.log('✅ Document save result:', saveResult);

      if (saveResult.id) {
        console.log('✅ Document saved successfully with ID:', saveResult.id);

        // Test retrieval
        return handler.getDocument(`test_${testDbName}`, saveResult.id);
      } else {
        throw new Error('Document save failed');
      }
    })
    .then(retrievedDoc => {
      if (retrievedDoc) {
        console.log('✅ Document retrieved successfully:', retrievedDoc.name);

        // Test get all documents
        return handler.getAllDocuments(`test_${testDbName}`);
      } else {
        throw new Error('Document retrieval failed');
      }
    })
    .then(allDocs => {
      console.log('✅ Retrieved all documents, count:', allDocs.length);

      // Cleanup
      console.log('\n📋 Test 6: Cleanup...');
      return handler.closeDatabase(`test_${testDbName}`);
    })
    .then(() => {
      console.log('✅ Database closed successfully');

      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('✅ The Windows database fix is working correctly');
      console.log('✅ The app should start without native module errors');

      if (process.platform === 'win32') {
        console.log('🪟 Windows-specific notes:');
        console.log('  - Using memory adapter for compatibility');
        console.log('  - Data will be lost on app restart (this is expected)');
        console.log('  - No native module compilation required');
      }

      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test failed:', error.message);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    });

} catch (error) {
  console.error('❌ Failed to load DatabaseHandler:', error.message);
  console.error('Stack trace:', error.stack);

  // Check if it's the specific native module error we're trying to fix
  if (error.message.includes('leveldown') ||
      error.message.includes('binding') ||
      error.message.includes('node-gyp')) {
    console.error('\n🚨 This is the exact error we are trying to fix!');
    console.error('💡 The fix should prevent this error from occurring');
  }

  process.exit(1);
}
