import AppDatabase from "../../../Utils/AppDatabase";
import { buffProducts } from "./utils";
import Product from "../CustomViews/Product";
import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { useRef, useState } from "react";
import { LabelFormatter } from "../../../Utils/functions";
import { Switch, Space, message } from "antd";

const products = {
  CustomView: (data) => <Product {...data} />,
  statistics: async () => {
    // Get the selected branch from localStorage
    const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

    // Get the showExternal value from localStorage or default to false
    const showExternal = localStorage.getItem("SHOW_EXTERNAL_PRODUCTS") === "true";

    // Get database prefix from localStorage or use default
    const databasePrefix = localStorage.getItem("DB_PREFIX") || "";

    const productsDB = AppDatabase("products", databasePrefix);
    const allProducts = await productsDB.getAllData();

    const products = await buffProducts(
      allProducts.map((r) => ({ key: r._id, ...r })),
      null, // pouchDatabase not needed here
      databasePrefix, // Pass the database prefix
      "products", // collection name
      SELECTED_BRANCH, // Pass the branch explicitly
      showExternal // Pass the showExternal flag
    );

    let stockValue = 0,
      estimatedRevenue = 0;

    products.map((product) => {
      // Calculate current stock with explicit number conversion
      const stoked = Number(product.stoked || 0);
      const sold = Number(product.sold || 0);
      const returned = Number(product.returned || 0);
      const returned_stock = Number(product.returned_stock || 0);
      const adjustments = Number(product.adjustments || 0);
      const cost = Number(product.cost || 0);
      const sell = Number(product.sell || 0);

      const currentStock = stoked - sold + returned - returned_stock + adjustments;

      // Log calculation for this specific module
      console.log(`PRODUCTS MODULE stock calculation for ${product.name || product._id} (${product._id}):`, {
        stoked,
        sold,
        returned,
        returned_stock,
        adjustments,
        currentStock
      });

      stockValue += currentStock * cost;
      estimatedRevenue += currentStock * sell;
    });

    return [
      { title: "Total Stock Value", value: stockValue },
      { title: "Total estimated Revenue", value: estimatedRevenue },
      {
        title: "Total estimated Profit",
        value: estimatedRevenue - stockValue,
        valueStyle: {
          color: estimatedRevenue - stockValue > 0 ? "#3f8600" : "#cf1322",
        },
      },
      {
        title: "Total estimated % Profit",
        value: ((estimatedRevenue - stockValue) / stockValue) * 100,
        valueStyle: {
          color: estimatedRevenue - stockValue > 0 ? "#3f8600" : "#cf1322",
        },
        suffix: "%",
      },
    ];
  },
  buffResults: (results, pouchDatabase, databasePrefix, collection, branchParam) => {
    // Get the showExternal value from localStorage or default to false
    const showExternal = localStorage.getItem("SHOW_EXTERNAL_PRODUCTS") === "true";
    return buffProducts(results, pouchDatabase, databasePrefix, collection, branchParam, showExternal);
  },

  // Add a TableFilter component to toggle external products
  TableFilter: ({ setTableFilter }) => {
    // Initialize state from localStorage
    const [showExternal, setShowExternal] = useState(
      localStorage.getItem("SHOW_EXTERNAL_PRODUCTS") === "true"
    );

    // Handle toggle change
    const handleToggleExternal = (checked) => {
      setShowExternal(checked);
      localStorage.setItem("SHOW_EXTERNAL_PRODUCTS", checked);

      // Force table refresh by updating a timestamp in the filter
      setTableFilter("updated_" + Date.now());
    };

    return (
      <Space style={{ marginRight: 16 }}>
        <span>Show External Products:</span>
        <Switch
          checked={showExternal}
          onChange={handleToggleExternal}
          checkedChildren="Yes"
          unCheckedChildren="No"
        />
      </Space>
    );
  },
  MoreActions: (props) => {
    const action = useRef();

    const {
      pouchDatabase,
      databasePrefix,
      record,
      CRUD_USER,
      modules,
    } = props;

    return (
      <TableDropdown
        key="actionGroup"
        menus={[
          {
            key: "Add Stock",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Close",
                    submitText: "Save",
                  },
                }}
                modalProps={{ centered: true }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Add Stock
                  </a>
                }
                title={"Add Stock"}
                destroyOnClose={true}
                layoutType="ModalForm"
                initialValues={{
                  product: {
                    ...record,
                    value: record._id,
                    label: LabelFormatter(
                      ["name", " - ", "measurements", "units"],
                      record
                    ).join(" "),
                  },
                }}
                onFinish={async (values) => {
                  try {
                    console.log('📦 Adding stock...');
                    console.log('📄 Stock values:', values);

                    // Validate required fields
                    if (!values.quantity || values.quantity <= 0) {
                      message.error("Please enter a valid quantity");
                      return false;
                    }

                    if (!values.cost_price || values.cost_price <= 0) {
                      message.error("Please enter a valid cost price");
                      return false;
                    }

                    await pouchDatabase(
                      modules.stock_purchasing.collection,
                      databasePrefix
                    ).saveDocument(values, CRUD_USER);

                    console.log('✅ Stock added successfully');
                    message.success('Stock added successfully');
                    return true;
                  } catch (error) {
                    console.error('Error adding stock:', error);
                    message.error(`Failed to add stock: ${error.message}`);
                    return false;
                  }
                }}
                columns={[...modules.stock_purchasing.columns]}
              />
            ),
          },
          {
            key: "Set Selling Price",
            name: (
              <BetaSchemaForm
                formRef={action}
                submitter={{
                  searchConfig: {
                    resetText: "Close",
                    submitText: "Save",
                  },
                }}
                modalProps={{ centered: true }}
                grid={true}
                trigger={
                  <a key="button" type="primary">
                    Set Selling Price
                  </a>
                }
                title={"Set Selling Price"}
                destroyOnClose={true}
                layoutType="ModalForm"
                initialValues={{
                  product: {
                    ...record,
                    value: record._id,
                    label: LabelFormatter(
                      ["name", " - ", "measurements", "units"],
                      record
                    ).join(" "),
                  },
                }}
                onFinish={async (values) => {
                  try {
                    console.log('💰 Setting selling price...');
                    console.log('📄 Price values:', values);

                    // Validate required fields
                    if (!values.price || values.price <= 0) {
                      message.error("Please enter a valid selling price");
                      return false;
                    }

                    await pouchDatabase(
                      modules.product_prices.collection,
                      databasePrefix
                    ).saveDocument(values, CRUD_USER);

                    console.log('✅ Selling price set successfully');
                    message.success('Selling price set successfully');
                    return true;
                  } catch (error) {
                    console.error('Error setting selling price:', error);
                    message.error(`Failed to set selling price: ${error.message}`);
                    return false;
                  }
                }}
                columns={[...modules.product_prices.columns]}
              />
            ),
          },
        ]}
      ></TableDropdown>
    );
  },
};

export default products;
