export default {
  name: "Housekeeping",
  icon: "UsergroupAddOutlined",
  path: "hotel/housekeeping",
  collection: "housekeeping",
  singular: "Housekeeping Record",
  parent: "hotel",
  removeCreate: true,
  columns: [
    {
      type: "dbSelect",
      collection: "rooms",
      label: ["number", "short_desc", " - ", "rate"],
      dataIndex: "room_number",
      isRequired: true,
      filterOptions: {
        dataIndex: "room_status",
        value: ["Occupied", "Booked", "House Use", "Out Of Order"],
        direction: "out",

        isPrintable: true,
      },
      title: "Room",
    },
    {
      title: "Cleaner",
      dataIndex: "cleaner",
      type: "dbSelect",
      isPrintable: true,
      collection: "users",
      label: ["first_name"],
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      hideInTable: true,
      isPrintable: true,
    },
  ],
};
