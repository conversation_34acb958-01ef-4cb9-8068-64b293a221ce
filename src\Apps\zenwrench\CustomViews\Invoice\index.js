import React, { useEffect, useState } from "react";
import "./css.css";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import PrintComponents from "react-print-components";
import { Float<PERSON><PERSON>on, Flex, Divider } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";
import DocumentFooter from "../DocumentFooter";

const Invoice = (props) => {
  const { data, title, bottomText = "", pouchDatabase, databasePrefix } = props;



  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS")) || {};

  const subTotal = data.items.reduce(
    (acc, item) => acc + item.price * item.quantity,
    0
  );
  const discount = data.discounted ? data.discount : 0;
  const tax = data.taxable ? subTotal * 0.18 : 0;
  const grandTotal = subTotal + tax - discount;

  const [company, setCompany] = useState(null);
  const [job, setJob] = useState(data.job);
  const [customer, setCustomer] = useState(null);
  const [vehicle, setVehicle] = useState(null);
  const [partsAndServices, setPartsAndServices] = useState(null);
  const [units, setUnits] = useState([]);

  useEffect(() => {
    const loadPartsAndServices = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const partsAndServicesData = await pouchDatabase("parts_and_services", databasePrefix).getAllData();
          setPartsAndServices(partsAndServicesData.map(doc => ({ doc })));
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setPartsAndServices([]);
        }
      } catch (error) {
        console.error("Error loading parts and services:", error);
        setPartsAndServices([]);
      }
    };

    loadPartsAndServices();
  }, [pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadJob = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined && data.job && data.job.value) {
          const jobData = await pouchDatabase("jobs", databasePrefix).getDocument(data.job.value);
          setJob(jobData);
        } else {
          console.error("pouchDatabase, databasePrefix, or job data not available");
        }
      } catch (error) {
        console.error("Error loading job:", error);
      }
    };

    loadJob();
  }, [data.job, pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadCustomer = async () => {
      if (!job || !job.customer) return;

      try {
        const customerId = job.customer.value || job.customer._id;

        if (pouchDatabase && databasePrefix !== undefined) {
          const customerData = await pouchDatabase("customers", databasePrefix).getDocument(customerId);
          setCustomer(customerData);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
        }
      } catch (error) {
        console.error("Error loading customer:", error);
      }
    };

    loadCustomer();
  }, [job, pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadVehicle = async () => {
      if (!job || !job.vehicle) return;

      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const vehicleData = await pouchDatabase("vehicles", databasePrefix).getDocument(job.vehicle.value);
          setVehicle(vehicleData);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
        }
      } catch (error) {
        console.error("Error loading vehicle:", error);
      }
    };

    loadVehicle();
  }, [job, pouchDatabase, databasePrefix]);

  useEffect(() => {
    const loadUnits = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const unitsData = await pouchDatabase("units", databasePrefix).getAllData();
          setUnits(unitsData);
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setUnits([]);
        }
      } catch (error) {
        console.error("Error loading units:", error);
        setUnits([]);
      }
    };

    loadUnits();
  }, [pouchDatabase, databasePrefix]);



  useEffect(() => {
    const loadCompanyData = async () => {
      try {
        if (pouchDatabase && databasePrefix !== undefined) {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            let comp = organizationsData[0];

            if (comp._attachments && comp._attachments.logo) {
              try {
                const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
                comp.logo = logoBlob;
                setCompany(comp);
              } catch (logoError) {
                console.warn("Error fetching logo:", logoError);
                setCompany(comp);
              }
            } else {
              setCompany(comp);
            }
          } else {
            console.warn("No organization data found, using fallback");
            setCompany({
              name: "Your Company",
              phone: "",
              alternative_phone: "",
              email: "",
              website: "",
              address: ""
            });
          }
        } else {
          console.error("pouchDatabase or databasePrefix not available");
          setCompany({
            name: "Your Company",
            phone: "",
            alternative_phone: "",
            email: "",
            website: "",
            address: ""
          });
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        setCompany({
          name: "Your Company",
          phone: "",
          alternative_phone: "",
          email: "",
          website: "",
          address: ""
        });
      }
    };

    loadCompanyData();
  }, [pouchDatabase, databasePrefix]);

  const emptyRows = [];

  for (
    let index = 0;
    index < (appSettings.documentHeader === "V2" ? 28 : 24) - data.items.length;
    index++
  ) {
    emptyRows.push({});
  }

  return !company || !customer || !vehicle ? null : (
    <div className="tm_invoice_wrap">
      <div className="tm_invoice tm_style1" id="tm_download_section">
        <div className={`tm_invoice_in ${job.job_type === "repeat_job" ? "repeat_job_watermark" : ""}`}>
          {appSettings &&
            appSettings.documentHeader &&
            appSettings.documentHeader === "V2" && (
              <>
                <Flex vertical={false} justify="space-between">
                  <RenderBlob blob={company.logo} size={150} />
                  <div>
                    <strong style={{ fontSize: 25, fontWeight: "bold" }}>
                      {title.toUpperCase()}
                    </strong>
                    <p class="tm_invoice_number tm_m0 tm_f11">
                      {title} No: <b class="tm_primary_color">{data._id}</b>
                      <br /> Date:{" "}
                      <b class="tm_primary_color">
                        {moment(data.date).format("DD MMM YYYY")}
                      </b>
                    </p>
                  </div>
                  <p class="tm_mb2 tm_f12">
                    {company.address}
                    <br />
                    Tel: {company.phone} / {company.alternative_phone} <br />
                    Email: {company.email} <br />
                    {company.website && company.website}
                  </p>
                </Flex>
                <Divider />
                <Flex vertical={false} justify="space-between">
                  <div class="tm_mb2 tm_f10">
                    <p class="tm_mb2">
                      <b class="tm_primary_color">To:</b>
                    </p>
                    <p>
                      <b style={{ fontSize: 15 }}>{customer.name}</b>
                      <br />
                      {customer.phone}{" "}
                      {customer.alternative_phone &&
                        `/ ${customer.alternative_phone}`}{" "}
                      <br />
                      {customer.email && (
                        <>
                          {customer.email} <br />
                        </>
                      )}
                      {customer.address && (
                        <>
                          {customer.address} <br />
                        </>
                      )}
                    </p>
                  </div>
                  <div class="tm_mb2 tm_f10">
                    {job.vehicle.label}
                    {job.millage && (
                      <>
                        <br />
                        Mileage :{" "}
                        <strong>{numberFormat(job.millage)} km</strong>
                      </>
                    )}
                  </div>
                </Flex>
              </>
            )}

          {(!appSettings.documentHeader ||
            appSettings.documentHeader === "V1") && (
              <Flex vertical={true}>
                <center>
                  <RenderBlob blob={company.logo} size={250} />
                  <p class="tm_f12">
                    {company.address}
                    <br />
                    Tel: {company.phone} / {company.alternative_phone} <br />
                    Email: {company.email} <br />
                    {company.website && company.website}
                  </p>
                </center>
                <Flex vertical={false} justify="space-between">
                  <div>
                    <p class="tm_mb2 tm_f11">
                      <b class="tm_primary_color">To:</b>
                    </p>
                    <p>
                      <b style={{ fontSize: 15 }}>{customer.name}</b>
                      <br />
                      {customer.phone}{" "}
                      {customer.alternative_phone &&
                        `/ ${customer.alternative_phone}`}{" "}
                      <br />
                      {customer.email && (
                        <>
                          {customer.email} <br />
                        </>
                      )}
                      {customer.address && (
                        <>
                          {customer.address} <br />
                        </>
                      )}
                      {customer._id && (
                        <>
                          Customer ID : <strong>{customer._id} <br /></strong>
                        </>
                      )}
                      <hr />

                      <>
                        {job.vehicle.label} |{" "}
                        {job.millage && (
                          <>
                            Mileage :{" "}
                            <strong>{numberFormat(job.millage)} km</strong>
                          </>
                        )}
                      </>
                    </p>
                  </div>
                  <div>
                    <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                      {title.toUpperCase()}
                    </strong>
                    <p class="tm_invoice_number tm_m0">
                      {title} No: <b class="tm_primary_color">{data._id}</b>
                    </p>
                    <p class="tm_invoice_date tm_m0">
                      Date:{" "}
                      <b class="tm_primary_color">
                        {moment(data.date).format("DD MMM YYYY")}
                      </b>
                    </p>
                    <p class="tm_invoice_number tm_m0">
                      Job ID: <b class="tm_primary_color">{job._id}</b>
                    </p>
                  </div>
                </Flex>
              </Flex>
            )}

          {/* <div class="tm_invoice_head tm_align_center tm_mb10">
            <div class="tm_invoice_left"></div>
            <div class="tm_invoice_right tm_text_right">
              <RenderBlob blob={company.logo} size={100} />
            </div>
          </div> */}

          {/* <div class="tm_invoice_info tm_mb20">
            <div>
              <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                {title.toUpperCase()}
              </strong>
            </div>
            <div class="tm_invoice_info_list">
              <p class="tm_invoice_number tm_m0">
                {title} No: <b class="tm_primary_color">#{data._id}</b>
              </p>
              <p class="tm_invoice_date tm_m0">
                Date:{" "}
                <b class="tm_primary_color">
                  {moment(data.created_at).format("DD MMM YYYY")}
                </b>
              </p>
            </div>
          </div> */}

          {/* <div class="tm_invoice_info tm_mb10">
            <div class="tm_invoice_left">
              <p class="tm_mb2 tm_f12">
                <b class="tm_primary_color">To:</b>
              </p>
              <p>
                <b style={{ fontSize: 15 }}>{job.customer.label}</b>
                <br />
                {job.customer.phone}{" "}
                {job.customer.alternative_phone &&
                  `/ ${job.customer.alternative_phone}`}{" "}
                <br />
                {job.customer.email && (
                  <>
                    {job.customer.email} <br />
                  </>
                )}
                {job.customer.address && (
                  <>
                    {job.customer.address} <br />
                  </>
                )}
                <hr />
                {job.vehicle.label}
              </p>
            </div>
            <div class="tm_invoice_right tm_text_right">
              <div style={{ display: "flex", justifyContent: "end" }}>
                <div style={{ marginLeft: 20 }}>
                  <p class="tm_mb2 tm_f12">
                    <b class="tm_f20 tm_primary_color">{company.name}</b>
                  </p>
                  <p>
                    {company.address}
                    <br />
                    Tel: {company.phone} / {company.alternative_phone} <br />
                    Email: {company.email} <br />
                    {company.website && company.website}
                  </p>
                </div>
              </div>
            </div>
          </div> */}

          {/* <div class="tm_invoice_info tm_mb20">
            <div class="tm_invoice_seperator">
              <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                {title.toUpperCase()}
              </strong>
            </div>
            <div class="tm_invoice_info_list">
              <p class="tm_invoice_number tm_m0">
                Invoice No: <b class="tm_primary_color">#{data._id}</b>
              </p>
              <p class="tm_invoice_date tm_m0">
                Date:{" "}
                <b class="tm_primary_color">
                  {moment(data.created_at).format("DD MMM YYYY")}
                </b>
              </p>
            </div>
          </div> */}

          {/* <div className="tm_invoice_head tm_mb20">
            <div className="tm_invoice_left">
              <div className="tm_logo tm_size1">
                <img src={company.logo} alt="Logo" />
                <RenderBlob blob={company.logo} size={100} />
              </div>
            </div>
            <div className="tm_invoice_right tm_text_right">
              <b className="tm_f20 tm_medium tm_primary_color">
                {company.name}
              </b>
              <p className="tm_m0 tm_f12">
                {company.address}
                <br />
                Tel: {company.phone} / {company.alternative_phone}
                <br />
                Email: {company.email}
              </p>
            </div>
          </div> */}

          {/* <hr className="tm_mb8" /> */}
          {/* <div className="tm_flex tm_flex_column_sm tm_justify_between tm_align_center tm_align_start_sm tm_medium tm_mb10">
            <p className="tm_m0">
              <b className="tm_primary_color">{data.job.customer.label}</b>
              <br />
              Tel: {data.job.customer.phone}
            </p>
            <p className="tm_m0">
              <b className="tm_primary_color">
                {data.job.vehicle.label.split(" - ")[0]}
              </b>
              <br />
              No: {data.job.vehicle.label.split(" - ")[1]}
            </p>
            <p className="tm_m0">
              {title} No: <br />
              <b className="tm_primary_color">{data._id}</b>
            </p>
            <p className="tm_m0">
              Date: <br />
              <b className="tm_primary_color">
                {moment(data.created_at).format("DD MMM YYYY")}
              </b>
            </p>
          </div> */}
          {/* <hr className="tm_mb20" /> */}
          {/* <div className="tm_box_3 tm_mb20">
            <center
              style={{ fontSize: 30, fontWeight: "bold", marginBottom: 0 }}
            >
              {title.toUpperCase()}
            </center>
          </div> */}
          <div className="tm_table tm_style">
            <center style={{ minHeight: 430 }}>
              <div
                className="tm_border"
              // style={{ width: 500 }}
              >
                <div className="main">
                  <table>
                    <thead>
                      <tr>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_center">
                          S/N
                        </th>
                        <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                          Description
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_left">
                          Qty
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Rate
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.items.map((item, index) => {
                        const PartUnit = partsAndServices.find(
                          (part) => part.id === item.item.value
                        );



                        const unit =
                          PartUnit && PartUnit.doc && PartUnit.doc.unit
                            ? units.find(
                              (u) => u._id === PartUnit.doc.unit.value
                            )?.abbreviation
                            : "";

                        return (
                          <tr className="main" key={`item-${index}`}>
                            <td className="tm_width_1 tm_text_center">
                              {index + 1}
                            </td>
                            <td className="tm_width_3">{item.item.label}</td>
                            <td className="tm_width_1 tm_text_left">
                              {item.quantity}{" "}
                              {item.quantity === 1 || unit === ""
                                ? unit
                                : unit + "s"}
                            </td>
                            <td className="tm_width_1 tm_text_right">
                              {numberFormat(item.price)}
                            </td>
                            <td className="tm_width_1 tm_text_right">
                              {numberFormat(item.price * item.quantity)}
                            </td>
                          </tr>
                        );
                      })}
                      {emptyRows.map((_, index) => {
                        return (
                          <tr className="main" key={`empty-row-${index}`}>
                            <td className="tm_width_1 tm_text_center">
                              {" "}
                              &nbsp;
                            </td>
                            <td className="tm_width_3"></td>
                            <td className="tm_width_1 tm_text_left"></td>
                            <td className="tm_width_1 tm_text_right"></td>
                            <td className="tm_width_1 tm_text_right"></td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </center>
            <div className="tm_invoice_footer tm_mb20 tm_m0_md">
              <div className="tm_left_footer">
                {appSettings.mobile_wallet_settings && (
                  <p className="tm_mb2 tm_f12">
                    <b className="tm_primary_color">
                      Mobile Wallet Payment Method:{" "}
                    </b>
                    <br />
                    {appSettings.mobile_wallet_settings.map((item, index) => (
                      <div key={`wallet-${index}`}>
                        {item.mobile_wallet} : <b>{item.id}</b>
                      </div>
                    ))}
                    <br />
                  </p>
                )}
                {appSettings.acc_no && (
                  <p className="tm_m0 tm_f12">
                    <b className="tm_primary_color">Bank Details: </b>
                    <br />
                    {appSettings.acc_name} : <b>{appSettings.acc_no}</b>
                    <br />
                    {appSettings.bank_name} - {appSettings.bank_address} Branch
                  </p>
                )}
              </div>
              <div className="tm_right_footer">
                <table>
                  <tbody>
                    <tr>
                      <td className="tm_width_3 tm_primary_color tm_border_none tm_bold tm_f15">
                        Subtotal
                      </td>
                      <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold tm_f15">
                        {appSettings.currency} {numberFormat(subTotal)}
                      </td>
                    </tr>
                    <tr>
                      <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                        Tax <span className="tm_ternary_color">(18%)</span>
                      </td>
                      <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                        {appSettings.currency} {numberFormat(tax)}
                      </td>
                    </tr>
                    {data.discounted && (
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                          Discount
                          {data.discount_description && (
                            <span className="tm_secondary_color">{` (${data.discount_description})`}</span>
                          )}
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                          {appSettings.currency} {numberFormat(discount)}
                        </td>
                      </tr>
                    )}
                    <tr className="tm_border_top tm_border_bottom">
                      <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color">
                        Grand Total{" "}
                      </td>
                      <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color tm_text_right">
                        {appSettings.currency} {numberFormat(grandTotal)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <center>
              <strong style={{ fontSize: "11px" }}>{bottomText}</strong>
            </center>
            <div className="tm_invoice_footer tm_type1">
              <DocumentFooter />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PrintableInvoice = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <Invoice {...props} />
      </PrintComponents>
      <Invoice {...props} />
    </>
  );
};

export default PrintableInvoice;
