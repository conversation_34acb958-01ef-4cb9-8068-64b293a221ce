import { Card, Col, Descriptions, Divider, Image, Row, Space } from "antd";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { formatMoney } from "../../../../Utils/functions";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";
import { fetchCurrentOrganization } from "../../../../Utils/organizationUtils";

var Visit = function Visit(props) {
  var data = props.data,
    pouchDatabase = props.pouchDatabase,
    databasePrefix = props.databasePrefix;

  var _useState = useState({}),
    guest = _useState[0],
    setGuest = _useState[1];

  var _useState2 = useState({}),
    room = _useState2[0],
    setApartment = _useState2[1];

  var organization = JSON.parse(
    localStorage.getItem(LOCAL_STORAGE_ORGANIZATION)
  );

  var _useState3 = useState(null),
    companyLogo = _useState3[0],
    setCompanyLogo = _useState3[1];

  var _useState4 = useState(null),
    dbOrganization = _useState4[0],
    setDbOrganization = _useState4[1];

  useEffect(function () {
    pouchDatabase("organizations", databasePrefix)
      .getAttachment(organization._id, "logo")
      .then(function (res) {
        return setCompanyLogo(res);
      });
  }, []);
  useEffect(function () {
    // Use utility function for efficient organization fetching
    fetchCurrentOrganization(pouchDatabase, "organizations", databasePrefix)
      .then(function (organization) {
        setDbOrganization(organization);
      })
      .catch(function (error) {
        console.error("Error fetching organization:", error);
        setDbOrganization(null);
      });
  }, []);
  useEffect(function () {
    data.guest &&
      pouchDatabase("guests", databasePrefix)
        .getDocument(data.guest.value)
        .then(function (res) {
          setGuest(res);
        });
  }, []);
  useEffect(function () {
    data.guest &&
      pouchDatabase("rooms", databasePrefix)
        .getDocument(data.room_number.value)
        .then(function (res) {
          setApartment(res);
        });
  }, []);

  return /*#__PURE__*/ React.createElement(
    "div",
    null,
    dbOrganization &&
      /*#__PURE__*/ React.createElement(
        Row,
        {
          gutter: 24,
          style: {
            marginTop: 18,
          },
        },
        /*#__PURE__*/ React.createElement(
          Col,
          {
            span: 12,
            style: {
              textAlign: "left",
            },
          },
          /*#__PURE__*/ React.createElement("h3", null, dbOrganization.name),
          dbOrganization.alternative_name &&
            /*#__PURE__*/ React.createElement(
              "h3",
              null,
              dbOrganization.alternative_name
            ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            dbOrganization.address
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Phone no: "),
            dbOrganization.phone
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Email: "),
            dbOrganization.email
          ),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "website: "),
            dbOrganization.website
          )
        ),
        /*#__PURE__*/ React.createElement(
          Col,
          {
            span: 12,
            style: {
              textAlign: "right",
            },
          },
          /*#__PURE__*/ React.createElement(Image, {
            width: 80,
            src: companyLogo,
          }),
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement(
            "div",
            null,
            /*#__PURE__*/ React.createElement("strong", null, "Check In No.: "),
            data.ref_number ? `${data._id} - ${data.ref_number}` : data._id,
            /*#__PURE__*/ React.createElement("br", null),
            /*#__PURE__*/ React.createElement("strong", null, "Print date: "),
            moment(new Date()).format("DD MMM YYYY HH:mm")
          )
        )
      ),
    /*#__PURE__*/ React.createElement(
      "center",
      {
        align: "center",
        style: {
          fontSize: "20px",
          marginTop: "5px",
          marginBottom: "5px",
        },
      },
      /*#__PURE__*/ React.createElement("u", null, "Guest Registration Form")
    ),
    /*#__PURE__*/ React.createElement(
      Descriptions,
      {
        title: "Guest Information",
        size: "small",
        labelStyle: {
          fontWeight: 600,
        },
      },
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Name",
        },
        /*#__PURE__*/ React.createElement(
          "b",
          null,
          guest.title + ". " + guest.sur_name + " " + guest.first_name
        )
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Phone",
        },
        guest.phone
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Customer No.",
        },
        guest._id
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Email",
        },
        guest.email
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Mobile",
        },
        guest.mobile
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Address",
        },
        guest.address
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Date of Birth",
        },
        moment(guest.date_of_birth).format("DD MMM YYYY")
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Nationality",
        },
        guest.nationality && guest.nationality.value
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Designation",
        },
        guest.designation
      )
    ),
    /*#__PURE__*/ React.createElement(Divider, {
      style: {
        marginTop: "10px",
        marginBottom: "10px",
      },
    }),
    /*#__PURE__*/ React.createElement(
      Descriptions,
      {
        title: "ID Information",
        column: 2,
        size: "small",
        labelStyle: {
          fontWeight: 600,
        },
      },
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "ID",
        },
        guest.id_type
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "ID Number",
        },
        guest.id_number
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Place of Issue",
        },
        guest.country && guest.country.label
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Date of issue",
        },
        moment(guest.date_of_issue).format("DD MMM YYYY ")
      )
    ),
    /*#__PURE__*/ React.createElement(Divider, {
      style: {
        marginTop: "10px",
        marginBottom: "10px",
      },
    }),
    /*#__PURE__*/ React.createElement(
      Descriptions,
      {
        title: "Visit Information",
        size: "small",
        labelStyle: {
          fontWeight: 600,
        },
      },
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Room Number",
        },
        /*#__PURE__*/ React.createElement("b", null, room.number)
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Persons",
        },
        data.persons
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Company",
        },
        data.company && data.company.label
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Room Type",
        },
        room.short_desc
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Purpose of Visit",
        },
        data.purpose_of_visit
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Billing",
        },
        data.billing
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Car Number",
        },
        data.car_number
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Arrival Date",
        },
        moment(data.arrival_date).format("DD MMM YYYY  HH:mm")
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Departure Date",
        },
        (data.extensions
          ? moment(data.departure_date).add(
              data.extensions / data.room_rate,
              "days"
            )
          : moment(data.departure_date)
        ).format("DD MMM YYYY")
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Date of Arrival In UG",
        },
        moment(data.arrival_date).format("DD MMM YYYY")
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Proposed Duration of Stay In UG",
        },
        moment(data.departure_date)
          .startOf("day")
          .diff(moment(data.arrival_date).startOf("day"), "days") < 1
          ? 1
          : moment(data.departure_date)
              .startOf("day")
              .diff(moment(data.arrival_date).startOf("day"), "days")
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Room Transfer",
        },
        /*#__PURE__*/ React.createElement(
          "b",
          null,
          data.transfers
            ? data.transfers.reverse()[0].room_number.label
            : "None"
        )
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Number of Nights",
        },
        (moment(data.departure_date)
          .startOf("day")
          .diff(moment(data.arrival_date).startOf("day"), "days") < 1
          ? 1
          : moment(data.departure_date)
              .startOf("day")
              .diff(moment(data.arrival_date).startOf("day"), "days")) +
          (data.extensions ? data.extensions / data.room_rate : 0)
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Room Rate",
        },
        formatMoney(data.room_rate)
      ),
      /*#__PURE__*/ React.createElement(
        Descriptions.Item,
        {
          label: "Total Amount",
        },
        formatMoney(
          ((moment(data.departure_date)
            .startOf("day")
            .diff(moment(data.arrival_date).startOf("day"), "days") < 1
            ? 1
            : moment(data.departure_date)
                .startOf("day")
                .diff(moment(data.arrival_date).startOf("day"), "days")) +
            (data.extensions ? data.extensions / data.room_rate : 0)) *
            data.room_rate
        )
      )
    ),
    /*#__PURE__*/ React.createElement(
      Row,
      null,
      /*#__PURE__*/ React.createElement(
        Col,
        {
          span: 12,
        },
        /*#__PURE__*/ React.createElement(
          Card,
          null,
          /*#__PURE__*/ React.createElement("strong", null, "Membership Info"),
          /*#__PURE__*/ React.createElement(
            "ul",
            {
              style: {
                fontSize: "10px",
              },
            },
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              "Check out is 12 noon."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              " Vistors are not permitted in guest rooms after Mid-Night."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              " Management is not responsible for safety of any valuables left in the room."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              " All disputes are subject to the jurisdiction of Uganda Courts only."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              " All payments should be made from Monday to Friday from 8:00am to 5:00pm and on Saturday, from 8:00am to 1:00pm. Do not make any payments on Sunday"
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              "Payments have to be made before check in and no refunds will be made."
            )
          )
        )
      ),
      /*#__PURE__*/ React.createElement(
        Col,
        {
          span: 12,
        },
        /*#__PURE__*/ React.createElement(
          Card,
          null,
          /*#__PURE__*/ React.createElement(
            "ul",
            {
              style: {
                fontSize: "10px",
              },
            },
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              "I agree to pay all the charges incurred by me during my stay in the room, and to settle my account unless prior arrangement has been made."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              "To take full responsibility for injury through accident or otherwise or loss of property during stay."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              null,
              "Use of swimming pool is at your own risk. The management will not be held responsible for any accident there in."
            ),
            /*#__PURE__*/ React.createElement(
              "li",
              {
                style: {
                  paddingBottom: "5px",
                },
              },
              "Main swimming pool, Gym and Health Club is strictly out of bounds for the children under 18 years of age"
            )
          )
        )
      )
    ),
    /*#__PURE__*/ React.createElement(
      Row,
      {
        justify: "space-between",
      },
      /*#__PURE__*/ React.createElement(
        Col,
        {
          style: {
            textAlign: "center",
          },
        },
        /*#__PURE__*/ React.createElement(
          "div",
          null,
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement("br", null),
          "................................................",
          /*#__PURE__*/ React.createElement("br", null)
        ),
        /*#__PURE__*/ React.createElement("strong", null, "Guest's Signature")
      ),
      /*#__PURE__*/ React.createElement(
        Col,
        {
          style: {
            textAlign: "center",
          },
        },
        /*#__PURE__*/ React.createElement(
          "div",
          null,
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement("br", null),
          "................................................",
          /*#__PURE__*/ React.createElement("br", null)
        ),
        /*#__PURE__*/ React.createElement("strong", null, "Receptionist")
      ),
      /*#__PURE__*/ React.createElement(
        Col,
        {
          style: {
            textAlign: "center",
          },
        },
        /*#__PURE__*/ React.createElement(
          "div",
          null,
          /*#__PURE__*/ React.createElement("br", null),
          /*#__PURE__*/ React.createElement("br", null),
          "................................................",
          /*#__PURE__*/ React.createElement("br", null)
        ),
        /*#__PURE__*/ React.createElement("strong", null, "General Manager")
      )
    )
  );
};

export default Visit;
