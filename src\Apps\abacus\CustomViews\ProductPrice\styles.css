/* Product Price Custom View Styles */

.price-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.price-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.price-card .ant-card-head-title {
  font-weight: 600;
}

.ant-descriptions-item-label {
  font-weight: 500;
}

.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

.ant-timeline-item-content {
  width: 100%;
}

.ant-timeline-item-label {
  width: 120px !important;
}

.price-history-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.price-history-table th,
.price-history-table td {
  border: 1px solid #f0f0f0;
  padding: 12px 16px;
  text-align: left;
}

.price-increase {
  color: #52c41a;
}

.price-decrease {
  color: #f5222d;
}

.price-unchanged {
  color: inherit;
}
