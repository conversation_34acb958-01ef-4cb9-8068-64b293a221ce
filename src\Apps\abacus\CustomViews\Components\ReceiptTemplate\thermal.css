*,
::after,
::before {
  box-sizing: border-box;
}

.tm_pos_invoice_wrap {
  margin: auto;
  margin-top: 30px;
  padding: 30px 20px;
  background-color: #fff;
}

.tm_pos_company_logo {
  display: flex;
  justify-content: center;
  margin-bottom: 7px;
}

.tm_pos_company_logo img {
  max-width: 100%;
  height: auto;
  max-height: 45px;
}

.tm_pos_invoice_top {
  text-align: center;
  margin-bottom: 18px;
}

.tm_pos_invoice_heading {
  display: flex;
  justify-content: center;
  position: relative;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  margin: 10px 0;
}

.tm_pos_invoice_heading:before {
  content: '';
  position: absolute;
  height: 0;
  width: 100%;
  left: 0;
  top: 46%;
  border-top: 1px dashed #b5b5b5;
}

.tm_pos_invoice_heading span {
  display: inline-flex;
  padding: 0 5px;
  background-color: #fff;
  z-index: 1;
  font-weight: 500;
  color: #000;
}

.tm_pos_invoice_table {
  width: 100%;
  margin-top: 10px;
  line-height: 1.3em;
}

.tm_pos_invoice_table thead th {
  font-weight: 500;
  color: #000;
  text-align: left;
  padding: 8px 3px;
  border-top: 1px dashed #b5b5b5;
  border-bottom: 1px dashed #b5b5b5;
}

.tm_pos_invoice_table td {
  padding: 4px;
}

.tm_pos_invoice_table th:last-child,
.tm_pos_invoice_table td:last-child {
  text-align: right;
  padding-right: 0;
}

.tm_bill_list {
  list-style: none;
  margin: 0;
  padding: 8px 0;
  border-bottom: 1px dashed #b5b5b5;
}

.tm_bill_list_in {
  display: flex;
  text-align: right;
  justify-content: flex-end;
  padding: 3px 0;
}

.tm_bill_title {
  padding-right: 20px;
}

.tm_bill_value {
  width: 90px;
}

.tm_pos_invoice_footer {
  text-align: center;
  margin-top: 20px;
}

.tm_pos_sample_text {
  text-align: center;
  padding: 12px 0;
  border-bottom: 1px dashed #b5b5b5;
  line-height: 1.6em;
  color: #000;
  font-size: 12px;
}

.tm_pos_company_name {
  font-weight: 500;
  color: #000;
  font-size: 18px;
  line-height: 1.4em;
}
