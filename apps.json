{"abacus": {"name": "Abacus", "type": "Inventory Management Software", "version": "0.0.31", "color": "#0718c4", "iconPath": "icons/abacus", "repository": "https://github.com/haclab-co/abacus-releases"}, "evia": {"name": "Evia R<PERSON>", "type": "Restaurant Management Software", "version": "0.0.17", "color": "#722ed1", "iconPath": "icons/evia", "repository": "https://github.com/haclab-co/evia-releases"}, "homz": {"name": "Homz", "type": "Hotel Management System", "version": "0.1.26", "color": "#FF4500", "iconPath": "icons/homz", "repository": "https://github.com/haclab-co/homz-releases"}, "inncontrol": {"name": "InnControl", "type": "Hotel Management System", "version": "0.1.24", "color": "#eb2f96", "iconPath": "icons/inncontrol", "repository": "https://github.com/haclab-co/inncontrol-releases"}, "kanify": {"name": "<PERSON>ni<PERSON>", "type": "Garage Management System", "version": "0.0.2", "color": "#FF4500", "iconPath": "icons/kanify", "repository": "https://github.com/haclab-co/kanify-releases"}, "kyeyo": {"name": "Kyeyo CV", "type": "Recruitment Management System", "version": "0.1.12", "color": "#391085", "iconPath": "icons/kyeyo", "repository": "https://github.com/haclab-co/kyeyo-releases"}, "lenkit": {"name": "Lenkit", "type": "Loan and Savings Management System", "version": "0.0.29", "color": "#31c48d", "iconPath": "icons/lenkit", "repository": "https://github.com/haclab-co/lenkit-releases"}, "mission-control": {"name": "Mission Control", "type": "Haclab Management Software", "version": "0.0.3", "color": "#ff0000", "iconPath": "icons/mission-control", "repository": "https://github.com/haclab-co/mission-control-releases"}, "prosy": {"name": "Prosy", "type": "Property Management System", "version": "0.0.49", "color": "#151a4b", "iconPath": "icons/prosy", "repository": "https://github.com/haclab-co/prosy-releases"}, "zenwrench": {"name": "ZenWrench", "type": "Garage Management System", "version": "0.0.151", "color": "#722ed1", "iconPath": "icons/zenwrench", "repository": "https://github.com/haclab-co/zenwrench-releases"}}