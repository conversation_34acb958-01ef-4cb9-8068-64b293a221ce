import { numberFormat } from "../../../Utils/functions";

export default {
  name: "Stock payments",
  icon: "CheckCircleOutlined",
  path: "/stock_payments",
  collection: "stock_payments",
  singular: "Stock payment(s)",
  removeCreate: true,
  columns: [
    {
      valueType: "date",
      dataIndex: "payment_Date",
      title: "Payment Date",
      noBackDate: true,
      isPrintable: true,
    },
    {
      type: "dbSelect",
      dataIndex: "order",
      title: "Order",
      collection: "orders",
      isPrintable: true,
      label: ["_id"],
    },
    {
      valueType: "textarea",
      isPrintable: true,
      dataIndex: "description",
      title: "Description",
    },
    { type: "digit", isPrintable: true, dataIndex: "amount", title: "Amount" },

    {
      valueType: "dependency",
      hideInTable: true,
      title: "Total",
      isPrintable: true,
      width: "lg",
      fieldProps: {
        name: ["order", "amount"],
      },
      columns: ({ order = null, amount = 0 }) => {
        

        let invoice_total = 0,
          previous_receipts = 0,
          total = 0;

        invoice_total = order.items.reduce(
          (pv, cv) => pv + cv.price * cv.quantity,
          0
        );

        previous_receipts = order.receipts;

        total = invoice_total - previous_receipts - amount;

        return [
          {
            dataIndex: "balance",
            width: "100%",
            colProps: {
              md: 24,
            },
            valueType: "digit",
            render: function render(v) {
              return numberFormat(v);
            },

            renderFormItem: function renderFormItem() {
              return (
                "Invoice Total : " +
                numberFormat(invoice_total) +
                " | Previous Receipts :  (" +
                numberFormat(previous_receipts) +
                ") | Balance :  " +
                numberFormat(total) +
                " "
              );
            },
          },
        ];
      },
    },
  ],
};
