import React, { useEffect, useState } from "react";
import "./css.css";
import PouchDb from "pouchdb-browser";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import PrintComponents from "react-print-components";
import { <PERSON>loat<PERSON><PERSON><PERSON>, Flex, Divider, But<PERSON>, message } from "antd";
import { PrinterOutlined, FileTextOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";
import DocumentFooter from "../DocumentFooter";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";

const createQuotationFromInvoice = async (invoiceData, pouchDb, databasePrefix, CRUD_USER) => {
  try {
    const quotationDoc = {
      customer: invoiceData.customer,
      items: invoiceData.items,
      date: new Date().toISOString(),
      taxable: invoiceData.taxable,
      discount: invoiceData.discount,
      discountable: invoiceData.discountable,
      notes: `Generated from Invoice #${invoiceData._id}`,
      createdAt: new Date().toISOString(),
      entrant: invoiceData.entrant
    };

    const quotationsDb = new pouchDb(`${databasePrefix}quotations`);
    const result = await quotationsDb.post(quotationDoc);

    message.success(`Quotation created successfully with ID: ${result.id}`);
    return result;
  } catch (error) {
    console.error('Error creating quotation:', error);
    message.error('Failed to create quotation');
    throw error;
  }
};

const Quotation = (props) => {
  const { data, title = "Quotation", bottomText = "", pouchDatabase, databasePrefix } = props;

  

  // Add default value for appSettings
  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS")) || {
    documentHeader: "V1", // Set a default header version
    currency: "UGX",      // Set a default currency
  };

  const subTotal = data.items.reduce(
    (acc, item) => acc + item.price * item.quantity,
    0
  );
  const discount = data.discounted ? data.discount : 0;
  const tax = data.taxable ? subTotal * 0.18 : 0;
  const grandTotal = subTotal + tax - discount;

  const [company, setCompany] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [products, setProducts] = useState(null);
  const [units, setUnits] = useState([]);

  useEffect(() => {
    const productsDB = new PouchDb("products");
    productsDB
      .allDocs({
        include_docs: true,
        attachments: true,
        binary: true,
      })
      .then((res) => {
        setProducts(res.rows);
      });
  }, []);
  useEffect(() => {
    
    const customersDB = new PouchDb("customers");
    data &&
      data.customer &&
      customersDB
        .get(data.customer.value || data.customer._id, { binary: true })
        .then((res) => {
          
          setCustomer(res);
        });
  }, [data]);

  useEffect(() => {
    const unitsDB = new PouchDb("units");
    unitsDB
      .allDocs({
        include_docs: true,
        binary: true,
      })
      .then((data) => {
        const docs = data.rows.map((d) => d.doc);
        setUnits(docs);
      });
  }, [data]);


  useEffect(() => {
    const loadCompanyData = async () => {
      try {
        // Use the proper database access method if available
        if (pouchDatabase && databasePrefix !== undefined) {
          try {
            const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
            if (organizationsData && organizationsData.length > 0) {
              let comp = organizationsData[0];

              if (comp._attachments && comp._attachments.logo) {
                try {
                  const logoBlob = await pouchDatabase("organizations", databasePrefix).getAttachment(comp._id, "logo");
                  comp.logo = logoBlob;
                  setCompany(comp);
                } catch (logoError) {
                  console.warn("Error fetching logo:", logoError);
                  setCompany(comp);
                }
              } else {
                setCompany(comp);
              }
              return;
            }
          } catch (dbError) {
            console.warn("Error with pouchDatabase method, falling back to direct PouchDB:", dbError);
          }
        }

        // Get organization ID from localStorage for better performance
        const storedOrganization = JSON.parse(localStorage.getItem(LOCAL_STORAGE_ORGANIZATION));

        if (storedOrganization && storedOrganization._id) {
          // Use getDocument with specific organization ID
          const CompanyDB = new PouchDb("organizations");
          try {
            const comp = await CompanyDB.get(storedOrganization._id, {
              attachments: true,
              binary: true,
            });
            if (comp._attachments && comp._attachments.logo) {
              try {
                const logoBlob = await CompanyDB.getAttachment(comp._id, "logo");
                comp.logo = logoBlob;
                setCompany(comp);
              } catch (logoError) {
                console.warn("Error fetching logo:", logoError);
                setCompany(comp);
              }
            } else {
              setCompany(comp);
            }
            return;
          } catch (error) {
            console.error("Error fetching organization by ID:", error);
          }
        }

        // Fallback to direct PouchDB access
        const CompanyDB = new PouchDb("organizations");
        const data = await CompanyDB.allDocs({
          include_docs: true,
          attachments: true,
          binary: true,
        });

        if (data && data.rows && data.rows.length > 0 && data.rows[0].doc) {
          let comp = data.rows[0].doc;
          if (comp._attachments && comp._attachments.logo) {
            try {
              const logoBlob = await CompanyDB.getAttachment(comp._id, "logo");
              comp.logo = logoBlob;
              setCompany(comp);
            } catch (logoError) {
              console.warn("Error fetching logo:", logoError);
              setCompany(comp);
            }
          } else {
            setCompany(comp);
          }
        } else {
          console.warn("No organization data found, using fallback");
          setCompany({
            name: "Your Company",
            phone: "",
            alternative_phone: "",
            email: "",
            website: "",
            address: ""
          });
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        setCompany({
          name: "Your Company",
          phone: "",
          alternative_phone: "",
          email: "",
          website: "",
          address: ""
        });
      }
    };

    loadCompanyData();
  }, [pouchDatabase, databasePrefix]);

  const emptyRows = [];
  // Safely access documentHeader with optional chaining
  for (
    let index = 0;
    index < (appSettings?.documentHeader === "V2" ? 28 : 24) - data.items.length;
    index++
  ) {
    emptyRows.push({});
  }

  return !company || !customer ? null : (
    // <div className="tm_invoice_wrap">
    //   <div className="tm_invoice tm_style1" id="tm_download_section">
    <div>
      {appSettings?.documentHeader === "V2" && (
        <>
          <Flex vertical={false} justify="space-between">
            <RenderBlob blob={company.logo} size={150} />
            <div>
              <strong style={{ fontSize: 25, fontWeight: "bold" }}>
                {title.toUpperCase()}
              </strong>
              <p class="tm_invoice_number tm_m0 tm_f11">
                {title} No: <b class="tm_primary_color">{data._id}</b>
                <br /> Date:{" "}
                <b class="tm_primary_color">
                  {moment(data.date).format("DD MMM YYYY")}
                </b>
              </p>
            </div>
            <p class="tm_mb2 tm_f12">
              {company.address}
              <br />
              Tel: {company.phone} / {company.alternative_phone} <br />
              Email: {company.email} <br />
              {company.website && company.website}
            </p>
          </Flex>
          <Divider />
          <Flex vertical={false} justify="space-between">
            <div class="tm_mb2 tm_f10">
              <p class="tm_mb2">
                <b class="tm_primary_color">To:</b>
              </p>
              <p>
                <b style={{ fontSize: 15 }}>{customer.name}</b>
                <br />
                {customer.phone}{" "}
                {customer.alternative_phone &&
                  `/ ${customer.alternative_phone}`}{" "}
                <br />
                {customer.email && (
                  <>
                    {customer.email} <br />
                  </>
                )}
                {customer.address && (
                  <>
                    {customer.address} <br />
                  </>
                )}
              </p>
            </div>
          </Flex>
        </>
      )}

      {(!appSettings?.documentHeader ||
        appSettings?.documentHeader === "V1") && (
          <Flex vertical={true}>
            <center>
              <RenderBlob blob={company.logo} size={250} />
              <p class="tm_f12">
                {company.address}
                <br />
                Tel: {company.phone} / {company.alternative_phone} <br />
                Email: {company.email} <br />
                {company.website && company.website}
              </p>
            </center>
            <Flex vertical={false} justify="space-between">
              <div>
                <p class="tm_mb2 tm_f11">
                  <b class="tm_primary_color">To:</b>
                </p>
                <p>
                  <b style={{ fontSize: 15 }}>{customer.name}</b>
                  <br />
                  {customer.phone}{" "}
                  {customer.alternative_phone &&
                    `/ ${customer.alternative_phone}`}{" "}
                  <br />
                  {customer.email && (
                    <>
                      {customer.email} <br />
                    </>
                  )}
                  {customer.address && (
                    <>
                      {customer.address} <br />
                    </>
                  )}
                  {customer._id && (
                    <>
                      Customer ID : <strong>{customer._id} <br /></strong>
                    </>
                  )}
                </p>
              </div>
              <div>
                <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                  {title.toUpperCase()}
                </strong>
                <p class="tm_invoice_number tm_m0">
                  {title} No: <b class="tm_primary_color">{data._id}</b>
                </p>
                <p class="tm_invoice_date tm_m0">
                  Date:{" "}
                  <b class="tm_primary_color">
                    {moment(data.date).format("DD MMM YYYY")}
                  </b>
                </p>
              </div>
            </Flex>
          </Flex>
        )}
      <div className="tm_table tm_style">
        <center style={{ minHeight: 430 }}>
          <div
            className="tm_border"
          // style={{ width: 500 }}
          >
            <div className="main">
              <table>
                <thead>
                  <tr>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_center">
                      S/N
                    </th>
                    <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                      Description
                    </th>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_left">
                      Qty
                    </th>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                      Rate
                    </th>
                    <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="tm_border_none" style={{ border: "none", fontSize: 10 }}>
                  {data.items.map((item, index) => {
                    const PartUnit = products.find(
                      (part) => part.id === item.product.value
                    );

                    

                    const unit =
                      PartUnit && PartUnit.doc && PartUnit.doc.unit
                        ? units.find(
                          (u) => u._id === PartUnit.doc.unit.value
                        )?.abbreviation
                        : "";

                    return (
                      <>
                        <tr className="main">
                          <td className="tm_width_1 tm_text_center">
                            {index + 1}
                          </td>
                          <td className="tm_width_3">{
                            (() => {
                              const product = products.find((p) => p.id === item.product.value)?.doc;
                              return product?.name + (product?.sku ? ` (${product.sku})` : '');
                            })()
                          }</td>
                          <td className="tm_width_1 tm_text_left">
                            {item.quantity}{" "}
                            {item.quantity === 1 || unit === ""
                              ? unit
                              : unit + "s"}
                          </td>
                          <td className="tm_width_1 tm_text_right">
                            {numberFormat(item.price)}
                          </td>
                          <td className="tm_width_1 tm_text_right">
                            {numberFormat(item.price * item.quantity)}
                          </td>
                        </tr>
                      </>
                    );
                  })}
                  {emptyRows.map((item, index) => {
                    return (
                      <tr className="main">
                        <td className="tm_width_1 tm_text_center">
                          {" "}
                          &nbsp;
                        </td>
                        <td className="tm_width_3"></td>
                        <td className="tm_width_1 tm_text_left"></td>
                        <td className="tm_width_1 tm_text_right"></td>
                        <td className="tm_width_1 tm_text_right"></td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </center>
        <div className="tm_invoice_footer tm_mb20 tm_m0_md">
          <div className="tm_left_footer">
            {appSettings.mobile_wallet_settings && (
              <p className="tm_mb2 tm_f12">
                <b className="tm_primary_color">
                  Mobile Wallet Payment Method:{" "}
                </b>
                <br />
                {appSettings.mobile_wallet_settings.map((item) => (
                  <div>
                    {item.mobile_wallet} : <b>{item.id}</b>
                  </div>
                ))}
                <br />
              </p>
            )}
            {appSettings.acc_no && (
              <p className="tm_m0 tm_f12">
                <b className="tm_primary_color">Bank Details: </b>
                <br />
                {appSettings.acc_name} : <b>{appSettings.acc_no}</b>
                <br />
                {appSettings.bank_name} - {appSettings.bank_address} Branch
              </p>
            )}
          </div>
          <div className="tm_right_footer">
            <table>
              <tbody>
                <tr>
                  <td className="tm_width_3 tm_primary_color tm_border_none tm_bold tm_f15">
                    Subtotal
                  </td>
                  <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold tm_f15">
                    {appSettings.currency} {numberFormat(subTotal)}
                  </td>
                </tr>
                <tr>
                  <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                    Tax <span className="tm_ternary_color">(18%)</span>
                  </td>
                  <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                    {appSettings.currency} {numberFormat(tax)}
                  </td>
                </tr>
                {data.discounted && (
                  <tr>
                    <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                      Discount
                      {data.discount_description && (
                        <span className="tm_secondary_color">{` (${data.discount_description})`}</span>
                      )}
                    </td>
                    <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                      {appSettings.currency} {numberFormat(discount)}
                    </td>
                  </tr>
                )}
                <tr className="tm_border_top tm_border_bottom">
                  <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color">
                    Grand Total{" "}
                  </td>
                  <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color tm_text_right">
                    {appSettings.currency} {numberFormat(grandTotal)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <center>
          <strong style={{ fontSize: "11px" }}>{bottomText}</strong>
        </center>
        <div className="tm_invoice_footer tm_type1">
          <DocumentFooter />
        </div>
      </div>
    </div>
    // </div>
    // </div>
  );
};

const PrintableQuotation = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <Quotation {...props} />
      </PrintComponents>
      <Quotation {...props} />
    </>
  );
};

export default PrintableQuotation;
