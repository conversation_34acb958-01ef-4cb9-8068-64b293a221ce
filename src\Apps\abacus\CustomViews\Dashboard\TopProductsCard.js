import React, { useState } from 'react';
import { Card, Radio, DatePicker, Space, Typography } from 'antd';
import <PERSON><PERSON><PERSON> from './PieChart';
import moment from 'moment';

const { MonthPicker } = DatePicker;
const { Text } = Typography;

const TopProductsCard = ({ pouchDatabase, databasePrefix, selectedMonth, onMonthChange }) => {
  const [metricType, setMetricType] = useState('quantity');
  // Use local state if no external state is provided
  const [localSelectedMonth, setLocalSelectedMonth] = useState(null);

  // Use either the prop or local state
  const effectiveMonth = selectedMonth !== undefined ? selectedMonth : localSelectedMonth;

  // Handle month change with proper logging
  const handleMonthChange = (date) => {
    console.log('Month changed to:', date ? date.format('YYYY-MM') : 'null');
    // Update local state
    setLocalSelectedMonth(date);
    // Propagate to parent if callback exists
    if (onMonthChange) {
      onMonthChange(date);
    }
  };

  // Format the title based on selected month
  const getTitle = () => {
    if (effectiveMonth) {
      return `Top Products for ${effectiveMonth.format('MMMM YYYY')}`;
    }
    return 'Top Products This Month';
  };

  return (
    <Card
      title={getTitle()}
      extra={
        <Space>
          <DatePicker
            picker="month"
            value={effectiveMonth}
            onChange={handleMonthChange}
            allowClear={true}
            placeholder="Select month"
            format="MMM YYYY"
            style={{ width: 120 }}
          />
          <Radio.Group
            value={metricType}
            onChange={e => setMetricType(e.target.value)}
            buttonStyle="solid"
            size="small"
          >
            <Radio.Button value="quantity">By Quantity</Radio.Button>
            <Radio.Button value="profit">By Profit</Radio.Button>
          </Radio.Group>
        </Space>
      }
    >
      <div style={{ height: 300 }}>
        {/* Add key prop to force re-render when month changes */}
        <PieChart
          key={`pie-${metricType}-${effectiveMonth ? effectiveMonth.format('YYYY-MM') : 'current'}`}
          pouchDatabase={pouchDatabase}
          databasePrefix={databasePrefix}
          by={metricType}
          selectedMonth={effectiveMonth}
        />
      </div>
    </Card>
  );
};

export default TopProductsCard;
