import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Tabs,
  Layout,
  Typography,
  Button,
  Flex,
  Float<PERSON>utton,
  Card,
  Row,
  Col,
  Table,
  Tag,
  Progress,
} from "antd";
import {
  UsergroupAddOutlined,
  PrinterOutlined,
  PercentageOutlined,
  InfoCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PouchDB from "pouchdb";
import PrintComponents from "react-print-components";
import ViewTable from "../../../../Components/ViewTable";
import RenderBlob from "../../../../Components/RenderBlob";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

const CustomerCategory = (props) => {
  const { data, ...rest } = props;
  const [company, setCompany] = useState(null);
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase: props.pouchDatabase,
    databasePrefix: props.databasePrefix,
    CRUD_USER: props.CRUD_USER,
    userPermissions: props.userPermissions,
    modules: props.modules,
    modulesProperties: props.modulesProperties,
    filterID: { column: "customer_category", id: data._id },
  };

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!pouchDatabase || databasePrefix === undefined) {
          console.error("pouchDatabase or databasePrefix not available");
          setLoading(false);
          return;
        }

        // Fetch company data (organizations)
        try {
          const organizationsData = await pouchDatabase("organizations", databasePrefix).getAllData();
          if (organizationsData && organizationsData.length > 0) {
            setCompany(organizationsData[0]);
          }
        } catch (error) {
          console.warn("Error fetching company data:", error);
        }

        // Fetch customers in this category
        const customersData = await pouchDatabase("customers", databasePrefix).getAllData();
        const filteredCustomers = customersData.filter(
          customer => customer.customer_category && customer.customer_category.value === data._id
        );

        setCustomers(filteredCustomers);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Printable customer category component
  const PrintableCustomerCategory = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>CUSTOMER CATEGORY</Title>
            <Text>Category: {data.name}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Category Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
        <Descriptions.Item label="Percentage Type">
          {data.percent_type || "Simple"}
        </Descriptions.Item>
        {data.percent_type === "Simple" ? (
          <Descriptions.Item label="Profit Percentage" span={2}>
            {data.percent}%
          </Descriptions.Item>
        ) : (
          <Descriptions.Item label="Profit Percentage Ranges" span={2}>
            {data.ranges && data.ranges.map((range, index) => (
              <div key={index}>
                {range.from} - {range.to}: {range.percent}%
              </div>
            ))}
          </Descriptions.Item>
        )}
        <Descriptions.Item label="Description" span={2}>
          {data.description || "No description provided"}
        </Descriptions.Item>
      </Descriptions>

      {customers.length > 0 && (
        <>
          <Divider />
          <Title level={4}>Customers in this Category</Title>
          <table className="category-customers-table">
            <thead>
              <tr>
                <th>Customer ID</th>
                <th>Name</th>
                <th>Phone</th>
                <th>Email</th>
              </tr>
            </thead>
            <tbody>
              {customers.slice(0, 10).map((customer, index) => (
                <tr key={index}>
                  <td>{customer._id}</td>
                  <td>{customer.name}</td>
                  <td>{customer.phone || "N/A"}</td>
                  <td>{customer.email || "N/A"}</td>
                </tr>
              ))}
            </tbody>
          </table>
          {customers.length > 10 && (
            <div style={{ textAlign: "center", marginTop: 10 }}>
              <Text type="secondary">
                Showing 10 of {customers.length} customers
              </Text>
            </div>
          )}
        </>
      )}

      <Divider />

      <div style={{ marginTop: "50px", textAlign: "center" }}>
        <p>Generated on: {moment().format("DD MMM YYYY, HH:mm")}</p>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading customer category information...</div>;
  }

  // Columns for the customers table
  const customerColumns = [
    {
      title: 'Customer ID',
      dataIndex: '_id',
      key: '_id',
      width: '15%',
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '25%',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
      width: '20%',
      render: (phone) => phone || 'N/A',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: '25%',
      render: (email) => email || 'N/A',
    },
    {
      title: 'Type',
      dataIndex: 'customer_type',
      key: 'customer_type',
      width: '15%',
      render: (type) => (
        <Tag color={type === 'Individual' ? 'blue' : 'green'}>
          {type || 'N/A'}
        </Tag>
      ),
    },
  ];

  return (
    <>
      <PageHeader
        backIcon={<UsergroupAddOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={data.name}
        subTitle="Customer Category"
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Category Details
              </Button>
            }
          >
            <PrintableCustomerCategory />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Card title={<><InfoCircleOutlined /> Category Details</>} className="category-card">
              <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Name">{data.name}</Descriptions.Item>
                <Descriptions.Item label="Percentage Type">
                  {data.percent_type || "Simple"}
                </Descriptions.Item>
                <Descriptions.Item label="Description" span={2}>
                  {data.description || "No description provided"}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title={<><PercentageOutlined /> Profit Percentage</>} className="category-card">
              {data.percent_type === "Simple" ? (
                <div className="percentage-display">
                  <Progress
                    type="dashboard"
                    percent={data.percent}
                    format={(percent) => `${percent}%`}
                    width={120}
                  />
                  <Divider style={{ margin: '16px 0' }} />
                  <Text>Flat rate profit percentage applied to all customers in this category</Text>
                </div>
              ) : (
                <div className="percentage-ranges">
                  <Title level={5}>Percentage Ranges</Title>
                  <Table
                    dataSource={data.ranges || []}
                    pagination={false}
                    size="small"
                    columns={[
                      {
                        title: 'From',
                        dataIndex: 'from',
                        key: 'from',
                      },
                      {
                        title: 'To',
                        dataIndex: 'to',
                        key: 'to',
                      },
                      {
                        title: 'Percentage',
                        dataIndex: 'percent',
                        key: 'percent',
                        render: (percent) => `${percent}%`,
                      },
                    ]}
                  />
                </div>
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card
              title={
                <Flex align="center" gap="small">
                  <UserOutlined />
                  <span>Customers in this Category ({customers.length})</span>
                </Flex>
              }
              className="category-card"
            >
              <Table
                dataSource={customers}
                columns={customerColumns}
                rowKey="_id"
                pagination={{ pageSize: 10 }}
              />
            </Card>
          </Col>
        </Row>
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Category Details"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableCustomerCategory />
        </PrintComponents>
      </div>
    </>
  );
};

export default CustomerCategory;
