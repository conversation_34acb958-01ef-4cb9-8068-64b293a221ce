const { ipc<PERSON><PERSON><PERSON> } = require("electron");
// All of the Node.js APIs are available in the preload process.
// It has the same sandbox as a Chrome extension.

// PERFORMANCE: Enhanced ElectronAPI for secure IPC communication
window.electronAPI = {
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
    send: (channel, ...args) => ipcRenderer.send(channel, ...args),
    on: (channel, callback) => ipcRenderer.on(channel, callback),
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
};

// PERFORMANCE: Enhanced AppAPI with backward compatibility
window.AppAPI = {
    sendEmail: (emailFrom, emailTo, emailSubject, emailHTML) => {
        var nodemailer = require("nodemailer")

        const transporter = nodemailer.createTransport({
            host: 'smtp.yandex.com',
            port: 465,
            tls: {
                ciphers: 'SSLv3',
                rejectUnauthorized: false
            },
            debug: true,
            auth: {
                user: '<EMAIL>',
                pass: '@*********'
            }
        });

        const mailOptions = {
            from: '"Haclab Company Limited" <<EMAIL>>',
            to: emailTo,
            subject: emailSubject,
            html: emailHTML,
        }

        transporter.sendMail(mailOptions, function (err, info) {
            if (err) console.log(err);
            else console.log(info);
        })
    },

    // PERFORMANCE: Additional safe utilities
    getMemoryUsage: () => {
        if (process && process.memoryUsage) {
            const usage = process.memoryUsage();
            return {
                rss: Math.round(usage.rss / 1024 / 1024), // MB
                heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
                heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
                external: Math.round(usage.external / 1024 / 1024) // MB
            };
        }
        return null;
    },

    getSystemInfo: () => {
        const os = require('os');
        return {
            platform: process.platform,
            arch: process.arch,
            totalMemory: Math.round(os.totalmem() / 1024 / 1024), // MB
            freeMemory: Math.round(os.freemem() / 1024 / 1024), // MB
            cpus: os.cpus().length
        };
    }
}

window.addEventListener('DOMContentLoaded', () => {
    const replaceText = (selector, text) => {
        const element = document.getElementById(selector)
        if (element) element.innerText = text
    }



    for (const type of ['chrome', 'node', 'electron']) {
        replaceText(`${type}-version`, process.versions[type])
    }
})