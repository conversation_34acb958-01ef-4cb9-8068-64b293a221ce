import React, { useEffect, useState } from "react";
import NewInvoice from "../OrderInvoice";
import InvoiceTemplate from "../../../Universal/CustomViews/Components/InvoiceTemplate";
import { buffEvents } from "../../modulesProperties/utils";
import PouchDb from "pouchdb-browser";

function Invoice({ data }) {
  const [guest, setGuest] = useState(null);

  useEffect(() => {
    const guestDB = new PouchDb("guests");
    data.guest
      ? guestDB.get(data.guest.value).then((data) => {
          setGuest(data);
        })
      : setGuest(null);
  }, [data]);

  

  const newdata = {
    date: data.createdAt,
    id: data._id,
    client: { title: "Guest", name: guest }, //data.guest,
    operator: { title: "Waiter", name: data.entrant.label },
    items: [
      {
        name: `${data.venue.label} hire for ${data.duration} hours`,
        price: data.price,
        quantity: 1,
      },
      ...data.items.map((item) => {
        return {
          name: item.item.label,
          price: item.cost,
          quantity: item.quantity,
        };
      }),
    ],
  };
  return (
    data && <InvoiceTemplate documentTitle="Events Invoice" data={newdata} />
  );
}

export default Invoice;
