import React, { useEffect, useState } from "react";
import PouchDb from "pouchdb-browser";
import ReceiptTemplate from "../../../Universal/CustomViews/ReceiptTemplate";
import moment from "moment";

const EventReceipt = ({ data, documentTitle }) => {
  const [guest, setGuest] = useState(null);
  const [invoice, setInvoice] = useState(null);
  const [receipt, setReceipt] = useState(null);



  // useEffect(() => {
  //   const guestDB = new PouchDb("guests");
  //   
  //   data.order && invoice && invoice.guest
  //     ? guestDB.get(invoice.guest.value).then((ERDATA) => {
  //         setGuest({ ...ERDATA, name:`${ERDATA.name.first_name} ${ERDATA.name.sur_name}` });
  //       })
  //     : setGuest(null);
  // }, []);

  useEffect(() => {
    const invoiceDB = new PouchDb("events");
    data.order
      ? invoiceDB.get(data.order._id).then((data) => {
        setInvoice(data);
        return data;
      }).then((invoiceData) => {
        const guestDB = new PouchDb("guests");
        
        invoiceData && invoiceData.guest
          ? guestDB.get(invoiceData.guest.value).then((ERDATA) => {
            setGuest(ERDATA);
          })
          : setGuest(null);

      })
      : setInvoice(null);
  }, []);

  useEffect(() => {
    if (data.order) {
      const receiptDB = new PouchDb("events_receipts");
      receiptDB.allDocs({ include_docs: true })
        .then((res) => {
          const filteredData = res.rows.filter(
            (row) =>
              row &&
              row.doc &&
              row.doc._id &&
              !row.doc._id.startsWith('_') &&
              row.doc.order &&
              row.doc.order._id === data.order._id &&
              row.doc.date &&
              moment(row.doc.date).isSameOrBefore(moment(data.date))
          );

          setReceipt(filteredData);
        })
        .catch((error) => {
          console.error("Error fetching event receipts:", error);
          setReceipt(null);
        });
    } else {
      setReceipt(null);
    }
  }, [data.order?._id, data.date]); // Added proper dependencies

  

  const newdata = {
    ...data,
    date: data.date,
    client: { title: "Guest", name: guest },
    paid:
      receipt && receipt.length > 0
        ? receipt.reduce((previous, current) => {
          return previous + parseInt(current.doc.amount);
        }, 0)
        : 0,
    operator: { title: "Cashier", name: data.entrant.label },
    invoice: {
      ...invoice,
      items: invoice
        ? [
          {
            name: `${invoice.venue.label} hire for ${invoice.duration} hours`,
            price: invoice.price,
            quantity: 1,
          },
          ...invoice.items.map((item) => {
            return {
              name: item.item.label.split(" - ")[0],
              price: item.cost,
              quantity: item.quantity,
            };
          }),
        ]
        : [],
    },
  };

  

  return (
    <div>
      <ReceiptTemplate data={newdata} documentTitle={"Event Receipt"} />
    </div>
  );
};

export default EventReceipt;
