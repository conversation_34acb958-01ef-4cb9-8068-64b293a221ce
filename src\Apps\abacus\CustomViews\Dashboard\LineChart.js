import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Line } from "@ant-design/plots";
// import currencyFormatter from "currency-formatter";
import moment from "moment";
import { buffProducts } from "../../modulesProperties/utils";
import { filterByBranch } from "../../../../Utils/branchFiltering";

// Helper function to format dates and amounts
const formatData = {
  date: (text) => moment(text).format("MMM YY"),
  amount: (text) => text.toLocaleString(),
  // currencyFormatter.format(text, { code: "" })
};

const LineChart = ({ pouchDatabase, databasePrefix, currentUser }) => {
  const [data, setData] = useState([]);

  // Memoize database instances to prevent recreation on every render
  const invoiceDB = useMemo(() => pouchDatabase("invoices", databasePrefix), [pouchDatabase, databasePrefix]);
  const expenseDB = useMemo(() => pouchDatabase("expenses", databasePrefix), [pouchDatabase, databasePrefix]);
  const productsDB = useMemo(() => pouchDatabase("products", databasePrefix), [pouchDatabase, databasePrefix]);

  const processChartData = useCallback(() => {
    Promise.all([
      invoiceDB.getAllData(),
      expenseDB.getAllData(),
      productsDB.getAllData().then((r) => buffProducts(r, null, null, "products", localStorage.getItem("SELECTED_BRANCH"))),
      // pouchDatabase("withdraws", databasePrefix).getAllData(),
    ]).then((values) => {
      // Get the selected branch from localStorage
      const SELECTED_BRANCH = localStorage.getItem("SELECTED_BRANCH");

      // Apply branch filtering using the utility function
      const income = filterByBranch(values[0] || [], SELECTED_BRANCH);
      const expenses = filterByBranch(values[1] || [], SELECTED_BRANCH);
      const products = values[2] || []; // Products already buffed
      // const withdraws =
      //   SELECTED_BRANCH && SELECTED_BRANCH !== "none"
      //     ? values[3].filter((i) => i.branch === SELECTED_BRANCH)
      //     : values[3];

      let chartData = [];

      for (let i = 11; i >= 0; i--) {
        let currentDate = moment(new Date());
        currentDate = currentDate.subtract(i, "months");

        let incomeThisMonth = income.filter((n) => {
          if (!n.date) return false;
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var incomeThisMonthAmount = incomeThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          if (!row.items || !Array.isArray(row.items)) {
            return sumSoFar;
          }
          return (
            sumSoFar +
            parseInt(
              row.items.reduce(
                (pv, cv) => pv + parseInt((cv.quantity || 0) * (cv.price || 0)),
                0
              )
            )
          );
        },
          0);

        var salesCostMonthAmount = incomeThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          if (!row.items || !Array.isArray(row.items)) {
            return sumSoFar;
          }
          return (
            sumSoFar +
            parseInt(
              row.items.reduce((pv, cv) => {
                if (!cv.product || !cv.product.value) {
                  return pv;
                }
                const cvCost = products.find((p) => p._id === cv.product.value);
                return (
                  pv +
                  parseInt(cv.quantity || 0) *
                  parseInt(cvCost ? (cvCost.cost ? cvCost.cost : 0) : 0)
                );
              }, 0)
            )
          );
        },
          0);

        let expensesThisMonth = expenses.filter((n) => {
          if (!n.date) return false;
          var d = moment(n.date);
          return currentDate.format("MMM YY") === d.format("MMM YY");
        });
        var expensesThisMonthAmount = expensesThisMonth.reduce(function (
          sumSoFar,
          row
        ) {
          return sumSoFar + parseInt(row.amount || 0);
        },
          0);

        chartData.push(
          {
            year: currentDate.format("MMM YY"),
            value: incomeThisMonthAmount,
            category: "Sales",
          },

          {
            year: currentDate.format("MMM YY"),
            value: salesCostMonthAmount,
            category: "Sales Cost",
          },
          {
            year: currentDate.format("MMM YY"),
            value: incomeThisMonthAmount - salesCostMonthAmount,
            category: "Gross Profit",
          },
          {
            year: currentDate.format("MMM YY"),
            value:
              incomeThisMonthAmount -
              salesCostMonthAmount -
              expensesThisMonthAmount,
            category: "Net Profit",
          },
          {
            year: currentDate.format("MMM YY"),
            value: expensesThisMonthAmount,
            category: "Expense",
          }
          // {
          //   year: currentDate.format("MMM YY"),
          //   value: loansThisMonthAmount - paymentsThisMonthAmount,
          //   category: "Arrears",
          // }
        );
      }
      setData(chartData);
    });
  }, [invoiceDB, expenseDB, productsDB]);

  useEffect(() => {
    // Set up event listeners
    const setupListeners = () => {
      invoiceDB.on("dbChange", processChartData);
      expenseDB.on("dbChange", processChartData);
      productsDB.on("dbChange", processChartData);
    };

    // Clean up event listeners
    const cleanupListeners = () => {
      invoiceDB.off("dbChange", processChartData);
      expenseDB.off("dbChange", processChartData);
      productsDB.off("dbChange", processChartData);
    };

    setupListeners();
    return cleanupListeners;
  }, [invoiceDB, expenseDB, productsDB, processChartData]);

  // Load data only once when component mounts
  useEffect(() => {
    processChartData();
    // We don't include processChartData in dependencies because we only want to run this once
    // The event listeners will handle subsequent updates
  }, []);

  // Memoize the config object to prevent unnecessary re-renders
  const config = useMemo(() => ({
    data,
    xField: "year",
    yField: "value",
    seriesField: "category",
    smooth: true,
    xAxis: {
      //   type: 'time',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.category, value: datum.value.toLocaleString() };
      },
    },
    point: {
      size: 4,
    },
    color: ["#1677ff", "#fadb14", "#13c2c2", "#52c41a", "#f5222d"],
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: (v) =>
          `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
  }), [data]);

  return <Line {...config} />;
};

export default LineChart;
