/* Print styles for Abacus Reports */
@media print {

  /* Override all backgrounds to white */
  body,
  div,
  table,
  tr,
  td,
  th,
  .ant-table,
  .ant-table-thead>tr>th,
  .ant-table-tbody>tr>td,
  .ant-card,
  .ant-card-head,
  .ant-card-body,
  .tm_gray_bg,
  .tm_accent_bg_10,
  .tm_accent_bg_20,
  .ant-table-row:nth-child(odd),
  .ant-table-row:nth-child(even),
  .ant-table-row:hover>td,
  .ant-table-row-hover,
  .ant-table-row-hover>td,
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    background-color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* Force white background for the print container */
  .ant-table-container,
  .ant-table-wrapper,
  .ant-table,
  .ant-card,
  .print-container {
    background-color: white !important;
  }

  /* Override any hover effects */
  .ant-table-tbody>tr.ant-table-row:hover>td {
    background-color: white !important;
  }

  /* Ensure text is black */
  body,
  div,
  table,
  tr,
  td,
  th,
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black !important;
  }

  /* Keep some specific colors for emphasis */
  .tm_danger_color,
  .ant-typography-danger {
    color: red !important;
  }

  .tm_accent_color,
  .ant-typography-success,
  .ant-typography-success strong {
    color: green !important;
  }

  /* Remove box shadows */
  .ant-card,
  .ant-table {
    box-shadow: none !important;
  }

  /* Keep borders for structure */
  .ant-table-thead>tr>th,
  .ant-table-tbody>tr>td {
    border-color: #ddd !important;
  }

  /* Remove pagination when printing */
  .ant-pagination {
    display: none !important;
  }

  /* Ensure proper page breaks */
  .ant-table {
    page-break-inside: auto !important;
  }

  .ant-table-tbody>tr {
    page-break-inside: avoid !important;
  }

  /* Ensure white background for cards */
  .ant-card {
    background: white !important;
  }
}