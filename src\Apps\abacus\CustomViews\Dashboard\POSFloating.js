import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ge, Drawer } from 'antd';
import { ShoppingCartOutlined } from '@ant-design/icons';
import POS from './POS';

const POSFloating = (props) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const showPOS = () => {
    setIsOpen(true);
  };
  
  const handleClose = () => {
    setIsOpen(false);
  };
  
  return (
    <>
      <FloatButton
        icon={<ShoppingCartOutlined />}
        tooltip="Open POS"
        onClick={showPOS}
        type="primary"
        badge={{ count: 0, showZero: false }}
      />
      
      <Drawer
        title="Point of Sale"
        placement="right"
        width="100%"
        onClose={handleClose}
        open={isOpen}
        bodyStyle={{ padding: 0, height: '100%' }}
        contentWrapperStyle={{ height: '100%' }}
        maskClosable={false}
        keyboard={false}
      >
        <POS {...props} />
      </Drawer>
    </>
  );
};

export default POSFloating;
