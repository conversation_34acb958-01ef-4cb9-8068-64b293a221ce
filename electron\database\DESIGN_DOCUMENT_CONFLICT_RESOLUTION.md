# Design Document Conflict Resolution

## Overview

This document describes the specialized conflict resolution system for CouchDB/PouchDB design documents (`_design/*`) that was implemented to handle the "Unable to resolve latest revision for id _design/common" error.

## Problem

Design documents contain special CouchDB structures like views, filters, and validation functions. When conflicts occur in design documents, the standard conflict resolution strategy can fail because:

1. **Special Structure**: Design documents have specific properties (`views`, `validate_doc_update`, etc.) that need careful merging
2. **View Definitions**: Map/reduce functions in views are code strings that can't be merged like regular data
3. **PouchDB Merge Library**: The `pouchdb-merge` library has limitations with design document conflicts

## Solution

### 1. Prevention at Source - Skip Design Documents During Sync

**UPDATED APPROACH**: The Electron backend now excludes design documents from synchronization entirely:

```javascript
const syncOptions = {
  live: false,
  retry: false,
  include_docs: true,
  batch_size: this.settings.batchSize,
  timeout: this.settings.syncTimeout,
  conflicts: true,
  filter: (doc) => !doc._id.startsWith('_design/') // Skip design documents
};
```

This prevents design document conflicts at the source, as the Electron backend focuses on data synchronization while the frontend handles design documents for querying.

### 2. Fallback - Detection and Routing (Frontend Only)

For frontend databases, the conflict resolution system detects design documents and routes them to specialized handlers:

```javascript
// Special handling for design documents
if (docId.startsWith('_design/')) {
  return await this.handleDesignDocumentConflicts(change, source);
}
```

### 2. Design Document Specific Resolution

The specialized resolution strategy:

1. **Collects All Views**: Gathers all unique views from all conflicting document versions
2. **Merges Views Intelligently**: Preserves all unique views while preferring newer versions for duplicates
3. **Handles Design Properties**: Properly merges `validate_doc_update`, `filters`, `shows`, `lists`, etc.
4. **Maintains Deterministic Resolution**: Uses timestamp + document ID for consistent resolution across clients

### 3. View Merging Logic

```javascript
// Collect all unique views from all document versions
sortedDocs.forEach(doc => {
  if (doc.views) {
    Object.keys(doc.views).forEach(viewName => {
      if (!allViews[viewName]) {
        allViews[viewName] = doc.views[viewName];
      } else {
        // Merge properties, preferring newer document
        allViews[viewName] = {
          ...allViews[viewName],
          ...doc.views[viewName]
        };
      }
    });
  }
});
```

## Implementation Details

### Files Modified

1. **electron/database/DatabaseHandler.js**
   - Added `handleDesignDocumentConflicts()` method
   - Added `resolveDesignDocumentConflicts()` method
   - Added `findDatabaseKeyByDocument()` helper method

2. **src/Database/index.js**
   - Added design document detection in `handleConflicts()`
   - Added `handleDesignDocumentConflicts()` method
   - Added `resolveDesignDocumentConflicts()` method

3. **src/Database/SimplifiedDB.js**
   - Added design document detection in `handleConflicts()`
   - Added `handleDesignDocumentConflicts()` method
   - Added `resolveDesignDocumentConflicts()` method

### Key Features

1. **Automatic Detection**: Conflicts in `_design/*` documents are automatically routed to specialized handlers
2. **View Preservation**: All unique views from conflicting versions are preserved
3. **Property Merging**: Design document properties are merged intelligently
4. **Deterministic Resolution**: Same conflicts resolve the same way on all clients
5. **Comprehensive Logging**: Detailed logging for debugging and monitoring
6. **Metadata Tracking**: Conflict resolution metadata is added to resolved documents

### Conflict Resolution Metadata

Resolved design documents include metadata about the resolution:

```javascript
conflictResolution: {
  strategy: 'designDocumentMerge',
  resolvedAt: new Date().toISOString(),
  conflictCount: conflictDocs.length,
  resolvedBy: 'DatabaseHandler',
  mergedViews: ['byCreatedAt', 'byUpdatedAt', 'byType'],
  mergedProperties: ['validate_doc_update', 'filters']
}
```

## Benefits

1. **Eliminates Errors**: Resolves the "Unable to resolve latest revision" error
2. **Preserves Functionality**: All views and design document features are preserved
3. **Automatic Resolution**: No manual intervention required
4. **Consistent Results**: Deterministic resolution across all clients
5. **Comprehensive Coverage**: Works across all database classes (DatabaseHandler, Database, SimplifiedDB)

## Testing

To test the implementation:

1. Create conflicting design documents on different clients
2. Trigger synchronization
3. Verify that conflicts are resolved automatically
4. Check that all views are preserved and functional
5. Confirm no "Unable to resolve latest revision" errors occur

## Monitoring

The system provides comprehensive logging:

- Design document conflict detection
- View merging progress
- Resolution completion
- Error handling and fallbacks

Monitor console logs for messages like:
- `[DatabaseHandler] Handling design document conflicts for _design/common`
- `[DatabaseHandler] Design document merge completed: 5 views merged`
- `[DatabaseHandler] Successfully resolved design document conflicts for _design/common`
