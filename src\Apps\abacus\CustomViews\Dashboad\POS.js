import React, { useState, useEffect } from "react";
import {
  Layout,
  Menu,
  Card,
  Button,
  List,
  Divider,
  Input,
  InputNumber,
  Row,
  Col,
  Typography,
  Modal,
  Tooltip,

} from "antd";
import { buffProducts } from "../../modulesProperties/utils";
import { DeleteOutlined } from "@ant-design/icons";

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

const POS = ({ pouchDatabase, databasePrefix }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const [cart, setCart] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [products, setProducts] = useState([
    { id: 1, name: "Product A", price: 20 },
  ]);


  useEffect(() => {
    const products = pouchDatabase("products", databasePrefix).getAllData()
      .then((r) => buffProducts(r, null, null, "products", localStorage.getItem("SELECTED_BRANCH")))
      .then((products) => {
        setProducts(products.map((p) => { return { ...p, id: p._id, price: p.sell } }));
      })
  }, []);

  const addToCart = (product) => {
    setCart((prevCart) => {
      const existingProduct = prevCart.find((item) => item.id === product.id);
      if (existingProduct) {
        return prevCart.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevCart, { ...product, quantity: 1 }];
      }
    });
  };

  const updateQuantity = (id, quantity) => {
    setCart((prevCart) =>
      prevCart.map((item) =>
        item.id === id ? { ...item, quantity: quantity } : item
      )
    );
  };

  const updatePrice = (id, price) => {
    setCart((prevCart) =>
      prevCart.map((item) =>
        item.id === id ? { ...item, price: price } : item
      )
    );
  };

  const removeItem = (id) => {
    setCart((prevCart) => prevCart.filter((item) => item.id !== id));
  };

  const getTotal = () =>
    cart.reduce((total, item) => total + item.price * item.quantity, 0);

  // Filter products based on search term
  const filteredProducts = products.filter((product) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <Button type="primary" onClick={showModal}>
        POS
      </Button>
      <Modal
        centered
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={"100%"}
      // height={"95%"}
      >
        <Layout
        // style={{ minHeight: "80vh" }}
        >
          <Layout>
            <Header style={{ background: "#fff", padding: "0 16px" }}>
              <Title level={2}>Point of Sale</Title>
            </Header>

            <Content style={{ margin: "16px" }}>
              <Row gutter={16}>
                <Col span={14}>
                  <Divider orientation="left">Product Catalog</Divider>
                  <Input
                    placeholder="Search for products"
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{ marginBottom: "16px" }}
                  />
                  <Row
                    gutter={[16, 16]}
                    style={{ height: "350px", overflow: "auto" }}
                  >
                    {filteredProducts.length > 0 ? (
                      filteredProducts.map((product) => (
                        <Col span={12} key={product.id}>
                          <Card
                            title={product.name}
                            extra={`${product.price.toLocaleString()}`}
                            actions={[
                              <Button
                                type="primary"
                                onClick={() => addToCart(product)}
                              >
                                Add to Cart
                              </Button>,
                            ]}
                          >
                            <strong> {product.name}</strong>
                            <p>In Stock: {(product.stoked - product.sold)
                              > 0 ?
                              <Text type="success">{product.stoked - product.sold}</Text>
                              :
                              <Text type="danger">{product.stoked - product.sold}</Text>
                            }</p>
                          </Card>
                        </Col>
                      ))
                    ) : (
                      <Col span={24}>
                        <p>No products found.</p>
                      </Col>
                    )}
                  </Row>
                </Col>

                <Col span={10}>
                  <Divider orientation="left">Cart Summary</Divider>
                  <List
                    bordered
                    dataSource={cart}
                    renderItem={(item) => (
                      <List.Item>
                        <Row style={{ width: "100%" }} gutter={8}>
                          <Col span={8}>{item.name}</Col>
                          <Col span={2}>
                            <InputNumber
                              min={1}
                              value={item.quantity}
                              onChange={(quantity) =>
                                updateQuantity(item.id, quantity)
                              }
                            />
                          </Col>
                          <Col span={8}>
                            <InputNumber
                              min={0}
                              value={item.price}
                              onChange={(price) => updatePrice(item.id, price)}
                              formatter={(value) => `X ${value}`}
                              parser={(value) => value.replace("X", "")}
                            />
                          </Col>
                          <Col span={4}>
                            {(item.price * item.quantity).toLocaleString()}
                          </Col>
                          <Col span={2}>
                            <Tooltip title="Remove">
                              <Button
                                onClick={() => removeItem(item.id)}
                                type="danger"
                                shape="circle"
                                icon={<DeleteOutlined />}
                              />
                            </Tooltip>
                          </Col>
                        </Row>
                      </List.Item>
                    )}
                  />
                  <Divider />
                  <Row justify="space-between">
                    <Col>Total:</Col>
                    <Col>{getTotal().toLocaleString()}</Col>
                  </Row>
                  <Button type="primary" block style={{ marginTop: "16px" }}>
                    Checkout
                  </Button>
                </Col>
              </Row>
            </Content>
          </Layout>
        </Layout>
      </Modal>
    </>
  );
};

export default POS;
