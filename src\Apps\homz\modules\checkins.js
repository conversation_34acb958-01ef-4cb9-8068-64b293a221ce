var moment = require("moment");

export default {
  name: "Checking In",
  icon: "CreditCardOutlined",
  path: "/checkins",
  collection: "checkins",
  singular: "Check In",
  removeCreate: true,
  columns: [
    {
      dataIndex: "_id",
      title: "ID",
      hideInForm: true,
    },
    {
      title: "Reference No",
      dataIndex: "ref_number",
      sorter: true,
      valueType: "number",
      width: "lg",
      colProps: {
        md: 6,
      },
    },
    {
      type: "dbSelect",
      collection: "guests",
      label: ["title", "sur_name", "first_name"],
      dataIndex: "guest",
      title: "Guest",
      width: "lg",
      valueType: "text",
      isRequired: true,
      filters: true,
      onFilter: true,
    },
    {
      title: "Arrival Date",
      dataIndex: "arrival_date",
      sorter: true,
      valueType: "dateTime",
      isRequired: true,
      width: "lg",
      colProps: {
        md: 6,
      },
    },

    {
      title: "Departure Date",
      dataIndex: "departure_date",
      sorter: true,
      valueType: "date",
      isRequired: true,
      width: "lg",
      colProps: {
        md: 12,
      },
    },
    {
      type: "dbSelect",
      isRequired: true,
      collection: "companies",
      label: ["name"],
      dataIndex: "company",
      title: "Company",
      width: "lg",
    },
    {
      title: "Billing",
      dataIndex: "billing",
      sorter: true,
      hideInTable: true,
      valueType: "select",
      isRequired: true,
      valueEnum: {
        "Self Payment": {
          text: "Self Payment",
        },
        Company: {
          text: "Company",
        },
      },
    },
    {
      valueType: "dateTime",
      dataIndex: "arrival_date",
      title: "Date Of Arrival In Uganda",
      hideInTable: true,
    },
    {
      valueType: "dependency",
      hideInTable: true,
      name: ["arrival_date", "departure_date"],
      columns: function columns(_ref) {
        var arrival_date = _ref.arrival_date,
          departure_date = _ref.departure_date;
        return [
          {
            dataIndex: "stay_duration",
            title: "Proposed Duration Of Stay In Uganda",
            valueType: "digits",
            colProps: {
              md: 12,
            },
            fieldProps: {
              addonAfter: "Nights",
              style: {
                width: "100%",
              },
              disabled: true,
              value:
                departure_date && arrival_date
                  ? moment(departure_date)
                      .startOf("day")
                      .diff(moment(arrival_date).startOf("day"), "days")
                  : 0,
            },
          },
        ];
      },
    },
    {
      valueType: "text",
      dataIndex: "car_number",
      title: "Car Number",
      hideInTable: true,
    },
    {
      valueType: "digit",
      dataIndex: "persons",
      title: "Persons",
      hideInTable: true,
    },
    {
      valueType: "text",
      dataIndex: "purpose_of_visit",
      title: "Purpose Of Visit",
      hideInTable: true,
    },
    {
      type: "dbSelect",
      collection: "apartments",
      label: ["number", "short_desc"],
      dataIndex: "apartment_number",
      isRequired: true,
      filterOptions: {
        dataIndex: "apartment_status",
        value: ["Occupied", "Booked", "House Use", "Out Of Order"],
        direction: "out",
      },
      title: "Apartment",
    },

    {
      valueType: "dependency",
      hideInTable: true,
      name: ["apartment_number"],
      columns: function columns(_ref2) {
        var apartment_number = _ref2.apartment_number;

        if (apartment_number) {
          return [
            {
              dataIndex: "apartment_rate",
              title: "Apartment Rate",
              valueType: "money",
              colProps: {
                md: 12,
              },
              fieldProps: {
                style: {
                  width: "100%",
                },
                precision: 2,
                defaultValue: apartment_number ? apartment_number.rate : 0,
              },
            },
          ];
        } else {
          return [];
        }
      },
    },
    {
      valueType: "dependency",
      hideInTable: true,
      name: [
        "apartment_number",
        "apartment_rate",
        "arrival_date",
        "departure_date",
      ],
      columns: function columns(_ref3) {
        var apartment_number = _ref3.apartment_number,
          apartment_rate = _ref3.apartment_rate,
          arrival_date = _ref3.arrival_date,
          departure_date = _ref3.departure_date;
        return [
          {
            dataIndex: "total",
            title: "Check In Total",
            valueType: "money",
            colProps: {
              md: 12,
            },
            fieldProps: {
              style: {
                width: "100%",
              },
              disabled: true,
              value:
                (apartment_rate || apartment_number) &&
                departure_date &&
                arrival_date
                  ? moment(departure_date)
                      .startOf("day")
                      .diff(moment(arrival_date).startOf("day"), "days") *
                    (apartment_rate ? apartment_rate : apartment_number.rate)
                  : 0,
            },
          },
        ];
      },
    },
    {
      dataIndex: "checked_out",
      title: "Checked Out",
      valueType: "switch",
      filters: true,
      onFilter: true,
      valueEnum: {
        true: { text: "Checked Out" },
        [undefined]: { text: "Checked In" },
        [!true]: { text: "Checked In [Edited]" },
      },
      fieldProps: {
        checkedChildren: false,
        unCheckedChildren: true,
        defaultChecked: false,
      },
      render: (_, row) => (row.checked_out ? "Yes" : "No"),
    },
  ],
};
