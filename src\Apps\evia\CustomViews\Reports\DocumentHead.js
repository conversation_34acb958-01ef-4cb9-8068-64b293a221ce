import { Col, Image, Row } from "antd";
import React from "react";

const letterheadStyles = {
  container: {
    display: "flex",
    alignItems: "center",
    padding: "20px",
    backgroundColor: "#f0f0f0",
  },
  logo: {
    height: "80px",
    marginRight: "20px",
  },
  companyDetails: {
    flexGrow: 1,
  },
  companyName: {
    margin: 0,
    fontSize: "24px",
    fontWeight: "bold",
  },
  companyInfo: {
    margin: "5px 0",
    fontSize: "14px",
  },
};

function DocumentHead({ company }) {
  

  return (
    company && (
      <Row gutter={24} style={{ marginTop: 30, marginBottom: 30 }}>
        <Col span={12} style={{ textAlign: "left" }}>
          <h3>{company.name}</h3>
          <h4>{company.alternative_name}</h4>
          <div>{company.address}</div>
          <div>
            <strong>Phone no: </strong>
            {company.phone}
          </div>
          <div>
            <strong>Email: </strong>
            {company.email}
          </div>
          <div>
            <strong>website: </strong>
            {company.website}
          </div>
        </Col>
        <Col
          span={12}
          style={{
            textAlign: "right",
          }}
        >
          <Image width={100} src={company.orgLogo} />
          {/* <div><strong>Invoice ID. : </strong>{data.invoice._id}</div> */}
        </Col>
      </Row>
    )
  );
}

export default DocumentHead;
