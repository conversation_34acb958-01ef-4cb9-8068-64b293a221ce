export default {
  name: "Venues",
  icon: "UsergroupAddOutlined",
  path: "/events_management/menu_categories",
  parent: "events_management",
  collection: "venues",
  singular: "Venue",
  columns: [
    {
      title: "Name",
      dataIndex: "name",
      valueType: "text",
      isRequired: true,
      isPrintable: true,
    },
    {
      title: "Price",
      dataIndex: "price",
      valueType: "digit",
      isPrintable: true,
    },
    {
      title: "Description",
      dataIndex: "description",
      valueType: "textarea",
      isPrintable: true,
    },
  ],
};
