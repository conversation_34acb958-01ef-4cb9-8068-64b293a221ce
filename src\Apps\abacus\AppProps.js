import { APP_ICON, APP_NAME, DATABASE_PREFIX } from "./constants/index";
import { modules } from "./modules";
import modulesProperties from "./modulesProperties";
import Dashboard from "./CustomViews/Dashboard";
import Reports from "./CustomViews/Reports";
import POS from "./CustomViews/Dashboard/POS";
import POSFloating from "./CustomViews/Dashboard/POSFloating";
import POSPanel from "./CustomViews/Dashboard/POSPanel";
import POSSettings from "./CustomViews/Settings/POSSettings";
import * as settings from "./modules/settings";
import { receiptsConfig } from "./moduleConfigs/receipts";

export default {
  APP_ICON,
  APP_NAME,
  DATABASE_PREFIX,
  modules,
  modulesProperties,
  AppReports: Reports,
  Dashboard,
  enforceBranches: true,
  // Choose one of the POS implementations:
  // 1. Standard POS button that opens a drawer
  POS: POS,
  // 2. Floating button that opens a full-screen POS
  // POS: POSFloating,
  // 3. Collapsible panel that slides in from the side
  // POS: POSPanel,
  settings: {
    ...settings.settings,
    posSettings: {
      name: "POS Settings",
      icon: "ShopOutlined",
      component: POSSettings,
    },
  },

  customModuleConfigs: {
    receipts: receiptsConfig,
  }
};
