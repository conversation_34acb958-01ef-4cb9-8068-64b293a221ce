import React, { useEffect, useState, useMemo } from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Descriptions,
  Table,
  Tag,
  Divider,
  Space,
  Button,
  Statistic,
  Avatar,
  Empty,
  Skeleton,
  Flex,
  Badge,
  Tooltip,
  Progress,
  Timeline
} from "antd";
import {
  AuditOutlined,
  ShoppingCartOutlined,
  CalendarOutlined,
  FileTextOutlined,
  PrinterOutlined,
  InfoCircleOutlined,
  DollarOutlined,
  PlusOutlined,
  MinusOutlined,
  UserOutlined,
  HistoryOutlined,
  BarChartOutlined,
  TagOutlined
} from "@ant-design/icons";
import { PageHeader } from "@ant-design/pro-components";
import moment from "moment";
import PouchDB from "pouchdb";
import { numberFormat } from "../../../../Utils/functions";
import PrintComponents from "react-print-components";
import "./styles.css";

const { Title, Text, Paragraph } = Typography;

const StockAdjustment = (props) => {
  const { data, singular } = props;
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState(null);

  // Fetch product data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products for items in adjustment
        if (data.items && data.items.length > 0) {
          const productsDB = new PouchDB("products");
          const productsResult = await productsDB.allDocs({ include_docs: true });
          const allProducts = productsResult.rows
            .filter(row => row.doc && !row.doc._id.startsWith('_'))
            .map(row => row.doc);
          setProducts(allProducts);
        }

        // Fetch company info for printing
        const companyDB = new PouchDB("company");
        const companyResult = await companyDB.allDocs({ include_docs: true });
        if (companyResult.rows.length > 0) {
          setCompany(companyResult.rows[0].doc);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [data.items]);

  // Prepare adjustment items with product details
  const adjustmentItems = useMemo(() => {
    if (!data.items || !products.length) return [];

    return data.items.map(item => {
      const product = products.find(p => p._id === item.product.value);
      return {
        ...item,
        productDetails: product || null
      };
    });
  }, [data.items, products]);

  // Calculate adjustment statistics
  const statistics = useMemo(() => {
    if (!adjustmentItems.length) return {
      totalItems: 0,
      totalValue: 0,
      positiveAdjustments: 0,
      negativeAdjustments: 0,
      positiveValue: 0,
      negativeValue: 0,
      categories: {}
    };

    let positiveAdjustments = 0;
    let negativeAdjustments = 0;
    let positiveValue = 0;
    let negativeValue = 0;
    const categories = {};

    adjustmentItems.forEach(item => {
      // Track by adjustment type
      const isAddition = item.add_subtract === true;
      const quantity = item.quantity || 0;
      const cost = item.productDetails ? item.productDetails.cost || 0 : 0;
      const value = cost * quantity;

      if (isAddition) {
        positiveAdjustments += quantity;
        positiveValue += value;
      } else {
        negativeAdjustments += quantity;
        negativeValue += value;
      }

      // Track by category
      if (item.productDetails && item.productDetails.category) {
        const categoryId = item.productDetails.category.value;
        const categoryName = item.productDetails.category.label;

        if (!categories[categoryId]) {
          categories[categoryId] = {
            name: categoryName,
            count: 0,
            value: 0
          };
        }

        categories[categoryId].count += quantity;
        categories[categoryId].value += value;
      }
    });

    const totalItems = positiveAdjustments + negativeAdjustments;
    const totalValue = positiveValue + negativeValue;

    return {
      totalItems,
      totalValue,
      positiveAdjustments,
      negativeAdjustments,
      positiveValue,
      negativeValue,
      categories
    };
  }, [adjustmentItems]);

  // Format date for display
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  // Printable adjustment component
  const PrintableAdjustment = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            {company.logo && <img src={company.logo} alt="Company Logo" style={{ maxHeight: 100 }} />}
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>STOCK ADJUSTMENT</Title>
            <Text>Adjustment #: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={5}>Adjustment Information</Title>
      <Descriptions bordered column={2}>
        <Descriptions.Item label="Adjustment ID">{data._id}</Descriptions.Item>
        <Descriptions.Item label="Date">{formatDate(data.date)}</Descriptions.Item>
        <Descriptions.Item label="Total Items">{statistics.totalItems}</Descriptions.Item>
        <Descriptions.Item label="Adjustment Type">
          {data.adjustment_type === 'add' ? 'Addition' : 'Subtraction'}
        </Descriptions.Item>
        {data.reason && (
          <Descriptions.Item label="Reason" span={2}>{data.reason}</Descriptions.Item>
        )}
      </Descriptions>

      <Divider />

      <Title level={5}>Adjusted Items</Title>
      <Table
        dataSource={adjustmentItems}
        pagination={false}
        size="small"
        columns={[
          {
            title: 'Product',
            dataIndex: 'product',
            key: 'product',
            render: product => product.label
          },
          {
            title: 'Adjustment Type',
            dataIndex: 'adjustment_type',
            key: 'adjustment_type',
            render: type => (
              <Tag color={type === 'add' ? 'success' : 'error'}>
                {type === 'add' ? 'Addition' : 'Subtraction'}
              </Tag>
            )
          },
          {
            title: 'Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            align: 'right'
          },
          {
            title: 'Unit Cost',
            key: 'cost',
            align: 'right',
            render: (_, record) => numberFormat(record.productDetails ? record.productDetails.cost || 0 : 0)
          },
          {
            title: 'Total Value',
            key: 'value',
            align: 'right',
            render: (_, record) => {
              const cost = record.productDetails ? record.productDetails.cost || 0 : 0;
              return numberFormat(cost * (record.quantity || 0));
            }
          }
        ]}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={4} index={0}>
                <Text strong>Total Value</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong>{numberFormat(statistics.totalValue)}</Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />

      <div style={{ textAlign: 'right', marginTop: 20 }}>
        <Text type="secondary">Generated on {moment().format('MMMM Do YYYY, h:mm:ss a')}</Text>
      </div>
    </div>
  );

  return (
    <>
      <PageHeader
        className="site-page-header-responsive"
        onBack={() => window.history.back()}
        title={`Adjustment #${data._id}`}
        subTitle={formatDate(data.date)}
        tags={[
          <Tag color="cyan" key="adjustment" className="status-tag">
            <AuditOutlined /> Stock Adjustment
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Adjustment
              </Button>
            }
          >
            <PrintableAdjustment />
          </PrintComponents>,
        ]}
      />

      <div style={{ padding: "0 24px 24px" }}>
        {loading ? (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card className="stock-card">
                <Skeleton active paragraph={{ rows: 4 }} />
              </Card>
              <Card className="stock-card" style={{ marginTop: 16 }}>
                <Skeleton active paragraph={{ rows: 4 }} />
              </Card>
            </Col>
            <Col xs={24} lg={16}>
              <Card className="stock-card">
                <Skeleton active paragraph={{ rows: 8 }} />
              </Card>
            </Col>
          </Row>
        ) : (
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              {/* Adjustment Information Card */}
              <Card
                title={<><InfoCircleOutlined /> Adjustment Information</>}
                className="stock-card"
                bordered={false}
              >
                <Descriptions column={1} size="small" bordered className="info-descriptions">
                  <Descriptions.Item label="Adjustment ID">
                    <Badge status="processing" text={data._id} />
                  </Descriptions.Item>
                  <Descriptions.Item label="Date">
                    <Space>
                      <CalendarOutlined />
                      {formatDate(data.date)}
                    </Space>
                  </Descriptions.Item>
                  {data.entrant && (
                    <Descriptions.Item label="Created By">
                      <Space>
                        <Avatar size="small" icon={<UserOutlined />} />
                        {data.entrant.label}
                      </Space>
                    </Descriptions.Item>
                  )}
                  <Descriptions.Item label="Created At">
                    <Space>
                      <HistoryOutlined />
                      {moment(data.createdAt).format('MMM DD, YYYY HH:mm')}
                    </Space>
                  </Descriptions.Item>
                </Descriptions>

                {data.description && (
                  <div className="description-section">
                    <Divider orientation="left">Description</Divider>
                    <Paragraph className="description-text">{data.description}</Paragraph>
                  </div>
                )}
              </Card>

              {/* Adjustment Summary Card */}
              <Card
                title={<><BarChartOutlined /> Adjustment Summary</>}
                className="stock-card summary-card"
                bordered={false}
              >
                <Statistic
                  title="Total Items"
                  value={statistics.totalItems}
                  prefix={<ShoppingCartOutlined />}
                  className="main-statistic"
                />

                <Divider style={{ margin: '16px 0' }} />

                <Row gutter={16} className="adjustment-stats">
                  <Col span={12}>
                    <Statistic
                      title="Additions"
                      value={statistics.positiveAdjustments}
                      prefix={<PlusOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                    <div className="stat-value">{numberFormat(statistics.positiveValue)}</div>
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="Subtractions"
                      value={statistics.negativeAdjustments}
                      prefix={<MinusOutlined />}
                      valueStyle={{ color: '#f5222d' }}
                    />
                    <div className="stat-value">{numberFormat(statistics.negativeValue)}</div>
                  </Col>
                </Row>

                <Divider style={{ margin: '16px 0' }} />

                <Statistic
                  title="Total Value"
                  value={numberFormat(statistics.totalValue)}
                  prefix={<DollarOutlined />}
                  className="total-value-stat"
                />

                {/* Distribution by adjustment type */}
                {statistics.totalItems > 0 && (
                  <div className="distribution-section">
                    <Divider orientation="left">Distribution</Divider>
                    <Tooltip title={`Additions: ${statistics.positiveAdjustments}, Subtractions: ${statistics.negativeAdjustments}`}>
                      <Progress
                        percent={Math.round((statistics.positiveAdjustments / statistics.totalItems) * 100)}
                        success={{ percent: 0 }}
                        type="dashboard"
                        format={() => (
                          <div className="progress-content">
                            <div><PlusOutlined style={{ color: '#52c41a' }} /></div>
                            <div>{Math.round((statistics.positiveAdjustments / statistics.totalItems) * 100)}%</div>
                          </div>
                        )}
                      />
                    </Tooltip>
                  </div>
                )}

                {/* Category breakdown if available */}
                {Object.keys(statistics.categories).length > 0 && (
                  <div className="categories-section">
                    <Divider orientation="left"><TagOutlined /> Categories</Divider>
                    <Timeline className="category-timeline">
                      {Object.values(statistics.categories).map((category, index) => (
                        <Timeline.Item key={index} color={index % 2 === 0 ? 'blue' : 'green'}>
                          <div className="category-item">
                            <div className="category-name">{category.name}</div>
                            <div className="category-stats">
                              <span>{category.count} items</span>
                              <span className="category-value">{numberFormat(category.value)}</span>
                            </div>
                          </div>
                        </Timeline.Item>
                      ))}
                    </Timeline>
                  </div>
                )}
              </Card>
            </Col>

            <Col xs={24} lg={16}>
              {/* Adjusted Items Card */}
              <Card
                title={<><ShoppingCartOutlined /> Adjusted Items</>}
                className="stock-card items-card"
                bordered={false}
              >
                {adjustmentItems.length > 0 ? (
                  <Table
                    dataSource={adjustmentItems}
                    rowKey={(record, index) => index}
                    pagination={false}
                    className="items-table"
                    columns={[
                      {
                        title: 'Product',
                        dataIndex: 'product',
                        key: 'product',
                        render: (product, record) => (
                          <Space className="product-cell">
                            <Avatar
                              shape="square"
                              size="small"
                              icon={<ShoppingCartOutlined />}
                              style={{ backgroundColor: '#1890ff' }}
                            />
                            <div>
                              <Tooltip title={`View product details: ${product.label}`}>
                                <a href={`#/inventory/products/${product.value}`} className="product-link">{product.label}</a>
                              </Tooltip>
                              {record.productDetails && record.productDetails.sku && (
                                <div className="product-sku">SKU: {record.productDetails.sku}</div>
                              )}
                            </div>
                          </Space>
                        )
                      },
                      {
                        title: 'Adjustment',
                        dataIndex: 'add_subtract',
                        key: 'add_subtract',
                        width: '15%',
                        render: value => (
                          <Tag color={value === true ? 'success' : 'error'} className="adjustment-tag">
                            {value === true ? (
                              <><PlusOutlined /> Addition</>
                            ) : (
                              <><MinusOutlined /> Subtraction</>
                            )}
                          </Tag>
                        )
                      },
                      {
                        title: 'Quantity',
                        dataIndex: 'quantity',
                        key: 'quantity',
                        align: 'right',
                        width: '15%',
                        render: (quantity, record) => (
                          <span className="quantity-cell">
                            {quantity}
                            {record.productDetails && record.productDetails.units && (
                              <span className="unit-text"> {record.productDetails.units}</span>
                            )}
                          </span>
                        )
                      },
                      {
                        title: 'Unit Cost',
                        key: 'cost',
                        align: 'right',
                        width: '15%',
                        render: (_, record) => (
                          <span className="cost-cell">
                            {numberFormat(record.productDetails ? record.productDetails.cost || 0 : 0)}
                          </span>
                        )
                      },
                      {
                        title: 'Total Value',
                        key: 'value',
                        align: 'right',
                        width: '15%',
                        render: (_, record) => {
                          const cost = record.productDetails ? record.productDetails.cost || 0 : 0;
                          const value = cost * (record.quantity || 0);
                          return (
                            <span className={`value-cell ${record.add_subtract ? 'positive-value' : 'negative-value'}`}>
                              {numberFormat(value)}
                            </span>
                          );
                        }
                      }
                    ]}
                    summary={() => (
                      <Table.Summary>
                        <Table.Summary.Row>
                          <Table.Summary.Cell colSpan={4} index={0}>
                            <Text strong>Total Value</Text>
                          </Table.Summary.Cell>
                          <Table.Summary.Cell index={1} align="right">
                            <Text strong className="summary-value">{numberFormat(statistics.totalValue)}</Text>
                          </Table.Summary.Cell>
                        </Table.Summary.Row>
                      </Table.Summary>
                    )}
                  />
                ) : (
                  <Empty
                    description="No items in this adjustment"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    className="empty-state"
                  />
                )}
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default StockAdjustment;
